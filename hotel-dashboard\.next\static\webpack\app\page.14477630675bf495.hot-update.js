"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/database-query/page.tsx":
/*!*************************************************!*\
  !*** ./app/(dashboard)/database-query/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseQueryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DatabaseQueryPage() {\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SELECT TOP 10 * FROM dbo.LOG_DATA;\");\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tables, setTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingTables, setIsLoadingTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 新增状态：结构化查询相关\n    const [selectedTable, setSelectedTable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [columns, setColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingColumns, setIsLoadingColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 分页相关状态\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalRows, setTotalRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [showPagination, setShowPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 导出进度相关状态\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exportProgress, setExportProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [exportTotal, setExportTotal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // CSV导出函数\n    const exportToCSV = (tableName, data)=>{\n        if (data.length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"无数据\",\n                description: \"没有数据可导出。\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const headers = Object.keys(data[0]);\n        // 创建CSV内容\n        const csvContent = [\n            // CSV头部\n            headers.join(','),\n            // CSV数据行\n            ...data.map((row)=>headers.map((header)=>{\n                    const value = String(row[header] || '');\n                    // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号\n                    if (value.includes(',') || value.includes('\"') || value.includes('\\n')) {\n                        return '\"'.concat(value.replace(/\"/g, '\"\"'), '\"');\n                    }\n                    return value;\n                }).join(','))\n        ].join('\\n');\n        // 创建Blob并下载\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat(tableName, \"_\").concat(new Date().toISOString().slice(0, 19).replace(/:/g, '-'), \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n            title: \"导出成功\",\n            description: \"\".concat(tableName, \" 已成功导出到CSV文件。\")\n        });\n    };\n    // 分批导出所有数据的函数\n    const exportAllDataToCSV = async (tableName)=>{\n        console.log('开始导出数据，表名:', tableName);\n        setIsExporting(true);\n        setExportProgress(0);\n        try {\n            // 1. 先获取总行数\n            const countQuery = buildStructuredQuery({\n                withCount: true\n            }) || \"SELECT COUNT(*) as total FROM \".concat(tableName, \";\");\n            console.log('获取导出总行数:', countQuery);\n            const countResponse = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: countQuery\n                })\n            });\n            const countData = await countResponse.json();\n            console.log('导出COUNT查询响应:', countData);\n            let totalCount = 0;\n            if (countResponse.ok && countData.data) {\n                console.log('导出COUNT查询数据结构:', countData.data);\n                // 使用与显示查询相同的解析逻辑\n                if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {\n                    // 处理 {tableName: [{total: 1500}]} 格式\n                    const tableNames = Object.keys(countData.data);\n                    if (tableNames.length > 0) {\n                        const firstTableData = countData.data[tableNames[0]];\n                        if (firstTableData && firstTableData.length > 0) {\n                            totalCount = parseInt(firstTableData[0].total) || 0;\n                        }\n                    }\n                } else if (Array.isArray(countData.data) && countData.data.length > 0) {\n                    // 处理直接数组格式\n                    totalCount = parseInt(countData.data[0].total) || 0;\n                }\n                console.log('导出解析的总行数:', totalCount);\n            } else {\n                console.error('导出COUNT查询失败:', countData);\n            }\n            if (totalCount === 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"无数据\",\n                    description: \"没有数据可导出。\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setExportTotal(totalCount);\n            // 2. 分批查询数据\n            const batchSize = 100;\n            const totalBatches = Math.ceil(totalCount / batchSize);\n            let allData = [];\n            let headers = [];\n            for(let batch = 0; batch < totalBatches; batch++){\n                const offset = batch * batchSize;\n                const batchQuery = buildStructuredQuery({\n                    page: batch + 1\n                }) || \"SELECT * FROM \".concat(tableName, \" LIMIT \").concat(batchSize, \" OFFSET \").concat(offset, \";\");\n                console.log(\"导出批次 \".concat(batch + 1, \"/\").concat(totalBatches, \":\"), batchQuery);\n                const batchResponse = await fetch('/api/database-query', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        query: batchQuery\n                    })\n                });\n                const batchData = await batchResponse.json();\n                if (batchResponse.ok && batchData.data) {\n                    // 假设数据结构是 { tableName: [...] }\n                    const tableData = Object.values(batchData.data)[0];\n                    if (tableData && Array.isArray(tableData)) {\n                        if (batch === 0) {\n                            headers = Object.keys(tableData[0] || {});\n                        }\n                        allData = allData.concat(tableData);\n                    }\n                }\n                // 更新进度\n                setExportProgress(batch + 1);\n                // 给UI一点时间更新\n                await new Promise((resolve)=>setTimeout(resolve, 10));\n            }\n            // 3. 生成CSV并下载\n            if (allData.length > 0) {\n                exportToCSV(tableName, allData);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"导出完成\",\n                    description: \"成功导出 \".concat(allData.length, \" 条记录\")\n                });\n            } else {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"导出失败\",\n                    description: \"未能获取到数据\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (err) {\n            console.error('导出错误:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"导出失败\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsExporting(false);\n            setExportProgress(0);\n            setExportTotal(0);\n        }\n    };\n    // 获取数据库表列表\n    const fetchTables = async ()=>{\n        setIsLoadingTables(true);\n        try {\n            // 尝试多种不同的查询方式\n            const queries = [\n                \"USE gina_db; SHOW TABLES;\",\n                \"SHOW TABLES FROM gina_db;\",\n                \"SELECT name FROM gina_db.sys.tables ORDER BY name;\",\n                \"SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;\"\n            ];\n            for(let i = 0; i < queries.length; i++){\n                try {\n                    console.log(\"Trying query \".concat(i + 1, \": \").concat(queries[i]));\n                    const response = await fetch('/api/database-query', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            query: queries[i]\n                        })\n                    });\n                    const response_data = await response.json();\n                    if (response.ok) {\n                        console.log('Query succeeded:', response_data);\n                        // 提取表名列表\n                        const tableData = response_data.data;\n                        if (tableData && Object.keys(tableData).length > 0) {\n                            const firstKey = Object.keys(tableData)[0];\n                            const tableRows = tableData[firstKey];\n                            if (Array.isArray(tableRows) && tableRows.length > 0) {\n                                // 尝试不同的列名\n                                const possibleColumns = [\n                                    'TABLE_NAME',\n                                    'name',\n                                    'Tables_in_gina_db'\n                                ];\n                                let tableNames = [];\n                                // 首先尝试已知的列名\n                                for (const colName of possibleColumns){\n                                    if (tableRows[0].hasOwnProperty(colName)) {\n                                        tableNames = tableRows.map((row)=>String(row[colName])).filter(Boolean);\n                                        break;\n                                    }\n                                }\n                                // 如果没有找到已知列名，使用第一列\n                                if (tableNames.length === 0) {\n                                    tableNames = tableRows.map((row)=>{\n                                        const values = Object.values(row);\n                                        return values.length > 0 ? String(values[0]) : null;\n                                    }).filter((name)=>Boolean(name));\n                                }\n                                if (tableNames.length > 0) {\n                                    setTables(tableNames);\n                                    console.log('Found tables:', tableNames);\n                                    return; // 成功获取表列表，退出函数\n                                }\n                            }\n                        }\n                    } else {\n                        console.log(\"Query \".concat(i + 1, \" failed:\"), response_data.error);\n                    }\n                } catch (err) {\n                    console.log(\"Query \".concat(i + 1, \" error:\"), err);\n                    continue; // 尝试下一个查询\n                }\n            }\n            // 所有查询都失败了\n            throw new Error('所有表列表查询都失败了。请检查数据库连接和权限。');\n        } catch (err) {\n            console.error('Error fetching tables:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: \"获取表列表失败: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingTables(false);\n        }\n    };\n    // 获取表的列信息\n    const fetchColumns = async (tableName)=>{\n        if (!tableName) return;\n        setIsLoadingColumns(true);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: \"SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '\".concat(tableName, \"' ORDER BY ORDINAL_POSITION;\")\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                const tableData = response_data.data;\n                if (tableData && Object.keys(tableData).length > 0) {\n                    const firstKey = Object.keys(tableData)[0];\n                    const columnRows = tableData[firstKey];\n                    if (Array.isArray(columnRows)) {\n                        const columnInfo = columnRows.map((row)=>{\n                            const columnName = row.COLUMN_NAME || row.column_name || '';\n                            const dataType = (row.DATA_TYPE || row.data_type || '').toLowerCase();\n                            // 根据数据类型判断字段类型\n                            let fieldType = 'string';\n                            if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('numeric') || dataType.includes('double') || dataType.includes('real') || dataType.includes('money') || dataType.includes('smallmoney')) {\n                                fieldType = 'number';\n                            } else if (dataType.includes('date') || dataType.includes('time') || dataType.includes('timestamp')) {\n                                fieldType = 'date';\n                            } else if (dataType.includes('bit') || dataType.includes('boolean')) {\n                                fieldType = 'boolean';\n                            }\n                            return {\n                                name: columnName,\n                                type: dataType,\n                                dataType: fieldType\n                            };\n                        });\n                        // 去重处理，以防有重复的列名\n                        const uniqueColumns = columnInfo.filter((col, index, self)=>index === self.findIndex((c)=>c.name === col.name));\n                        setColumns(uniqueColumns);\n                        console.log('Found columns:', uniqueColumns);\n                    }\n                }\n            } else {\n                throw new Error(response_data.error || '获取列信息失败。');\n            }\n        } catch (err) {\n            console.error('Error fetching columns:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: \"获取列信息失败: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingColumns(false);\n        }\n    };\n    // 页面加载时获取表列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DatabaseQueryPage.useEffect\": ()=>{\n            fetchTables();\n        }\n    }[\"DatabaseQueryPage.useEffect\"], []);\n    // 处理表选择\n    const handleTableSelect = (tableName)=>{\n        setSelectedTable(tableName);\n        setQuery(\"SELECT * FROM \".concat(tableName, \" LIMIT 100;\"));\n        setFilters([]); // 清空之前的筛选条件\n        // 重置分页状态\n        setCurrentPage(1);\n        setTotalRows(0);\n        setShowPagination(false);\n        setResult(null);\n        setError(null);\n        fetchColumns(tableName); // 获取列信息\n    };\n    // 构建结构化查询\n    const buildStructuredQuery = function() {\n        let { withCount = false, page = 1 } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        if (!selectedTable) return \"\";\n        let whereClause = \"\";\n        const activeFilters = filters.filter((f)=>f.enabled && f.value !== \"\" && f.value !== null);\n        if (activeFilters.length > 0) {\n            const conditions = activeFilters.map((filter)=>{\n                const column = filter.column;\n                const value = filter.value;\n                switch(filter.operator){\n                    case 'equals':\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    case 'contains':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"%'\");\n                    case 'starts_with':\n                        return \"\".concat(column, \" LIKE '\").concat(value, \"%'\");\n                    case 'ends_with':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"'\");\n                    case 'regex':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"%'\"); // 使用LIKE代替REGEXP以提高兼容性\n                    case 'greater_than':\n                        return \"\".concat(column, \" > \").concat(value);\n                    case 'less_than':\n                        return \"\".concat(column, \" < \").concat(value);\n                    case 'between':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN \").concat(value[0], \" AND \").concat(value[1]);\n                        }\n                        return \"\".concat(column, \" = \").concat(value);\n                    case 'date_range':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN '\").concat(value[0], \"' AND '\").concat(value[1], \"'\");\n                        }\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    default:\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                }\n            });\n            whereClause = \" WHERE \" + conditions.join(\" AND \");\n        }\n        if (withCount) {\n            return \"SELECT COUNT(*) as total FROM \".concat(selectedTable).concat(whereClause, \";\");\n        }\n        const offset = (page - 1) * pageSize;\n        return \"SELECT * FROM \".concat(selectedTable).concat(whereClause, \" LIMIT \").concat(pageSize, \" OFFSET \").concat(offset, \";\");\n    };\n    // 执行结构化查询\n    const handleStructuredQuery = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        const structuredQuery = buildStructuredQuery({\n            page\n        });\n        setQuery(structuredQuery);\n        // 直接执行查询，不依赖状态更新\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            // 首先执行COUNT查询获取总行数\n            const countQuery = buildStructuredQuery({\n                withCount: true\n            });\n            console.log('执行COUNT查询:', countQuery);\n            const countResponse = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: countQuery\n                })\n            });\n            const countData = await countResponse.json();\n            console.log('COUNT查询响应:', countData);\n            let totalCount = 0;\n            if (countResponse.ok && countData.data) {\n                console.log('COUNT查询数据结构:', countData.data);\n                // 检查数据结构，可能是 { tableName: [...] } 格式\n                if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {\n                    // 如果是对象格式，取第一个表的数据\n                    const firstTableData = Object.values(countData.data)[0];\n                    if (firstTableData && firstTableData.length > 0) {\n                        totalCount = parseInt(firstTableData[0].total) || 0;\n                    }\n                } else if (Array.isArray(countData.data) && countData.data.length > 0) {\n                    // 如果是数组格式\n                    totalCount = parseInt(countData.data[0].total) || 0;\n                }\n                console.log('解析的总行数:', totalCount);\n            }\n            setTotalRows(totalCount);\n            setShowPagination(true); // 始终显示分页信息，包括总数\n            // 然后执行数据查询\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: structuredQuery\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                setResult(response_data.data);\n                setCurrentPage(page);\n            } else {\n                throw new Error(response_data.error || '查询失败。');\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleQuery = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query\n                })\n            });\n            const response_data = await response.json();\n            if (!response.ok) {\n                throw new Error(response_data.error || '发生未知错误。');\n            }\n            // Extract the actual data from the new API response format\n            const actualData = response_data.data || {};\n            // Check if the result is an empty object, which is a valid success case\n            if (Object.keys(actualData).length === 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"Query Successful\",\n                    description: \"The query ran successfully but returned no data.\"\n                });\n                setResult({});\n            } else {\n                setResult(actualData);\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 添加筛选条件\n    const addFilter = ()=>{\n        if (columns.length === 0) return;\n        const newFilter = {\n            column: columns[0].name,\n            operator: 'equals',\n            value: '',\n            enabled: true\n        };\n        setFilters([\n            ...filters,\n            newFilter\n        ]);\n    };\n    // 更新筛选条件\n    const updateFilter = (index, updates)=>{\n        const newFilters = [\n            ...filters\n        ];\n        newFilters[index] = {\n            ...newFilters[index],\n            ...updates\n        };\n        setFilters(newFilters);\n    };\n    // 删除筛选条件\n    const removeFilter = (index)=>{\n        const newFilters = filters.filter((_, i)=>i !== index);\n        setFilters(newFilters);\n    };\n    const renderTable = (tableName, data)=>{\n        if (data.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"表 '\",\n                    tableName,\n                    \"' 没有数据行。\"\n                ]\n            }, tableName, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 625,\n                columnNumber: 14\n            }, this);\n        }\n        const headers = Object.keys(data[0]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    tableName,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 font-normal\",\n                                        children: [\n                                            \"(\",\n                                            data.length,\n                                            \" rows)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>exportAllDataToCSV(tableName),\n                                disabled: isExporting,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 15\n                                    }, this),\n                                    isExporting ? \"导出中...\" : \"导出全部数据\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 630,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0\",\n                    children: [\n                        isExporting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"导出进度\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                exportProgress,\n                                                \" / \",\n                                                Math.ceil(exportTotal / 100),\n                                                \" 批次\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: \"\".concat(exportTotal > 0 ? exportProgress / Math.ceil(exportTotal / 100) * 100 : 0, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: [\n                                        \"正在导出 \",\n                                        exportTotal,\n                                        \" 条记录...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto max-h-[600px] border rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHeader, {\n                                        className: \"sticky top-0 bg-white z-10 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                            children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2\",\n                                                    style: {\n                                                        minWidth: '120px'\n                                                    },\n                                                    children: header\n                                                }, header, false, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableBody, {\n                                        children: data.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                                className: \"hover:bg-gray-50\",\n                                                children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"whitespace-nowrap px-4 py-2 text-sm border-b\",\n                                                        style: {\n                                                            minWidth: '120px'\n                                                        },\n                                                        title: String(row[header]),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-w-[200px] truncate\",\n                                                            children: String(row[header])\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, \"\".concat(rowIndex, \"-\").concat(header), false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, rowIndex, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t\",\n                            children: showPagination ? \"显示第 \".concat((currentPage - 1) * pageSize + 1, \"-\").concat(Math.min(currentPage * pageSize, totalRows), \" 条，总计 \").concat(totalRows, \" 条记录\") : \"总计: \".concat(data.length, \" 条记录\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 710,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, tableName, true, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n            lineNumber: 629,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"数据库查询\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"快速表选择 (gina_db)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                onValueChange: handleTableSelect,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                        className: \"w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                            placeholder: isLoadingTables ? \"加载表中...\" : \"选择要查询的表\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                        children: tables.map((tableName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                value: tableName,\n                                                                children: tableName\n                                                            }, tableName, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: fetchTables,\n                                                disabled: isLoadingTables,\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(isLoadingTables ? 'animate-spin' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"刷新\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 13\n                                    }, this),\n                                    tables.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"在 gina_db 数据库中找到 \",\n                                            tables.length,\n                                            \" 个表\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 11\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedTable,\n                                                    \" 的筛选条件\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: addFilter,\n                                                disabled: isLoadingColumns || columns.length === 0,\n                                                children: \"添加筛选\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 15\n                                    }, this),\n                                    isLoadingColumns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 mb-2\",\n                                        children: \"加载列中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 17\n                                    }, this),\n                                    filters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-4\",\n                                        children: filters.map((filter, index)=>{\n                                            var _columns_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                        checked: filter.enabled,\n                                                        onCheckedChange: (enabled)=>updateFilter(index, {\n                                                                enabled\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.column,\n                                                        onValueChange: (column)=>updateFilter(index, {\n                                                                column\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-40\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 801,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: columns.map((col, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: col.name,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                col.dataType === 'string' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 808,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 809,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'date' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 810,\n                                                                                    columnNumber: 61\n                                                                                }, this),\n                                                                                col.name,\n                                                                                \" (\",\n                                                                                col.type,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, \"\".concat(col.name, \"-\").concat(colIndex), false, {\n                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                        lineNumber: 806,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.operator,\n                                                        onValueChange: (operator)=>updateFilter(index, {\n                                                                operator\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: (()=>{\n                                                                    const selectedColumn = columns.find((col)=>col.name === filter.column);\n                                                                    if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'string') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"Equals\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 830,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"contains\",\n                                                                                children: \"Contains\"\n                                                                            }, \"contains\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 831,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"starts_with\",\n                                                                                children: \"Starts with\"\n                                                                            }, \"starts_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 832,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"ends_with\",\n                                                                                children: \"Ends with\"\n                                                                            }, \"ends_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 833,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"regex\",\n                                                                                children: \"Regex\"\n                                                                            }, \"regex\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 834,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'number') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"Equals\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 838,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"greater_than\",\n                                                                                children: \"Greater than\"\n                                                                            }, \"greater_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 839,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"less_than\",\n                                                                                children: \"Less than\"\n                                                                            }, \"less_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 840,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"between\",\n                                                                                children: \"Between\"\n                                                                            }, \"between\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 841,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'date') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"Equals\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 845,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"date_range\",\n                                                                                children: \"Date range\"\n                                                                            }, \"date_range\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    }\n                                                                    return [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: \"equals\",\n                                                                            children: \"Equals\"\n                                                                        }, \"equals\", false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 849,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ];\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 825,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    filter.operator === 'between' || filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 864,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[0] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[0]), \"yyyy-MM-dd\") : \"From date\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 860,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 859,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[0] ? new Date(filter.value[0]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            dateStr,\n                                                                                            currentValue[1]\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 872,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"to\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 891,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[1] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[1]), \"yyyy-MM-dd\") : \"To date\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 887,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 886,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[1] ? new Date(filter.value[1]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            currentValue[0],\n                                                                                            dateStr\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 899,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 898,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 885,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"From\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[0] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                e.target.value,\n                                                                                currentValue[1]\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 914,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"to\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 924,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"To\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[1] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                currentValue[0],\n                                                                                e.target.value\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        type: ((_columns_find = columns.find((col)=>col.name === filter.column)) === null || _columns_find === void 0 ? void 0 : _columns_find.dataType) === 'number' ? 'number' : 'text',\n                                                        placeholder: \"Value\",\n                                                        className: \"flex-1\",\n                                                        value: Array.isArray(filter.value) ? '' : filter.value,\n                                                        onChange: (e)=>updateFilter(index, {\n                                                                value: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeFilter(index),\n                                                        children: \"Remove\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 789,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: ()=>{\n                                                    // 重置到第一页并执行查询\n                                                    setCurrentPage(1);\n                                                    handleStructuredQuery(1);\n                                                },\n                                                disabled: isLoading,\n                                                className: \"flex-1\",\n                                                children: isLoading ? \"查询中...\" : \"使用筛选查询\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 961,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                onClick: async ()=>{\n                                                    // 显示前100行数据（重置筛选条件），但先获取总数并启用分页\n                                                    setFilters([]);\n                                                    setCurrentPage(1);\n                                                    setIsLoading(true);\n                                                    setError(null);\n                                                    setResult(null);\n                                                    try {\n                                                        // 先获取总行数\n                                                        const countQuery = \"SELECT COUNT(*) as total FROM \".concat(selectedTable, \";\");\n                                                        console.log('获取总行数:', countQuery);\n                                                        const countResponse = await fetch('/api/database-query', {\n                                                            method: 'POST',\n                                                            headers: {\n                                                                'Content-Type': 'application/json'\n                                                            },\n                                                            body: JSON.stringify({\n                                                                query: countQuery\n                                                            })\n                                                        });\n                                                        const countData = await countResponse.json();\n                                                        console.log('COUNT查询响应:', countData);\n                                                        console.log('COUNT查询响应状态:', countResponse.ok);\n                                                        let totalCount = 0;\n                                                        if (countResponse.ok && countData.data) {\n                                                            console.log('COUNT查询数据结构:', countData.data);\n                                                            console.log('数据类型:', typeof countData.data);\n                                                            console.log('是否为数组:', Array.isArray(countData.data));\n                                                            // 尝试多种数据结构解析\n                                                            try {\n                                                                // 方法1: 检查是否是 { tableName: [...] } 格式\n                                                                if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {\n                                                                    console.log('尝试对象格式解析...');\n                                                                    const tableNames = Object.keys(countData.data);\n                                                                    console.log('表名:', tableNames);\n                                                                    if (tableNames.length > 0) {\n                                                                        const firstTableData = countData.data[tableNames[0]];\n                                                                        console.log('第一个表的数据:', firstTableData);\n                                                                        if (firstTableData && firstTableData.length > 0) {\n                                                                            console.log('第一行数据:', firstTableData[0]);\n                                                                            totalCount = parseInt(firstTableData[0].total) || 0;\n                                                                        }\n                                                                    }\n                                                                } else if (Array.isArray(countData.data) && countData.data.length > 0) {\n                                                                    console.log('尝试数组格式解析...');\n                                                                    console.log('第一行数据:', countData.data[0]);\n                                                                    totalCount = parseInt(countData.data[0].total) || 0;\n                                                                }\n                                                                console.log('解析的总行数:', totalCount);\n                                                            } catch (parseError) {\n                                                                console.error('解析COUNT数据时出错:', parseError);\n                                                            }\n                                                        } else {\n                                                            console.error('COUNT查询失败:', countData);\n                                                        }\n                                                        setTotalRows(totalCount);\n                                                        setShowPagination(true); // 始终显示分页信息，包括总数\n                                                        // 然后查询前100行\n                                                        const dataQuery = \"SELECT * FROM \".concat(selectedTable, \" LIMIT 100 OFFSET 0;\");\n                                                        setQuery(dataQuery);\n                                                        const dataResponse = await fetch('/api/database-query', {\n                                                            method: 'POST',\n                                                            headers: {\n                                                                'Content-Type': 'application/json'\n                                                            },\n                                                            body: JSON.stringify({\n                                                                query: dataQuery\n                                                            })\n                                                        });\n                                                        const dataResult = await dataResponse.json();\n                                                        if (dataResponse.ok) {\n                                                            setResult(dataResult.data);\n                                                            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                                                                title: \"显示全部数据\",\n                                                                description: \"显示前100行，总计 \".concat(totalCount, \" 条记录\")\n                                                            });\n                                                        } else {\n                                                            throw new Error(dataResult.error || '查询失败。');\n                                                        }\n                                                    } catch (err) {\n                                                        setError(err.message);\n                                                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                                                            title: \"错误\",\n                                                            description: err.message,\n                                                            variant: \"destructive\"\n                                                        });\n                                                    } finally{\n                                                        setIsLoading(false);\n                                                    }\n                                                },\n                                                disabled: isLoading || !selectedTable,\n                                                size: \"sm\",\n                                                children: \"显示全部 (100行)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 960,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 13\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        className: \"text-xs font-medium text-gray-600 mb-1 block\",\n                                        children: \"生成的查询语句:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 1081,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"text-xs text-gray-800 font-mono\",\n                                        children: buildStructuredQuery({\n                                            page: currentPage\n                                        }) || \"SELECT * FROM \".concat(selectedTable, \" LIMIT \").concat(pageSize, \" OFFSET 0;\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 1084,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1080,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 728,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 724,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mt-4 bg-destructive/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-destructive\",\n                            children: \"错误\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 1095,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1094,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 1098,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1097,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 1093,\n                columnNumber: 9\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold\",\n                                children: \"查询结果\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1106,\n                                columnNumber: 13\n                            }, this),\n                            showPagination && totalRows > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"总计 \",\n                                    totalRows,\n                                    \" 条记录，第 \",\n                                    currentPage,\n                                    \" 页，共 \",\n                                    Math.ceil(totalRows / pageSize),\n                                    \" 页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1108,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1105,\n                        columnNumber: 11\n                    }, this),\n                    Object.keys(result).length > 0 ? Object.entries(result).map((param)=>{\n                        let [tableName, data] = param;\n                        return renderTable(tableName, data);\n                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"查询执行成功，但没有返回数据。\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1117,\n                        columnNumber: 13\n                    }, this),\n                    showPagination && totalRows > pageSize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>handleStructuredQuery(currentPage - 1),\n                                disabled: currentPage <= 1 || isLoading,\n                                children: \"上一页\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1123,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: Array.from({\n                                    length: Math.min(5, Math.ceil(totalRows / pageSize))\n                                }, (_, i)=>{\n                                    const pageNum = Math.max(1, currentPage - 2) + i;\n                                    if (pageNum > Math.ceil(totalRows / pageSize)) return null;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: pageNum === currentPage ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleStructuredQuery(pageNum),\n                                        disabled: isLoading,\n                                        children: pageNum\n                                    }, pageNum, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1132,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>handleStructuredQuery(currentPage + 1),\n                                disabled: currentPage >= Math.ceil(totalRows / pageSize) || isLoading,\n                                children: \"下一页\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1151,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1122,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 1104,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n        lineNumber: 723,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseQueryPage, \"WM7d+N+P+Ku0iGLIs3hjlOjWIUM=\");\n_c = DatabaseQueryPage;\nvar _c;\n$RefreshReg$(_c, \"DatabaseQueryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/database-query/page.tsx\n"));

/***/ })

});