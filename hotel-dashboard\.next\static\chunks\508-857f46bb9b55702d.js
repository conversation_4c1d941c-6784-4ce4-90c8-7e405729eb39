"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[508],{4508:(e,t,a)=>{a.r(t),a.d(t,{default:()=>E});var s=a(95155),r=a(12115),l=a(99474),n=a(97168),i=a(82714),o=a(90088),d=a(51154);function c(e){let{onSearch:t,isLoading:a}=e,[c,m]=(0,r.useState)(""),[h,f]=(0,r.useState)(!0);return(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(c,h)},className:"p-4 border rounded-lg space-y-4 bg-card text-card-foreground",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"searchTerms",children:"搜索条件 (每行一个SN)"}),(0,s.jsx)(l.T,{id:"searchTerms",value:c,onChange:e=>m(e.target.value),placeholder:"例如: GDDX2.*202506.*",rows:5,disabled:a})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 pt-2",children:[(0,s.jsx)(o.d,{id:"useRegex",checked:h,onCheckedChange:f,disabled:a}),(0,s.jsx)(i.J,{htmlFor:"useRegex",children:"使用正则表达式"})]}),(0,s.jsx)(n.$,{type:"submit",disabled:a,className:"w-full md:w-auto",children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),"搜索中..."]}):"搜索"})]})}var m=a(95139),h=a(88524),f=a(47655),u=a(53999);let x=r.forwardRef((e,t)=>{let{className:a,children:r,...l}=e;return(0,s.jsxs)(f.bL,{ref:t,className:(0,u.cn)("relative overflow-hidden",a),...l,children:[(0,s.jsx)(f.LM,{className:"h-full w-full rounded-[inherit]",children:r}),(0,s.jsx)(p,{}),(0,s.jsx)(f.OK,{})]})});x.displayName=f.bL.displayName;let p=r.forwardRef((e,t)=>{let{className:a,orientation:r="vertical",...l}=e;return(0,s.jsx)(f.VM,{ref:t,orientation:r,className:(0,u.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",a),...l,children:(0,s.jsx)(f.lr,{className:"relative flex-1 rounded-full bg-border"})})});p.displayName=f.VM.displayName;var j=a(92657),g=a(99840),b=a(1243),w=a(54481),N=a(6262),v=a(40133),y=a(27472);let k=e=>{let{isOpen:t,onClose:a,surfaceFile:l}=e,[i,o]=(0,r.useState)(!1),[c,m]=(0,r.useState)(null),[h,f]=(0,r.useState)(null),u=(0,r.useRef)(null);(0,r.useEffect)(()=>{t&&l?x():(f(null),m(null),o(!1),u.current&&u.current.resetTransform())},[t,l]);let x=async()=>{if(l){o(!0),m(null),f(null),u.current&&u.current.resetTransform();try{let e=await fetch("/api/surface-data/preview",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filePath:l.fullPath})});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error((null==t?void 0:t.error)||"Error fetching preview: ".concat(e.statusText))}let t=await e.json();if(!t.success)throw Error(t.error||"An unknown error occurred in the API.");if(!t.imageData)throw Error("No image data received from API.");f(t.imageData)}catch(e){console.error("Error fetching or processing preview data:",e),m(e instanceof Error?e.message:"An unknown error occurred.")}finally{o(!1)}}};return(0,s.jsx)(g.lG,{open:t,onOpenChange:a,children:(0,s.jsxs)(g.Cf,{className:"max-w-4xl h-[700px] flex flex-col",children:[(0,s.jsxs)(g.c7,{children:[(0,s.jsxs)(g.L3,{children:["Point Cloud Preview: ",(null==l?void 0:l.name)||"Loading..."]}),(0,s.jsx)(g.HM,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"ghost",size:"icon",className:"absolute top-4 right-4",children:"\xd7"})})]}),(0,s.jsxs)("div",{className:"flex-grow flex items-center justify-center relative border rounded-md overflow-hidden bg-slate-100",children:[i&&(0,s.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-center bg-background/80 z-20",children:[(0,s.jsx)(d.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"mt-2 text-muted-foreground",children:"Loading preview data..."})]}),c&&!i&&(0,s.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-center bg-destructive/10 p-4 z-20",children:[(0,s.jsx)(b.A,{className:"h-12 w-12 text-destructive"}),(0,s.jsx)("p",{className:"mt-2 text-destructive-foreground font-semibold",children:"Error loading preview"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-destructive-foreground/80 text-center",children:c}),(0,s.jsx)(n.$,{onClick:x,className:"mt-4",children:"Try Again"})]}),!i&&!c&&h&&(0,s.jsx)(y.GT,{ref:u,initialScale:1,minScale:.5,maxScale:10,centerOnInit:!0,limitToBounds:!0,doubleClick:{mode:"reset"},children:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"absolute top-2 right-2 z-10 flex gap-1",children:[(0,s.jsx)(n.$,{variant:"outline",size:"icon",onClick:()=>e.zoomIn(),children:(0,s.jsx)(w.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"icon",onClick:()=>e.zoomOut(),children:(0,s.jsx)(N.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"icon",onClick:()=>e.resetTransform(),children:(0,s.jsx)(v.A,{className:"h-4 w-4"})})]}),(0,s.jsx)(y.WZ,{wrapperStyle:{width:"100%",height:"100%"},contentStyle:{width:"100%",height:"100%"},children:(0,s.jsx)("img",{src:h,alt:"Preview of ".concat((null==l?void 0:l.name)||"point cloud"),className:"w-full h-full object-contain"})})]})}),!i&&!c&&!h&&(0,s.jsx)("div",{className:"text-muted-foreground",children:"No preview available or file not selected."})]})]})})};var S=a(89613);let C=S.Kq,R=S.bL,A=S.l9,T=r.forwardRef((e,t)=>{let{className:a,sideOffset:r=4,...l}=e;return(0,s.jsx)(S.UC,{ref:t,sideOffset:r,className:(0,u.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})});function z(e){let{results:t,isLoading:a,selectedFiles:l,setSelectedFiles:i,onDownloadSelected:o}=e,[c,f]=(0,r.useState)(null),[u,p]=(0,r.useState)(!1),g=e=>{f(e),p(!0)},b=(e,t)=>{t?i(t=>[...t,e]):i(t=>t.filter(t=>t.path!==e.path))},w=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][t]},N=e=>e?new Date(1e3*e).toLocaleString():"-";return a&&0===t.length?(0,s.jsxs)("div",{className:"flex justify-center items-center h-40",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-2",children:"正在加载搜索结果..."})]}):a||0!==t.length?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold",children:["搜索结果 (",t.length," 条)"]}),(0,s.jsx)(n.$,{onClick:o,disabled:0===l.length||a,children:a&&l.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),"下载中..."]}):"下载选中 (".concat(l.length,")")})]}),(0,s.jsx)(x,{className:"h-[400px] rounded-md border",children:(0,s.jsxs)(h.XI,{children:[(0,s.jsx)(h.A0,{children:(0,s.jsxs)(h.Hj,{children:[(0,s.jsx)(h.nd,{className:"w-[50px]",children:(0,s.jsx)(m.S,{checked:t.length>0&&l.length===t.length,onCheckedChange:e=>{e?i(t):i([])},disabled:0===t.length||a})}),(0,s.jsx)(h.nd,{className:"w-[40%]",children:"文件名"}),(0,s.jsx)(h.nd,{className:"w-[20%]",children:"大小"}),(0,s.jsx)(h.nd,{className:"w-[25%]",children:"修改时间"}),(0,s.jsx)(h.nd,{className:"w-[15%] text-center",children:"预览"})]})}),(0,s.jsx)(h.BF,{children:t.map(e=>(0,s.jsxs)(h.Hj,{children:[(0,s.jsx)(h.nA,{children:(0,s.jsx)(m.S,{checked:l.some(t=>t.path===e.path),onCheckedChange:t=>b(e,!!t),disabled:a})}),(0,s.jsx)(h.nA,{className:"font-medium truncate max-w-xs",title:e.name,children:e.name}),(0,s.jsx)(h.nA,{children:w(e.size)}),(0,s.jsx)(h.nA,{children:N(e.modifyTime)}),(0,s.jsx)(h.nA,{className:"text-center",children:(0,s.jsx)(C,{children:(0,s.jsxs)(R,{children:[(0,s.jsx)(A,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>g(e),"aria-label":"预览 ".concat(e.name),disabled:a,children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})}),(0,s.jsx)(T,{children:(0,s.jsx)("p",{children:"预览文件"})})]})})})]},e.path))})]})}),c&&(0,s.jsx)(k,{isOpen:u,onClose:()=>{p(!1),f(null)},surfaceFile:c})]}):(0,s.jsx)("div",{className:"text-center p-4",children:"没有搜索结果。"})}T.displayName=S.UC.displayName;var L=a(14503);function E(){let[e,t]=(0,r.useState)([]),[a,l]=(0,r.useState)(!1),[n,i]=(0,r.useState)(null),[o,d]=(0,r.useState)([]),m=async(e,a)=>{l(!0),i(null),t([]),d([]);try{let s;let r=e.split("\n").map(e=>e.trim()).filter(e=>e.length>0);if(0===r.length){(0,L.oR)({title:"搜索条件不足",description:"请输入至少一个搜索条件。",variant:"destructive"}),l(!1);return}s=a?r.join("|"):r.map(e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")).join("|");let n=new URLSearchParams;n.append("regex",s);let i=await fetch("/api/ftp/search?".concat(n.toString()));if(!i.ok){let e=await i.json();throw Error(e.error||"HTTP error! status: ".concat(i.status))}let o=await i.json();t(o.files),0===o.files.length?(0,L.oR)({title:"搜索结果",description:"未找到匹配的文件。"}):o.files.length>=o.limit&&(0,L.oR)({title:"搜索结果提示",description:"搜索结果已达到".concat(o.limit,"条上限，可能还有更多结果未显示。请优化搜索条件。"),variant:"default"})}catch(t){let e=t instanceof Error?t.message:"搜索过程中发生未知错误";i(e),(0,L.oR)({title:"搜索错误",description:e,variant:"destructive"})}finally{l(!1)}},h=async()=>{if(0===o.length){(0,L.oR)({title:"下载提示",description:"请至少选择一个文件进行下载。",variant:"default"});return}l(!0),i(null);try{let e=o.map(e=>e.fullPath),t=await fetch("/api/ftp/download",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filePaths:e})});if(!t.ok){let e=await t.json();throw Error(e.error||"HTTP error! status: ".concat(t.status))}let a=await t.blob(),s=window.URL.createObjectURL(a),r=document.createElement("a");r.href=s;let l=new Date().toISOString().replace(/[:.]/g,"-");r.download=o.length>1?"surface_data_".concat(l,".zip"):o[0].name,document.body.appendChild(r),r.click(),r.remove(),window.URL.revokeObjectURL(s),(0,L.oR)({title:"下载成功",description:"".concat(o.length," 个文件已开始下载。")}),d([])}catch(t){let e=t instanceof Error?t.message:"下载过程中发生未知错误";i(e),(0,L.oR)({title:"下载错误",description:e,variant:"destructive"})}finally{l(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto p-4 space-y-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"面形数据查询"}),(0,s.jsx)(c,{onSearch:m,isLoading:a}),n&&(0,s.jsxs)("div",{className:"text-red-500",children:["错误: ",n]}),(0,s.jsx)(z,{results:e,isLoading:a,selectedFiles:o,setSelectedFiles:d,onDownloadSelected:h})]})}},95139:(e,t,a)=>{a.d(t,{S:()=>o});var s=a(95155),r=a(12115),l=a(76981),n=a(5196),i=a(53999);let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.bL,{ref:t,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...r,children:(0,s.jsx)(l.C1,{className:(0,i.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})})});o.displayName=l.bL.displayName},99474:(e,t,a)=>{a.d(t,{T:()=>n});var s=a(95155),r=a(12115),l=a(53999);let n=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...r})});n.displayName="Textarea"},99840:(e,t,a)=>{a.d(t,{Cf:()=>f,Es:()=>x,HM:()=>m,L3:()=>p,c7:()=>u,lG:()=>o,rr:()=>j,zM:()=>d});var s=a(95155),r=a(12115),l=a(15452),n=a(54416),i=a(53999);let o=l.bL,d=l.l9,c=l.ZL,m=l.bm,h=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});h.displayName=l.hJ.displayName;let f=r.forwardRef((e,t)=>{let{className:a,children:r,...o}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(h,{}),(0,s.jsxs)(l.UC,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...o,children:[r,(0,s.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=l.UC.displayName;let u=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};u.displayName="DialogHeader";let x=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};x.displayName="DialogFooter";let p=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});p.displayName=l.hE.displayName;let j=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...r})});j.displayName=l.VY.displayName}}]);