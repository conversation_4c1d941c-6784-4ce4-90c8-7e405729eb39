"use strict";exports.id=226,exports.ids=[226],exports.modules={4679:(e,t)=>{function r(e){return/^\d\d\d(?:$| )/.test(e)}function s(e){return/^\d\d\d-/.test(e)}function i(e){return""!==e.trim()}Object.defineProperty(t,"__esModule",{value:!0}),t.positiveIntermediate=t.positiveCompletion=t.isMultiline=t.isSingleLine=t.parseControlResponse=void 0,t.parseControlResponse=function(e){let t;let o=e.split(/\r?\n/).filter(i),n=[],a=0;for(let e=0;e<o.length;e++){let i=o[e];if(t)t.test(i)&&(t=void 0,n.push(o.slice(a,e+1).join("\n")));else if(s(i)){let r=i.substr(0,3);t=RegExp(`^${r}(?:$| )`),a=e}else r(i)&&n.push(i)}return{messages:n,rest:t?o.slice(a).join("\n")+"\n":""}},t.isSingleLine=r,t.isMultiline=s,t.positiveCompletion=function(e){return e>=200&&e<300},t.positiveIntermediate=function(e){return e>=300&&e<400}},6226:function(e,t,r){var s=this&&this.__createBinding||(Object.create?function(e,t,r,s){void 0===s&&(s=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,s,i)}:function(e,t,r,s){void 0===s&&(s=r),e[s]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||s(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.enterPassiveModeIPv6=t.enterPassiveModeIPv4=void 0,i(r(11123),t),i(r(14505),t),i(r(66886),t),i(r(87233),t),i(r(87608),t);var o=r(10455);Object.defineProperty(t,"enterPassiveModeIPv4",{enumerable:!0,get:function(){return o.enterPassiveModeIPv4}}),Object.defineProperty(t,"enterPassiveModeIPv6",{enumerable:!0,get:function(){return o.enterPassiveModeIPv6}})},10455:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.downloadTo=t.uploadFrom=t.connectForPassiveTransfer=t.parsePasvResponse=t.enterPassiveModeIPv4=t.parseEpsvResponse=t.enterPassiveModeIPv6=void 0;let s=r(21396),i=r(27910),o=r(34631),n=r(4679);function a(e){let t=e.match(/[|!]{3}(.+)[|!]/);if(null===t||void 0===t[1])throw Error(`Can't parse response to 'EPSV': ${e}`);let r=parseInt(t[1],10);if(Number.isNaN(r))throw Error(`Can't parse response to 'EPSV', port is not a number: ${e}`);return r}function c(e){let t=e.match(/([-\d]+,[-\d]+,[-\d]+,[-\d]+),([-\d]+),([-\d]+)/);if(null===t||4!==t.length)throw Error(`Can't parse response to 'PASV': ${e}`);return{host:t[1].replace(/,/g,"."),port:(255&parseInt(t[2],10))*256+(255&parseInt(t[3],10))}}function l(e,t,r){return new Promise((s,i)=>{let n=r._newSocket(),a=function(e){e.message="Can't open data connection in passive mode: "+e.message,i(e)},c=function(){n.destroy(),i(Error(`Timeout when trying to open data connection to ${e}:${t}`))};n.setTimeout(r.timeout),n.on("error",a),n.on("timeout",c),n.connect({port:t,host:e,family:r.ipFamily},()=>{r.socket instanceof o.TLSSocket&&(n=(0,o.connect)(Object.assign({},r.tlsOptions,{socket:n,session:r.socket.getSession()}))),n.removeListener("error",a),n.removeListener("timeout",c),r.dataSocket=n,s()})})}t.enterPassiveModeIPv6=async function(e){let t=await e.request("EPSV"),r=a(t.message);if(!r)throw Error("Can't parse EPSV response: "+t.message);let s=e.socket.remoteAddress;if(void 0===s)throw Error("Control socket is disconnected, can't get remote address.");return await l(s,r,e),t},t.parseEpsvResponse=a,t.enterPassiveModeIPv4=async function(e){let t=await e.request("PASV"),r=c(t.message);if(!r)throw Error("Can't parse PASV response: "+t.message);let i=e.socket.remoteAddress;return(0,s.ipIsPrivateV4Address)(r.host)&&i&&!(0,s.ipIsPrivateV4Address)(i)&&(r.host=i),await l(r.host,r.port,e),t},t.parsePasvResponse=c,t.connectForPassiveTransfer=l;class d{constructor(e,t){this.ftp=e,this.progress=t,this.response=void 0,this.dataTransferDone=!1}onDataStart(e,t){if(void 0===this.ftp.dataSocket)throw Error("Data transfer should start but there is no data connection.");this.ftp.socket.setTimeout(0),this.ftp.dataSocket.setTimeout(this.ftp.timeout),this.progress.start(this.ftp.dataSocket,e,t)}onDataDone(e){this.progress.updateAndStop(),this.ftp.socket.setTimeout(this.ftp.timeout),this.ftp.dataSocket&&this.ftp.dataSocket.setTimeout(0),this.dataTransferDone=!0,this.tryResolve(e)}onControlDone(e,t){this.response=t,this.tryResolve(e)}onError(e,t){this.progress.updateAndStop(),this.ftp.socket.setTimeout(this.ftp.timeout),this.ftp.dataSocket=void 0,e.reject(t)}onUnexpectedRequest(e){let t=Error(`Unexpected FTP response is requesting an answer: ${e.message}`);this.ftp.closeWithError(t)}tryResolve(e){this.dataTransferDone&&void 0!==this.response&&(this.ftp.dataSocket=void 0,e.resolve(this.response))}}t.uploadFrom=function(e,t){let r=new d(t.ftp,t.tracker),o=`${t.command} ${t.remotePath}`;return t.ftp.handle(o,(o,a)=>{if(o instanceof Error)r.onError(a,o);else if(150===o.code||125===o.code){var c,l,d,p;let o=t.ftp.dataSocket;if(!o){r.onError(a,Error("Upload should begin but no data connection is available."));return}c=!("getCipher"in o)||void 0!==o.getCipher(),l=o,d="secureConnect",p=()=>{t.ftp.log(`Uploading to ${(0,s.describeAddress)(o)} (${(0,s.describeTLS)(o)})`),r.onDataStart(t.remotePath,t.type),(0,i.pipeline)(e,o,e=>{e?r.onError(a,e):r.onDataDone(a)})},!0===c?p():l.once(d,()=>p())}else(0,n.positiveCompletion)(o.code)?r.onControlDone(a,o):(0,n.positiveIntermediate)(o.code)&&r.onUnexpectedRequest(o)})},t.downloadTo=function(e,t){if(!t.ftp.dataSocket)throw Error("Download will be initiated but no data connection is available.");let r=new d(t.ftp,t.tracker);return t.ftp.handle(t.command,(o,a)=>{if(o instanceof Error)r.onError(a,o);else if(150===o.code||125===o.code){let o=t.ftp.dataSocket;if(!o){r.onError(a,Error("Download should begin but no data connection is available."));return}t.ftp.log(`Downloading from ${(0,s.describeAddress)(o)} (${(0,s.describeTLS)(o)})`),r.onDataStart(t.remotePath,t.type),(0,i.pipeline)(o,e,e=>{e?r.onError(a,e):r.onDataDone(a)})}else 350===o.code?t.ftp.send("RETR "+t.remotePath):(0,n.positiveCompletion)(o.code)?r.onControlDone(a,o):(0,n.positiveIntermediate)(o.code)&&r.onUnexpectedRequest(o)})}},11123:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Client=void 0;let s=r(29021),i=r(33873),o=r(34631),n=r(28354),a=r(14505),c=r(87233),l=r(37359),d=r(76306),p=r(72939),u=r(21396),h=r(10455),f=r(4679),m=(0,n.promisify)(s.readdir),g=(0,n.promisify)(s.mkdir),w=(0,n.promisify)(s.stat),y=(0,n.promisify)(s.open),v=(0,n.promisify)(s.close),k=(0,n.promisify)(s.unlink),S=()=>["LIST -a","LIST"],_=()=>["MLSD","LIST -a","LIST"];class T{constructor(e=3e4){this.availableListCommands=S(),this.ftp=new a.FTPContext(e),this.prepareTransfer=this._enterFirstCompatibleMode([h.enterPassiveModeIPv6,h.enterPassiveModeIPv4]),this.parseList=c.parseList,this._progressTracker=new l.ProgressTracker}close(){this.ftp.close(),this._progressTracker.stop()}get closed(){return this.ftp.closed}connect(e="localhost",t=21){return this.ftp.reset(),this.ftp.socket.connect({host:e,port:t,family:this.ftp.ipFamily},()=>this.ftp.log(`Connected to ${(0,u.describeAddress)(this.ftp.socket)} (${(0,u.describeTLS)(this.ftp.socket)})`)),this._handleConnectResponse()}connectImplicitTLS(e="localhost",t=21,r={}){return this.ftp.reset(),this.ftp.socket=(0,o.connect)(t,e,r,()=>this.ftp.log(`Connected to ${(0,u.describeAddress)(this.ftp.socket)} (${(0,u.describeTLS)(this.ftp.socket)})`)),this.ftp.tlsOptions=r,this._handleConnectResponse()}_handleConnectResponse(){return this.ftp.handle(void 0,(e,t)=>{e instanceof Error?t.reject(e):(0,f.positiveCompletion)(e.code)?t.resolve(e):t.reject(new a.FTPError(e))})}send(e,t=!1){return t?(this.ftp.log("Deprecated call using send(command, flag) with boolean flag to ignore errors. Use sendIgnoringError(command)."),this.sendIgnoringError(e)):this.ftp.request(e)}sendIgnoringError(e){return this.ftp.handle(e,(e,t)=>{e instanceof a.FTPError?t.resolve({code:e.code,message:e.message}):e instanceof Error?t.reject(e):t.resolve(e)})}async useTLS(e={},t="AUTH TLS"){let r=await this.send(t);return this.ftp.socket=await (0,u.upgradeSocket)(this.ftp.socket,e),this.ftp.tlsOptions=e,this.ftp.log(`Control socket is using: ${(0,u.describeTLS)(this.ftp.socket)}`),r}login(e="anonymous",t="guest"){return this.ftp.log(`Login security: ${(0,u.describeTLS)(this.ftp.socket)}`),this.ftp.handle("USER "+e,(e,r)=>{e instanceof Error?r.reject(e):(0,f.positiveCompletion)(e.code)?r.resolve(e):331===e.code?this.ftp.send("PASS "+t):r.reject(new a.FTPError(e))})}async useDefaultSettings(){let e=(await this.features()).has("MLST");this.availableListCommands=e?_():S(),await this.send("TYPE I"),await this.sendIgnoringError("STRU F"),await this.sendIgnoringError("OPTS UTF8 ON"),e&&await this.sendIgnoringError("OPTS MLST type;size;modify;unique;unix.mode;unix.owner;unix.group;unix.ownername;unix.groupname;"),this.ftp.hasTLS&&(await this.sendIgnoringError("PBSZ 0"),await this.sendIgnoringError("PROT P"))}async access(e={}){var t,r;let s;let i=!0===e.secure;if(s="implicit"===e.secure?await this.connectImplicitTLS(e.host,e.port,e.secureOptions):await this.connect(e.host,e.port),i){let s=null!==(t=e.secureOptions)&&void 0!==t?t:{};s.host=null!==(r=s.host)&&void 0!==r?r:e.host,await this.useTLS(s)}return await this.sendIgnoringError("OPTS UTF8 ON"),await this.login(e.user,e.password),await this.useDefaultSettings(),s}async pwd(){let e=await this.send("PWD"),t=e.message.match(/"(.+)"/);if(null===t||void 0===t[1])throw Error(`Can't parse response to command 'PWD': ${e.message}`);return t[1]}async features(){let e=await this.sendIgnoringError("FEAT"),t=new Map;return e.code<400&&(0,f.isMultiline)(e.message)&&e.message.split("\n").slice(1,-1).forEach(e=>{let r=e.trim().split(" ");t.set(r[0],r[1]||"")}),t}async cd(e){let t=await this.protectWhitespace(e);return this.send("CWD "+t)}async cdup(){return this.send("CDUP")}async lastMod(e){let t=await this.protectWhitespace(e),r=(await this.send(`MDTM ${t}`)).message.slice(4);return(0,p.parseMLSxDate)(r)}async size(e){let t=await this.protectWhitespace(e),r=`SIZE ${t}`,s=await this.send(r),i=parseInt(s.message.slice(4),10);if(Number.isNaN(i))throw Error(`Can't parse response to command '${r}' as a numerical value: ${s.message}`);return i}async rename(e,t){let r=await this.protectWhitespace(e),s=await this.protectWhitespace(t);return await this.send("RNFR "+r),this.send("RNTO "+s)}async remove(e,t=!1){let r=await this.protectWhitespace(e);return t?this.sendIgnoringError(`DELE ${r}`):this.send(`DELE ${r}`)}trackProgress(e){this._progressTracker.bytesOverall=0,this._progressTracker.reportTo(e)}async uploadFrom(e,t,r={}){return this._uploadWithCommand(e,t,"STOR",r)}async appendFrom(e,t,r={}){return this._uploadWithCommand(e,t,"APPE",r)}async _uploadWithCommand(e,t,r,s){return"string"==typeof e?this._uploadLocalFile(e,t,r,s):this._uploadFromStream(e,t,r)}async _uploadLocalFile(e,t,r,i){let o=await y(e,"r"),n=(0,s.createReadStream)("",{fd:o,start:i.localStart,end:i.localEndInclusive,autoClose:!1});try{return await this._uploadFromStream(n,t,r)}finally{await P(()=>v(o))}}async _uploadFromStream(e,t,r){let s=e=>this.ftp.closeWithError(e);e.once("error",s);try{let s=await this.protectWhitespace(t);return await this.prepareTransfer(this.ftp),await (0,h.uploadFrom)(e,{ftp:this.ftp,tracker:this._progressTracker,command:r,remotePath:s,type:"upload"})}finally{e.removeListener("error",s)}}async downloadTo(e,t,r=0){return"string"==typeof e?this._downloadToFile(e,t,r):this._downloadToStream(e,t,r)}async _downloadToFile(e,t,r){let i=r>0,o=await y(e,i?"r+":"w"),n=(0,s.createWriteStream)("",{fd:o,start:r,autoClose:!1});try{return await this._downloadToStream(n,t,r)}catch(s){let t=await P(()=>w(e)),r=t&&t.size>0;throw i||r||await P(()=>k(e)),s}finally{await P(()=>v(o))}}async _downloadToStream(e,t,r){let s=e=>this.ftp.closeWithError(e);e.once("error",s);try{let s=await this.protectWhitespace(t);return await this.prepareTransfer(this.ftp),await (0,h.downloadTo)(e,{ftp:this.ftp,tracker:this._progressTracker,command:r>0?`REST ${r}`:`RETR ${s}`,remotePath:s,type:"download"})}finally{e.removeListener("error",s),e.end()}}async list(e=""){let t;let r=await this.protectWhitespace(e);for(let e of this.availableListCommands){let s=""===r?e:`${e} ${r}`;await this.prepareTransfer(this.ftp);try{let t=await this._requestListWithCommand(s);return this.availableListCommands=[e],t}catch(e){if(!(e instanceof a.FTPError))throw e;t=e}}throw t}async _requestListWithCommand(e){let t=new d.StringWriter;await (0,h.downloadTo)(t,{ftp:this.ftp,tracker:this._progressTracker,command:e,remotePath:"",type:"list"});let r=t.getText(this.ftp.encoding);return this.ftp.log(r),this.parseList(r)}async removeDir(e){return this._exitAtCurrentDirectory(async()=>{await this.cd(e);let t=await this.pwd();await this.clearWorkingDir(),"/"!==t&&(await this.cdup(),await this.removeEmptyDir(t))})}async clearWorkingDir(){for(let e of(await this.list()))e.isDirectory?(await this.cd(e.name),await this.clearWorkingDir(),await this.cdup(),await this.removeEmptyDir(e.name)):await this.remove(e.name)}async uploadFromDir(e,t){return this._exitAtCurrentDirectory(async()=>(t&&await this.ensureDir(t),await this._uploadToWorkingDir(e)))}async _uploadToWorkingDir(e){for(let t of(await m(e))){let r=(0,i.join)(e,t),s=await w(r);s.isFile()?await this.uploadFrom(r,t):s.isDirectory()&&(await this._openDir(t),await this._uploadToWorkingDir(r),await this.cdup())}}async downloadToDir(e,t){return this._exitAtCurrentDirectory(async()=>(t&&await this.cd(t),await this._downloadFromWorkingDir(e)))}async _downloadFromWorkingDir(e){for(let t of(await b(e),await this.list())){let r=(0,i.join)(e,t.name);t.isDirectory?(await this.cd(t.name),await this._downloadFromWorkingDir(r),await this.cdup()):t.isFile&&await this.downloadTo(r,t.name)}}async ensureDir(e){for(let t of(e.startsWith("/")&&await this.cd("/"),e.split("/").filter(e=>""!==e)))await this._openDir(t)}async _openDir(e){await this.sendIgnoringError("MKD "+e),await this.cd(e)}async removeEmptyDir(e){let t=await this.protectWhitespace(e);return this.send(`RMD ${t}`)}async protectWhitespace(e){if(!e.startsWith(" "))return e;let t=await this.pwd();return(t.endsWith("/")?t:t+"/")+e}async _exitAtCurrentDirectory(e){let t=await this.pwd();try{return await e()}finally{this.closed||await P(()=>this.cd(t))}}_enterFirstCompatibleMode(e){return async t=>{let r;for(let s of(t.log("Trying to find optimal transfer strategy..."),e))try{let e=await s(t);return t.log("Optimal transfer strategy found."),this.prepareTransfer=s,e}catch(e){r=e}throw Error(`None of the available transfer strategies work. Last error response was '${r}'.`)}}async upload(e,t,r={}){return this.ftp.log("Warning: upload() has been deprecated, use uploadFrom()."),this.uploadFrom(e,t,r)}async append(e,t,r={}){return this.ftp.log("Warning: append() has been deprecated, use appendFrom()."),this.appendFrom(e,t,r)}async download(e,t,r=0){return this.ftp.log("Warning: download() has been deprecated, use downloadTo()."),this.downloadTo(e,t,r)}async uploadDir(e,t){return this.ftp.log("Warning: uploadDir() has been deprecated, use uploadFromDir()."),this.uploadFromDir(e,t)}async downloadDir(e){return this.ftp.log("Warning: downloadDir() has been deprecated, use downloadToDir()."),this.downloadToDir(e)}}async function b(e){try{await w(e)}catch(t){await g(e,{recursive:!0})}}async function P(e){try{return await e()}catch(e){return}}t.Client=T},13771:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.transformList=t.parseLine=t.testLine=void 0;let s=r(66886),i=RegExp("(\\S+)\\s+(\\S+)\\s+(?:(<DIR>)|([0-9]+))\\s+(\\S.*)");t.testLine=function(e){return/^\d{2}/.test(e)&&i.test(e)},t.parseLine=function(e){let t=e.match(i);if(null===t)return;let r=t[5];if("."===r||".."===r)return;let o=new s.FileInfo(r);return"<DIR>"===t[3]?(o.type=s.FileType.Directory,o.size=0):(o.type=s.FileType.File,o.size=parseInt(t[4],10)),o.rawModifiedAt=t[1]+" "+t[2],o},t.transformList=function(e){return e}},14505:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FTPContext=t.FTPError=void 0;let s=r(91645),i=r(4679);class o extends Error{constructor(e){super(e.message),this.name=this.constructor.name,this.code=e.code}}function n(){}t.FTPError=o;class a{constructor(e=0,t="utf8"){this.timeout=e,this.verbose=!1,this.ipFamily=void 0,this.tlsOptions={},this._partialResponse="",this._encoding=t,this._socket=this.socket=this._newSocket(),this._dataSocket=void 0}close(){let e=Error(this._task?"User closed client during task":"User closed client");this.closeWithError(e)}closeWithError(e){!this._closingError&&(this._closingError=e,this._closeControlSocket(),this._closeSocket(this._dataSocket),this._passToHandler(e),this._stopTrackingTask())}get closed(){return void 0===this.socket.remoteAddress||void 0!==this._closingError}reset(){this.socket=this._newSocket()}get socket(){return this._socket}set socket(e){this.dataSocket=void 0,this.tlsOptions={},this._partialResponse="",this._socket&&(e.localPort===this._socket.localPort?this._removeSocketListeners(this.socket):this._closeControlSocket()),e&&(this._closingError=void 0,e.setTimeout(0),e.setEncoding(this._encoding),e.setKeepAlive(!0),e.on("data",e=>this._onControlSocketData(e)),e.on("end",()=>this.closeWithError(Error("Server sent FIN packet unexpectedly, closing connection."))),e.on("close",e=>{e||this.closeWithError(Error("Server closed connection unexpectedly."))}),this._setupDefaultErrorHandlers(e,"control socket")),this._socket=e}get dataSocket(){return this._dataSocket}set dataSocket(e){this._closeSocket(this._dataSocket),e&&(e.setTimeout(0),this._setupDefaultErrorHandlers(e,"data socket")),this._dataSocket=e}get encoding(){return this._encoding}set encoding(e){this._encoding=e,this.socket&&this.socket.setEncoding(e)}send(e){let t=e.startsWith("PASS")?"> PASS ###":`> ${e}`;this.log(t),this._socket.write(e+"\r\n",this.encoding)}request(e){return this.handle(e,(e,t)=>{e instanceof Error?t.reject(e):t.resolve(e)})}handle(e,t){if(this._task){let e=Error("User launched a task while another one is still running. Forgot to use 'await' or '.then()'?");e.stack+=`
Running task launched at: ${this._task.stack}`,this.closeWithError(e)}return new Promise((r,s)=>{if(this._task={stack:Error().stack||"Unknown call stack",responseHandler:t,resolver:{resolve:e=>{this._stopTrackingTask(),r(e)},reject:e=>{this._stopTrackingTask(),s(e)}}},this._closingError){let e=Error(`Client is closed because ${this._closingError.message}`);e.stack+=`
Closing reason: ${this._closingError.stack}`,e.code=void 0!==this._closingError.code?this._closingError.code:"0",this._passToHandler(e);return}this.socket.setTimeout(this.timeout),e&&this.send(e)})}log(e){this.verbose&&console.log(e)}get hasTLS(){return"encrypted"in this._socket}_stopTrackingTask(){this.socket.setTimeout(0),this._task=void 0}_onControlSocketData(e){this.log(`< ${e}`);let t=this._partialResponse+e,r=(0,i.parseControlResponse)(t);for(let e of(this._partialResponse=r.rest,r.messages)){let t=parseInt(e.substr(0,3),10),r={code:t,message:e},s=t>=400?new o(r):void 0;this._passToHandler(s||r)}}_passToHandler(e){this._task&&this._task.responseHandler(e,this._task.resolver)}_setupDefaultErrorHandlers(e,t){e.once("error",e=>{e.message+=` (${t})`,this.closeWithError(e)}),e.once("close",e=>{e&&this.closeWithError(Error(`Socket closed due to transmission error (${t})`))}),e.once("timeout",()=>{e.destroy(),this.closeWithError(Error(`Timeout (${t})`))})}_closeControlSocket(){this._removeSocketListeners(this._socket),this._socket.on("error",n),this.send("QUIT"),this._closeSocket(this._socket)}_closeSocket(e){e&&(this._removeSocketListeners(e),e.on("error",n),e.destroy())}_removeSocketListeners(e){e.removeAllListeners(),e.removeAllListeners("timeout"),e.removeAllListeners("data"),e.removeAllListeners("end"),e.removeAllListeners("error"),e.removeAllListeners("close"),e.removeAllListeners("connect")}_newSocket(){return new s.Socket}}t.FTPContext=a},21396:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ipIsPrivateV4Address=t.upgradeSocket=t.describeAddress=t.describeTLS=void 0;let s=r(34631);t.describeTLS=function(e){return e instanceof s.TLSSocket?e.getProtocol()||"Server socket or disconnected client socket":"No encryption"},t.describeAddress=function(e){return"IPv6"===e.remoteFamily?`[${e.remoteAddress}]:${e.remotePort}`:`${e.remoteAddress}:${e.remotePort}`},t.upgradeSocket=function(e,t){return new Promise((r,i)=>{let o=Object.assign({},t,{socket:e}),n=(0,s.connect)(o,()=>{!1===o.rejectUnauthorized||n.authorized?(n.removeAllListeners("error"),r(n)):i(n.authorizationError)}).once("error",e=>{i(e)})})},t.ipIsPrivateV4Address=function(e=""){e.startsWith("::ffff:")&&(e=e.substr(7));let t=e.split(".").map(e=>parseInt(e,10));return 10===t[0]||172===t[0]&&t[1]>=16&&t[1]<=31||192===t[0]&&168===t[1]||"127.0.0.1"===e}},30127:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.transformList=t.parseLine=t.testLine=void 0;let s=r(66886),i=RegExp("([bcdelfmpSs-])(((r|-)(w|-)([xsStTL-]))((r|-)(w|-)([xsStTL-]))((r|-)(w|-)([xsStTL-]?)))\\+?\\s*(\\d+)\\s+(?:(\\S+(?:\\s\\S+)*?)\\s+)?(?:(\\S+(?:\\s\\S+)*)\\s+)?(\\d+(?:,\\s*\\d+)?)\\s+((?:\\d+[-/]\\d+[-/]\\d+)|(?:\\S{3}\\s+\\d{1,2})|(?:\\d{1,2}\\s+\\S{3})|(?:\\d{1,2}月\\s+\\d{1,2}日))\\s+((?:\\d+(?::\\d+)?)|(?:\\d{4}年))\\s(.*)");function o(e,t,r){let i=0;"-"!==e&&(i+=s.FileInfo.UnixPermission.Read),"-"!==t&&(i+=s.FileInfo.UnixPermission.Write);let o=r.charAt(0);return"-"!==o&&o.toUpperCase()!==o&&(i+=s.FileInfo.UnixPermission.Execute),i}t.testLine=function(e){return i.test(e)},t.parseLine=function(e){let t=e.match(i);if(null===t)return;let r=t[21];if("."===r||".."===r)return;let n=new s.FileInfo(r);switch(n.size=parseInt(t[18],10),n.user=t[16],n.group=t[17],n.hardLinkCount=parseInt(t[15],10),n.rawModifiedAt=t[19]+" "+t[20],n.permissions={user:o(t[4],t[5],t[6]),group:o(t[8],t[9],t[10]),world:o(t[12],t[13],t[14])},t[1].charAt(0)){case"d":n.type=s.FileType.Directory;break;case"e":case"l":n.type=s.FileType.SymbolicLink;break;case"b":case"c":case"f":case"-":n.type=s.FileType.File;break;default:n.type=s.FileType.Unknown}if(n.isSymbolicLink){let e=r.indexOf(" -> ");-1!==e&&(n.name=r.substring(0,e),n.link=r.substring(e+4))}return n},t.transformList=function(e){return e}},37359:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressTracker=void 0;class r{constructor(){this.bytesOverall=0,this.intervalMs=500,this.onStop=s,this.onHandle=s}reportTo(e=s){this.onHandle=e}start(e,t,r){let i=0;this.onStop=function(e,t){let r=setInterval(t,e);return t(),e=>{clearInterval(r),e&&t(),t=s}}(this.intervalMs,()=>{let s=e.bytesRead+e.bytesWritten;this.bytesOverall+=s-i,i=s,this.onHandle({name:t,type:r,bytes:s,bytesOverall:this.bytesOverall})})}stop(){this.onStop(!1)}updateAndStop(){this.onStop(!0)}}function s(){}t.ProgressTracker=r},66886:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.FileInfo=t.FileType=void 0,function(e){e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=3]="SymbolicLink"}(r||(t.FileType=r={}));class s{constructor(e){this.name=e,this.type=r.Unknown,this.size=0,this.rawModifiedAt="",this.modifiedAt=void 0,this.permissions=void 0,this.hardLinkCount=void 0,this.link=void 0,this.group=void 0,this.user=void 0,this.uniqueID=void 0,this.name=e}get isDirectory(){return this.type===r.Directory}get isSymbolicLink(){return this.type===r.SymbolicLink}get isFile(){return this.type===r.File}get date(){return this.rawModifiedAt}set date(e){this.rawModifiedAt=e}}t.FileInfo=s,s.UnixPermission={Read:4,Write:2,Execute:1}},72939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseMLSxDate=t.transformList=t.parseLine=t.testLine=void 0;let s=r(66886);function i(e,t){t.size=parseInt(e,10)}let o={size:i,sizd:i,unique:(e,t)=>{t.uniqueID=e},modify:(e,t)=>{t.modifiedAt=a(e),t.rawModifiedAt=t.modifiedAt.toISOString()},type:(e,t)=>{if(e.startsWith("OS.unix=slink"))return t.type=s.FileType.SymbolicLink,t.link=e.substr(e.indexOf(":")+1),1;switch(e){case"file":t.type=s.FileType.File;break;case"dir":t.type=s.FileType.Directory;break;case"OS.unix=symlink":t.type=s.FileType.SymbolicLink;break;case"cdir":case"pdir":return 2;default:t.type=s.FileType.Unknown}return 1},"unix.mode":(e,t)=>{let r=e.substr(-3);t.permissions={user:parseInt(r[0],10),group:parseInt(r[1],10),world:parseInt(r[2],10)}},"unix.ownername":(e,t)=>{t.user=e},"unix.owner":(e,t)=>{void 0===t.user&&(t.user=e)},get"unix.uid"(){return this["unix.owner"]},"unix.groupname":(e,t)=>{t.group=e},"unix.group":(e,t)=>{void 0===t.group&&(t.group=e)},get"unix.gid"(){return this["unix.group"]}};function n(e,t){let r=e.indexOf(t);return[e.substr(0,r),e.substr(r+t.length)]}function a(e){return new Date(Date.UTC(+e.slice(0,4),+e.slice(4,6)-1,+e.slice(6,8),+e.slice(8,10),+e.slice(10,12),+e.slice(12,14),+e.slice(15,18)))}t.testLine=function(e){return/^\S+=\S+;/.test(e)||e.startsWith(" ")},t.parseLine=function(e){let[t,r]=n(e," ");if(""===r||"."===r||".."===r)return;let i=new s.FileInfo(r);for(let e of t.split(";")){let[t,r]=n(e,"=");if(!r)continue;let s=o[t.toLowerCase()];if(s&&2===s(r,i))return}return i},t.transformList=function(e){let t=new Map;for(let r of e)r.isSymbolicLink||void 0===r.uniqueID||t.set(r.uniqueID,r);let r=[];for(let s of e){if(s.isSymbolicLink&&void 0!==s.uniqueID&&void 0===s.link){let e=t.get(s.uniqueID);void 0!==e&&(s.link=e.name)}s.name.includes("/")||r.push(s)}return r},t.parseMLSxDate=a},76306:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StringWriter=void 0;let s=r(27910);class i extends s.Writable{constructor(){super(...arguments),this.buf=Buffer.alloc(0)}_write(e,t,r){e instanceof Buffer?(this.buf=Buffer.concat([this.buf,e]),r(null)):r(Error("StringWriter expects chunks of type 'Buffer'."))}getText(e){return this.buf.toString(e)}}t.StringWriter=i},87233:function(e,t,r){var s=this&&this.__createBinding||(Object.create?function(e,t,r,s){void 0===s&&(s=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,s,i)}:function(e,t,r,s){void 0===s&&(s=r),e[s]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&s(t,e,r);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.parseList=void 0;let n=o(r(13771)),a=[n,o(r(30127)),o(r(72939))];function c(e){return""!==e.trim()}function l(e){return!e.startsWith("total")}let d=/\r?\n/;t.parseList=function(e){var t;let r=e.split(d).filter(c).filter(l);if(0===r.length)return[];let s=(t=r[r.length-1],a.find(e=>!0===e.testLine(t)));if(!s)throw Error("This library only supports MLSD, Unix- or DOS-style directory listing. Your FTP server seems to be using another format. You can see the transmitted listing when setting `client.ftp.verbose = true`. You can then provide a custom parser to `client.parseList`, see the documentation for details.");let i=r.map(s.parseLine).filter(e=>void 0!==e);return s.transformList(i)}},87608:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})}};