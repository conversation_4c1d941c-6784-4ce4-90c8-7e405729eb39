"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[991],{82991:(e,t,l)=>{l.d(t,{default:()=>u});var a=l(95155),r=l(12115),o=l(88482),n=l(82714),s=l(89852),i=l(97168),d=l(95784);let c=e=>{let{canvasSize:t,setCanvasSize:l,setGrid:c,grid:h,selectedColor:u,setSelectedColor:f,selectedShapeType:m,setSelectedShapeType:g,diameter:p,setDiameter:x,replaceColor:w,resetCanvas:M,exportCanvas:v}=e,[j,y]=r.useState("#000000"),[b,C]=r.useState("#ff0000"),[S,F]=r.useState({width:0,height:0});return r.useEffect(()=>{h.cols>0&&h.rows>0&&F({width:t.width/h.cols,height:t.height/h.rows})},[t,h]),(0,a.jsxs)(o.Zp,{className:"w-full md:w-80",children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{children:"工具栏"})}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{children:"画布尺寸 (px)"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(s.p,{type:"number",defaultValue:1920,onChange:e=>l({width:parseInt(e.target.value),height:t.height}),placeholder:"宽度"}),(0,a.jsx)(s.p,{type:"number",defaultValue:1080,onChange:e=>l({width:t.width,height:parseInt(e.target.value)}),placeholder:"高度"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{children:"网格 (列x行)"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(s.p,{type:"number",value:h.cols,onChange:e=>c({...h,cols:parseInt(e.target.value)}),placeholder:"列"}),(0,a.jsx)(s.p,{type:"number",value:h.rows,onChange:e=>c({...h,rows:parseInt(e.target.value)}),placeholder:"行"})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(n.J,{children:"单元格尺寸"}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground p-2 border rounded-md",children:[(0,a.jsxs)("div",{children:["宽: ",S.width.toFixed(2)," px"]}),(0,a.jsxs)("div",{children:["高: ",S.height.toFixed(2)," px"]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{children:"图形类型"}),(0,a.jsxs)(d.l6,{value:m,onValueChange:e=>g(e),children:[(0,a.jsx)(d.bq,{children:(0,a.jsx)(d.yv,{placeholder:"选择图形"})}),(0,a.jsxs)(d.gC,{children:[(0,a.jsx)(d.eb,{value:"circle",children:"圆形"}),(0,a.jsx)(d.eb,{value:"square",children:"MTF"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{children:"直径"}),(0,a.jsx)(s.p,{type:"number",value:p,onChange:e=>x(parseInt(e.target.value,10)||0)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{children:"图形颜色"}),(0,a.jsx)(s.p,{type:"color",value:u,onChange:e=>f(e.target.value),className:"w-full"})]}),(0,a.jsxs)("div",{className:"space-y-2 border-t pt-4",children:[(0,a.jsx)(n.J,{children:"替换颜色"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s.p,{placeholder:"旧颜色",type:"color",value:j,onChange:e=>y(e.target.value)}),(0,a.jsx)("span",{children:">"}),(0,a.jsx)(s.p,{placeholder:"新颜色",type:"color",value:b,onChange:e=>C(e.target.value)})]}),(0,a.jsx)(i.$,{onClick:()=>w(j,b),className:"w-full",children:"替换"})]}),(0,a.jsxs)("div",{className:"space-y-2 border-t pt-4",children:[(0,a.jsx)(n.J,{children:"画布操作"}),(0,a.jsx)(i.$,{onClick:M,className:"w-full",variant:"destructive",children:"重置画布"}),(0,a.jsx)(i.$,{onClick:()=>v("png"),className:"w-full",children:"导出为 PNG"}),(0,a.jsx)(i.$,{onClick:()=>v("jpeg"),className:"w-full",children:"导出为 JPEG"})]})]})]})},h=e=>{let{width:t,height:l,grid:o,shapes:n,addShape:s,deleteShape:i,backgroundColor:d}=e,c=(0,r.useRef)(null);(0,r.useEffect)(()=>{let e=c.current;if(!e)return;let a=e.getContext("2d");if(!a)return;let r=window.devicePixelRatio||1;e.width=t*r,e.height=l*r,e.style.width="".concat(t,"px"),e.style.height="".concat(l,"px"),a.scale(r,r),a.fillStyle=d,a.fillRect(0,0,t,l),h(a);let s=e=>{let a,r;let n=t/o.cols,s=l/o.rows,i=e.diameter/2,d=e.cell.col*n,c=e.cell.row*s;switch(e.alignment){case"center":a=d+n/2,r=c+s/2;break;case"topLeft":a=d+i,r=c+i;break;case"coordinates":e.coordinates?(a=d+e.coordinates.x,r=c+e.coordinates.y):(a=d+n/2,r=c+s/2)}return{cx:a,cy:r}},i=(e,t,l,a,r,o)=>{let n=document.createElement("canvas");n.width=a*o,n.height=a*o;let s=n.getContext("2d");if(!s)return;s.scale(o,o),s.fillStyle="#FFFFFF",s.fillRect(0,0,a,a),s.fillStyle=r,s.imageSmoothingEnabled=!1;let i=a/2,d=a/2;s.fillRect(Math.round(0),Math.round(d-1),Math.round(a),Math.round(2)),s.fillRect(Math.round(i-1),Math.round(0),Math.round(2),Math.round(a));for(let e=i-1-2;e>0;e-=4)s.fillRect(Math.round(e-2),Math.round(0),Math.round(2),Math.round(a/2-1));for(let e=d-1-2;e>0;e-=4)s.fillRect(Math.round(i+1),Math.round(e-2),Math.round(a/2-1),Math.round(2));for(let e=d+1+2;e<a;e+=4)s.fillRect(Math.round(0),Math.round(e),Math.round(a/2-1),Math.round(2));for(let e=i+1+2;e<a;e+=4)s.fillRect(Math.round(e),Math.round(d+1),Math.round(2),Math.round(a/2-1));e.imageSmoothingEnabled=!1,e.drawImage(n,t,l,a,a)},u=(e,t,l)=>{let{cx:a,cy:r}=s(t),o=t.diameter/2;if("circle"===t.type){e.save(),e.imageSmoothingEnabled=!1;let n=document.createElement("canvas"),s=Math.ceil(t.diameter*l)+2;n.width=s,n.height=s;let i=n.getContext("2d");if(i){i.imageSmoothingEnabled=!1,i.fillStyle=t.color;let o=t.diameter*l/2,d=s/2;for(let e=0;e<s;e++)for(let t=0;t<s;t++)Math.sqrt((e-d)**2+(t-d)**2)<=o&&i.fillRect(e,t,1,1);e.drawImage(n,a-t.diameter/2,r-t.diameter/2,t.diameter,t.diameter)}else e.fillStyle=t.color,e.beginPath(),e.arc(a,r,o,0,2*Math.PI),e.fill();e.restore()}else"square"===t.type&&i(e,a-o,r-o,t.diameter,t.color,l)};n.forEach(e=>u(a,e,r))},[t,l,o,n,d]);let h=e=>{e.strokeStyle="rgba(0, 0, 0, 0.5)",e.setLineDash([5,5]),e.lineWidth=1;let a=t/o.cols,r=l/o.rows;for(let t=1;t<o.cols;t++)e.beginPath(),e.moveTo(t*a,0),e.lineTo(t*a,l),e.stroke();for(let l=1;l<o.rows;l++)e.beginPath(),e.moveTo(0,l*r),e.lineTo(t,l*r),e.stroke();e.setLineDash([])},u=(e,a)=>{let r=t/o.cols,s=l/o.rows;for(let t=n.length-1;t>=0;t--){let l,o;let i=n[t],d=i.diameter/2,c=i.cell.col*r,h=i.cell.row*s;switch(i.alignment){case"center":l=c+r/2,o=h+s/2;break;case"topLeft":l=c+d,o=h+d;break;case"coordinates":i.coordinates?(l=c+i.coordinates.x,o=h+i.coordinates.y):(l=c+r/2,o=h+s/2)}if("circle"===i.type){if(Math.sqrt((e-l)**2+(a-o)**2)<=d)return i}else if("square"===i.type&&e>=l-d&&e<=l+d&&a>=o-d&&a<=o+d)return i}};return(0,a.jsx)("canvas",{ref:c,width:t,height:l,onMouseDown:e=>{let a=c.current;if(!a)return;let r=a.getBoundingClientRect(),n=e.clientX-r.left,d=e.clientY-r.top;if(2===e.button){e.preventDefault();let t=u(n,d);t&&i(t.id);return}let h=t/o.cols;s({row:Math.floor(d/(l/o.rows)),col:Math.floor(n/h)},"center")},onContextMenu:e=>e.preventDefault(),className:"border border-gray-400"})},u=()=>{let[e,t]=(0,r.useState)({width:1920,height:1080}),[l,o]=(0,r.useState)({rows:9,cols:17}),[n,s]=(0,r.useState)([]),[i,d]=(0,r.useState)("#000000"),[u,f]=(0,r.useState)("#FFFFFF"),[m,g]=(0,r.useState)("circle"),[p,x]=(0,r.useState)(50),w=async t=>{if("undefined"==typeof document)return;let a=document.createElement("canvas");a.width=e.width,a.height=e.height;let r=a.getContext("2d");if(!r){console.error("Could not get temporary canvas context for exporting.");return}r.fillStyle=u,r.fillRect(0,0,a.width,a.height);let o=e=>{let t,r;let o=a.width/l.cols,n=a.height/l.rows,s=e.diameter/2,i=e.cell.col*o,d=e.cell.row*n;switch(e.alignment){case"center":t=i+o/2,r=d+n/2;break;case"topLeft":t=i+s,r=d+s;break;case"coordinates":e.coordinates?(t=i+e.coordinates.x,r=d+e.coordinates.y):(t=i+o/2,r=d+n/2)}return{cx:t,cy:r}},s=(e,t,l,a,r)=>{let o=document.createElement("canvas");o.width=a,o.height=a;let n=o.getContext("2d");if(!n)return;n.fillStyle="#FFFFFF",n.fillRect(0,0,a,a),n.fillStyle=r,n.imageSmoothingEnabled=!1;let s=a/2,i=a/2;n.fillRect(Math.round(0),Math.round(i-1),Math.round(a),Math.round(2)),n.fillRect(Math.round(s-1),Math.round(0),Math.round(2),Math.round(a));for(let e=s-1-2;e>0;e-=4)n.fillRect(Math.round(e-2),Math.round(0),Math.round(2),Math.round(a/2-1));for(let e=i-1-2;e>0;e-=4)n.fillRect(Math.round(s+1),Math.round(e-2),Math.round(a/2-1),Math.round(2));for(let e=i+1+2;e<a;e+=4)n.fillRect(Math.round(0),Math.round(e),Math.round(a/2-1),Math.round(2));for(let e=s+1+2;e<a;e+=4)n.fillRect(Math.round(e),Math.round(i+1),Math.round(2),Math.round(a/2-1));e.imageSmoothingEnabled=!1,e.drawImage(o,t,l)};for(let e of n){let{cx:t,cy:l}=o(e),a=e.diameter/2;if("circle"===e.type){let o=document.createElement("canvas"),n=Math.ceil(e.diameter)+2;o.width=n,o.height=n;let s=o.getContext("2d");if(s){s.imageSmoothingEnabled=!1,s.fillStyle=e.color;let a=e.diameter/2,i=n/2;for(let e=0;e<n;e++)for(let t=0;t<n;t++)Math.sqrt((e-i)**2+(t-i)**2)<=a&&s.fillRect(e,t,1,1);r.imageSmoothingEnabled=!1,r.drawImage(o,t-e.diameter/2,l-e.diameter/2,e.diameter,e.diameter)}else r.fillStyle=e.color,r.beginPath(),r.arc(t,l,a,0,2*Math.PI),r.fill()}else"square"===e.type&&s(r,t-a,l-a,e.diameter,e.color)}let i=a.toDataURL("image/".concat(t)),d=document.createElement("a");d.href=i,d.download="drawing-board-".concat(Date.now(),".").concat(t),document.body.appendChild(d),d.click(),document.body.removeChild(d)};return(0,a.jsxs)("div",{className:"flex flex-col md:flex-row h-full gap-4",children:[(0,a.jsx)(c,{canvasSize:e,setCanvasSize:t,setGrid:o,grid:l,selectedColor:i,setSelectedColor:d,selectedShapeType:m,setSelectedShapeType:g,diameter:p,setDiameter:x,replaceColor:(e,t)=>{let l=e.toLowerCase(),a=t.toLowerCase();u.toLowerCase()===l&&f(a),s(e=>e.map(e=>e.color.toLowerCase()===l?{...e,color:a}:e))},resetCanvas:()=>{s([]),f("#FFFFFF")},exportCanvas:w}),(0,a.jsx)("div",{className:"flex-grow overflow-auto",children:(0,a.jsx)(h,{width:e.width,height:e.height,grid:l,shapes:n,addShape:(e,t,l)=>{s([...n,{id:Date.now(),type:m,cell:e,color:i,diameter:p,alignment:t,coordinates:l}])},deleteShape:e=>{s(n.filter(t=>t.id!==e))},backgroundColor:u})})]})}}}]);