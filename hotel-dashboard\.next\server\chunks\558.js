"use strict";exports.id=558,exports.ids=[558],exports.modules={27558:(e,l,t)=>{t.r(l),t.d(l,{default:()=>W});var o=t(60687),s=t(43210),i=t.n(s),n=t(55192),a=t(39390),r=t(24934),c=t(31158),d=t(121),g=t.n(d),u=t(70333),f=t(17544),h=t(94084),m=t.n(h);async function p(e,l,t=3e3){let o=Date.now();return new Promise((s,i)=>{let n=()=>{let i=e.innerText.includes("图表加载中..."),a=e.querySelector(".recharts-surface");a&&!i?(console.log(`Chart for block ${l} is ready.`),s()):Date.now()-o>t?(console.warn(`Timeout waiting for chart ${l} to render. isLoadingTextPresent: ${i}, hasRechartsSurface: ${!!a}`),s()):setTimeout(n,200)};n()})}async function x(e,l,t,o){try{console.log("[exportUtils] Starting export for ZIP. Selected block IDs:",t);let s=new(m()),i=t.length,n=0,a=0;for(let l of t){console.log(`[exportUtils] Processing block ${l}`);let t=e.querySelector(`[data-block-id="${l}"]`);if(!t){console.error(`[exportUtils] Block element not found for ID: ${l}. Skipping.`),s.file(`error_block-${l}_not_found.txt`,`DOM element for block ID ${l} was not found.`),n++,a++,o&&o(n/i*100);continue}try{console.log(`[exportUtils] Found block element for ${l}. Waiting for chart to be ready...`),await p(t,l),console.log(`[exportUtils] Chart for ${l} is considered ready. Generating image...`);let e=await g().toPng(t,{width:t.scrollWidth,height:t.scrollHeight,bgcolor:"#ffffff",style:{border:"none !important",outline:"none !important","box-shadow":"none !important","background-color":"#ffffff !important"},filter:e=>(e instanceof HTMLElement&&(e.style.border="none",e.style.outline="none",e.style.boxShadow="none"),!0),cacheBust:!0});console.log(`[exportUtils] Image generated for block ${l}.`);let o=e.split(",")[1];if(!o)throw Error(`Failed to extract base64 data for block ${l}`);let i=atob(o),n=new Uint8Array(i.length);for(let e=0;e<i.length;e++)n[e]=i.charCodeAt(e);s.file(`chart_block-${l}.png`,n),console.log(`[exportUtils] Added image for block ${l} to zip.`)}catch(e){console.error(`[exportUtils] Failed to generate or add image for block ${l}:`,e),s.file(`error_block-${l}_img_generation.txt`,`Failed to generate image for block ${l}: ${e instanceof Error?e.message:String(e)}`),a++}n++,o&&o(n/i*100),console.log(`[exportUtils] Finished processing block ${l}. Progress: ${n/i*100}%`)}if(0===n&&i>0)throw Error("No blocks were processed for export.");if(0===i){(0,u.oR)({title:"没有内容可导出",description:"没有选择任何数据块进行导出。",variant:"default"});return}console.log("[exportUtils] Generating zip file...");let r=await s.generateAsync({type:"blob"});if(console.log("[exportUtils] Zip file generated, size:",r.size),0===r.size)throw Error("Generated zip file is empty. This might happen if all image generations failed.");(0,f.saveAs)(r,`${l}.zip`),console.log("[exportUtils] Zip file saved."),a>0?(0,u.oR)({title:"导出部分完成",description:`${i-a} 个图表已导出，但有 ${a} 个图表导出失败。详情请查看ZIP包内的错误文件。`,variant:"default"}):(0,u.oR)({title:"导出成功",description:`已将 ${i} 个图表导出为压缩包。`})}catch(e){throw console.error("[exportUtils] Error exporting elements as images:",e),(0,u.oR)({title:"导出失败",description:e instanceof Error?e.message:"导出过程中发生错误",variant:"destructive"}),e}}var k=t(86287),y=t(14555),b=t(65822),N=t(96241);let w=s.forwardRef(({className:e,...l},t)=>(0,o.jsx)(y.bL,{className:(0,N.cn)("grid gap-2",e),...l,ref:t}));w.displayName=y.bL.displayName;let j=s.forwardRef(({className:e,...l},t)=>(0,o.jsx)(y.q7,{ref:t,className:(0,N.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...l,children:(0,o.jsx)(y.C1,{className:"flex items-center justify-center",children:(0,o.jsx)(b.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));j.displayName=y.q7.displayName;let v=({dataChunks:e,onSelectionChange:l,onStartExport:t})=>{console.log("[LogDisplayArea] Rendering. ProcessedDataChunks count:",e.length);let[d,g]=(0,s.useState)(""),[f,h]=(0,s.useState)([]),m=(0,s.useRef)(null),[p,y]=(0,s.useState)(!1),[b,N]=(0,s.useState)(0),{toast:v}=(0,u.dj)(),A=i().useRef(l);(0,s.useEffect)(()=>{A.current=l},[l]),(0,s.useEffect)(()=>{if(console.log("[LogDisplayArea] useEffect for auto-selection triggered. processedDataChunks count:",e.length,"current selectedBlockId:",d),0===e.length)""!==d&&(console.log("[LogDisplayArea] No data chunks, clearing selectedBlockId."),g(""));else if(d&&""!==d.trim()&&e.some(e=>e.block_id===d))console.log("[LogDisplayArea] Current selection is still valid:",d);else{console.log("[LogDisplayArea] Current selection is invalid or not set. Attempting to find first valid block.");let l=e.find(e=>e.block_id&&"string"==typeof e.block_id&&""!==e.block_id.trim());l?(console.log("[LogDisplayArea] Found first valid block. Setting selectedBlockId to:",l.block_id),g(l.block_id)):""!==d&&(console.warn("[LogDisplayArea] No valid block_id found in any processed chunks. Clearing selectedBlockId."),g(""))}},[e,d]),(0,s.useEffect)(()=>{if(d&&e.length>0){let l=e.filter(e=>e.block_id===d);A.current(l)}else A.current([])},[d,e]);let L=(0,s.useCallback)(e=>{console.log("[LogDisplayArea] handleBlockSelectionChange - START. blockId:",e),g(e),console.log("[LogDisplayArea] handleBlockSelectionChange - END.")},[]),D=(0,s.useCallback)(e=>{console.log("[LogDisplayArea] handleExportSelectionChange - START. blockId:",e),h(l=>{let t=l.includes(e)?l.filter(l=>l!==e):[...l,e];return console.log("[LogDisplayArea] handleExportSelectionChange - New selection:",t),t})},[]),F=(0,s.useCallback)(()=>{console.log("[LogDisplayArea] selectAllForExport - START");let l=e.map(e=>e.block_id);h(l),console.log("[LogDisplayArea] selectAllForExport - END. Selected all IDs:",l)},[e]),T=(0,s.useCallback)(()=>{console.log("[LogDisplayArea] deselectAllForExport - START"),h([]),console.log("[LogDisplayArea] deselectAllForExport - END")},[]),S=async()=>{if(console.log("handleExportAllImages called"),console.log("displayAreaRef:",m.current),console.log("processedDataChunks:",e),console.log("exportBlockIds:",f),!m.current){v({variant:"destructive",title:"错误",description:"显示区域容器未找到。"});return}if(!e||0===e.length){v({variant:"destructive",title:"错误",description:"没有可导出的内容。"});return}if(0===f.length){v({variant:"destructive",title:"错误",description:"请至少选择一个数据块进行导出。"});return}y(!0),N(0);try{let e=document.querySelector(".log-chart-container");if(!e)throw Error("找不到图表容器");let l=e.querySelectorAll("[data-block-id]");l.forEach(e=>{let l=e.getAttribute("data-block-id");l&&f.includes(l)?e.style.display="block":e.style.display="none"}),await new Promise(e=>setTimeout(e,1e3)),await x(e,`log-analysis-summary-${Date.now()}`,f,e=>{N(e)}),l.forEach(e=>{e.getAttribute("data-block-id")===d?e.style.display="block":e.style.display="none"})}catch(e){console.error("DisplayArea export failed in component:",e),v({variant:"destructive",title:"导出失败",description:e instanceof Error?e.message:"导出过程中发生错误。"})}finally{y(!1),N(0)}},C=e&&e.length>0;return(0,o.jsxs)(n.Zp,{className:"h-[450px] flex flex-col",children:[(0,o.jsxs)(n.aR,{className:"flex flex-row items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)(n.ZB,{children:"选择数据块进行分析"}),(0,o.jsx)(n.BT,{children:"从解析的日志文件中选择一个数据块以在图表中显示。"})]}),(0,o.jsx)(r.$,{onClick:S,disabled:p||!C||0===f.length,size:"sm",children:p?"导出中...":(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"导出选中图片"]})})]}),(0,o.jsx)(n.Wu,{className:"flex-1 overflow-hidden p-4",children:(0,o.jsxs)("div",{ref:m,className:"h-full flex flex-col",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,o.jsx)(r.$,{onClick:F,size:"sm",variant:"outline",children:"全选"}),(0,o.jsx)(r.$,{onClick:T,size:"sm",variant:"outline",children:"取消全选"}),(0,o.jsxs)("span",{className:"text-sm text-muted-foreground ml-2",children:["已选择导出: ",f.length," 项"]}),p&&(0,o.jsx)("div",{className:"flex-1 ml-4",children:(0,o.jsx)(k.k,{value:b,className:"h-1"})})]}),(0,o.jsx)("div",{className:"flex-1 overflow-y-auto min-h-0",children:(0,o.jsx)("div",{className:"space-y-2 p-2 border rounded-md",children:e.map(e=>(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)(w,{value:d,onValueChange:L,className:"flex items-center",children:(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(j,{value:e.block_id,id:`display-${e.block_id}`}),(0,o.jsx)(a.J,{htmlFor:`display-${e.block_id}`,className:"cursor-pointer",children:"显示"})]})}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"checkbox",id:`export-${e.block_id}`,checked:f.includes(e.block_id),onChange:()=>D(e.block_id),className:"h-4 w-4 rounded border-gray-300"}),(0,o.jsx)(a.J,{htmlFor:`export-${e.block_id}`,className:"cursor-pointer",children:`数据块 ${e.block_id} (胶厚: ${e.glue_thickness_values.length}, 准直: ${e.collimation_diff_values.length})`})]})]},e.block_id))})}),(!e||0===e.length)&&(0,o.jsx)("p",{className:"text-muted-foreground p-2",children:"暂无数据块可供分析或导出。"})]})})]})};var A=t(48482),L=t(41526),D=t(85168),F=t(27747),T=t(9920),S=t(38246),C=t(57359),U=t(50326),$=t(46245);let E=e=>{if(!e)return NaN;try{let l=e.replace(",",".");if(!l.includes("T")&&l.includes(" ")){let e=l.split(" ");e.length>1&&e[0].includes("-")&&e[1].includes(":")&&(l=e[0]+"T"+e.slice(1).join(" "))}let t=new Date(l),o=t.getTime();if(isNaN(o)){let l=e.replace(",",".");o=(t=new Date(l)).getTime()}if(isNaN(o)&&(o=(t=new Date(e)).getTime()),isNaN(o))return console.warn(`[LogChartViewHelper] Failed to parse timestamp: "${e}"`),NaN;return o}catch(l){return console.error(`[LogChartViewHelper] Error parsing timestamp: "${e}"`,l),NaN}},R=e=>{let l=[];(e.glue_thickness_values||[]).forEach(e=>{let t=E(e.timestamp);isNaN(t)||l.push({time:t,glueThickness:e.value,collimationDiff:null})}),(e.collimation_diff_values||[]).forEach(e=>{let t=E(e.timestamp);isNaN(t)||l.push({time:t,glueThickness:null,collimationDiff:e.value})}),l.sort((e,l)=>e.time-l.time);let t=[];return l.forEach(e=>{let l=t.find(l=>l.time===e.time);l?(null!==e.glueThickness&&(l.glueThickness=e.glueThickness),null!==e.collimationDiff&&(l.collimationDiff=e.collimationDiff)):t.push({...e})}),t},B=({chunk:e,isChartReady:l})=>{let{chartDataForThisBlock:t,eventPointsForThisBlock:i,timeDomainForThisBlock:n,glueDomainForThisBlock:a,collimationDomainForThisBlock:r,hasDataForThisBlock:c}=(0,s.useMemo)(()=>{if(!e)return{chartDataForThisBlock:[],eventPointsForThisBlock:[],timeDomainForThisBlock:[Date.now()-36e5,Date.now()],glueDomainForThisBlock:[0,1e3],collimationDomainForThisBlock:[0,.1],hasDataForThisBlock:!1};let l=R(e),t=(e.valve_open_events||[]).map(e=>({time:E(e.timestamp),value:0,label:`打开放气阀 (${e.timestamp.split(" ")[1]||e.timestamp})`})).filter(e=>!isNaN(e.time));t.sort((e,l)=>e.time-l.time);let o=[Date.now()-36e5,Date.now()];if(l.length>0){let e=l.map(e=>e.time).filter(e=>!isNaN(e));e.length>0&&(o=[Math.min(...e),Math.max(...e)])}o[0]===o[1]&&(o[1]=o[0]+36e5);let s=l.map(e=>e.glueThickness).filter(e=>null!==e&&!isNaN(e)),i=[0,1e3];s.length>0&&(i=[Math.min(...s),Math.max(...s)]),i[0]===i[1]&&(i[1]=i[0]+10);let n=l.map(e=>e.collimationDiff).filter(e=>null!==e&&!isNaN(e)),a=[0,.1];return n.length>0&&(a=[Math.min(...n),Math.max(...n)]),a[0]===a[1]&&(a[1]=a[0]+.01),{chartDataForThisBlock:l,eventPointsForThisBlock:t,timeDomainForThisBlock:o,glueDomainForThisBlock:i,collimationDomainForThisBlock:a,hasDataForThisBlock:l.length>0}},[e]);return c&&l?(0,o.jsx)(A.u,{width:"100%",height:"100%",children:(0,o.jsxs)(L.X,{data:t,margin:{top:20,right:40,left:30,bottom:20},children:[(0,o.jsx)(D.d,{strokeDasharray:"3 3"}),(0,o.jsx)(F.W,{dataKey:"time",domain:n,type:"number",tickFormatter:e=>new Date(e).toLocaleTimeString(),allowDuplicatedCategory:!1}),(0,o.jsx)(T.h,{yAxisId:"glue",orientation:"left",domain:a,type:"number",stroke:"#8884d8",label:{value:"胶厚 (μm)",angle:-90,position:"insideLeft",offset:-5,style:{fill:"#8884d8",textAnchor:"middle"}},tickFormatter:e=>e.toFixed(2),width:70}),(0,o.jsx)(T.h,{yAxisId:"collimation",orientation:"right",domain:r,type:"number",stroke:"#82ca9d",label:{value:"准直差",angle:90,position:"insideRight",offset:-15,style:{fill:"#82ca9d",textAnchor:"middle"}},tickFormatter:e=>e.toFixed(3),width:80}),(0,o.jsx)(S.m,{labelFormatter:e=>new Date(e).toLocaleString(),formatter:(e,l)=>"number"!=typeof e?[e,l]:"glueThickness"===l?[e.toFixed(2)+" μm","胶厚"]:"collimationDiff"===l?[e.toFixed(3),"准直差"]:[e,l],contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",padding:"8px"}}),(0,o.jsx)(C.s,{verticalAlign:"top",height:36,wrapperStyle:{paddingBottom:"10px"}}),(0,o.jsx)(U.N,{yAxisId:"glue",type:"monotone",dataKey:"glueThickness",name:"胶厚",stroke:"#8884d8",strokeWidth:2,dot:{r:2},activeDot:{r:5},isAnimationActive:!1,connectNulls:!0}),(0,o.jsx)(U.N,{yAxisId:"collimation",type:"monotone",dataKey:"collimationDiff",name:"准直差",stroke:"#82ca9d",strokeWidth:2,dot:{r:2},activeDot:{r:5},isAnimationActive:!1,connectNulls:!0}),i.map((l,t)=>(0,o.jsx)($.e,{x:l.time,stroke:"rgba(255,0,0,0.7)",yAxisId:"glue",strokeDasharray:"4 4",label:{value:l.label,position:"insideTopRight",fill:"rgba(255,0,0,0.7)",fontSize:10}},`event-${e.block_id}-${t}`))]})}):(0,o.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:l?"此数据块无有效图表数据。":"图表加载中..."})},_=function({dataChunks:e,selectedBlockIds:l,onBlockSelect:t}){let i=(0,s.useRef)(null),[a,r]=(0,s.useState)(!1),[c,d]=(0,s.useState)(!1),{toast:g}=(0,u.dj)();return(0,s.useMemo)(()=>(console.log("LogChartView - hasChartData check:",{dataChunks:e}),e&&Array.isArray(e))?e.length>0:(console.log("LogChartView - dataChunks is invalid:",e),!1),[e]),(0,s.useMemo)(()=>(console.log("LogChartView - chartData calculation:",{dataChunks:e,selectedBlockIds:l}),e&&Array.isArray(e))?e.filter(e=>(console.log("Filtering chunk:",{chunk:e,selectedBlockIds:l}),l.includes(e.block_id))).flatMap(e=>(console.log("Processing chunk for chart data:",e),e.data&&Array.isArray(e.data))?e.data.map(l=>({name:l.name,value:l.value,type:l.type,block_id:e.block_id})):(console.log("Invalid chunk data:",e.data),[])):(console.log("LogChartView - dataChunks is invalid in chartData:",e),[]),[e,l]),(0,o.jsx)("div",{ref:i,className:"space-y-6 log-chart-container",children:e.map(e=>(0,o.jsxs)(n.Zp,{"data-block-id":e.block_id,className:l.includes(e.block_id)?"block":"hidden",children:[(0,o.jsx)(n.aR,{className:"flex flex-row justify-between items-center",children:(0,o.jsxs)(n.ZB,{children:["数据块 ",e.block_id]})}),(0,o.jsx)(n.Wu,{className:"p-0",children:(0,o.jsx)("div",{className:"w-full h-[400px] min-h-[400px]",style:{width:"100%",height:"400px"},children:(0,o.jsx)(B,{chunk:e,isChartReady:c})})})]},e.block_id))})};var P=t(10750);let I=({onProcessingStart:e,onDataProcessed:l,onError:t,disabled:i=!1})=>{let[c,d]=(0,s.useState)(null),[g,f]=(0,s.useState)(!1),[h,m]=(0,s.useState)(null),[p,x]=(0,s.useState)("未选择文件"),k=(0,s.useRef)(null),y=(0,s.useRef)(l),b=(0,s.useRef)(t),N=(0,s.useRef)(e);(0,s.useEffect)(()=>{y.current=l},[l]),(0,s.useEffect)(()=>{b.current=t},[t]),(0,s.useEffect)(()=>{N.current=e},[e]),(0,s.useEffect)(()=>(console.log("[LogFileUpload] useEffect[] - START: Component did mount / Worker setup effect running."),console.log("[LogFileUpload] useEffect[] - window is undefined, Worker NOT created (SSR or other env)."),()=>{console.log("[LogFileUpload] useEffect[] - CLEANUP FUNCTION START: Component will unmount / Worker teardown.");let e=k.current;if(e){console.log("[LogFileUpload] Cleanup: Attempting to terminate worker...",e);try{e.onmessage=null,e.onerror=null,e.terminate(),console.log("[LogFileUpload] Cleanup: Worker terminated successfully.")}catch(e){console.error("[LogFileUpload] Cleanup: Error terminating worker:",e)}k.current=null,console.log("[LogFileUpload] Cleanup: workerRef.current set to null.")}else console.log("[LogFileUpload] Cleanup: No worker instance found in ref to terminate.");console.log("[LogFileUpload] useEffect[] - CLEANUP FUNCTION END.")}),[]);let w=(0,s.useCallback)(async()=>{if(console.log("[LogFileUpload] handleUpload - START."),!c){(0,u.oR)({title:"没有选择文件",description:"请选择一个日志文件进行上传。",variant:"destructive"}),console.log("[LogFileUpload] handleUpload - No file selected, returning.");return}if(console.log("[LogFileUpload] handleUpload - Setting isProcessing to true, calling onProcessingStartRef.current()."),f(!0),N.current(),m(null),(0,u.oR)({title:"开始处理",description:`正在处理文件: ${c.name}`}),!k.current){(0,u.oR)({title:"Worker 未初始化",description:"Web Worker 尚未准备好，请稍后再试。",variant:"destructive"}),b.current("Worker not initialized"),f(!1),m(null),console.log("[LogFileUpload] handleUpload - Worker not initialized, returning.");return}console.log("[LogFileUpload] handleUpload - Worker instance exists, proceeding with file processing.");try{let e=await c.arrayBuffer();if(!e||0===e.byteLength){f(!1),b.current("文件为空或读取失败。"),m(null),(0,u.oR)({title:"错误",description:"文件为空或读取失败。",variant:"destructive"});return}let l="",t="UTF-8",o=!1,s=new Uint8Array(e);if(console.log("[LogFileUpload] uInt8ArrayForDetection type:",typeof s,"instanceof Uint8Array:",s instanceof Uint8Array,"length:",s.length),s.length>0&&console.log("[LogFileUpload] uInt8ArrayForDetection sample (first 20 bytes):",s.slice(0,20)),s.length>10)try{console.log("[LogFileUpload] Attempting jschardet.detect...");let e=Buffer.from(s),l=P.detect(e);console.log("[LogFileUpload] jschardet.detect result:",l),l&&l.encoding&&l.confidence>.2?(t=l.encoding.toUpperCase(),console.log(`[LogFileUpload] jschardet confidently detected encoding: ${t} with confidence: ${l.confidence}`),["GBK","GB2312","GB18030","BIG5"].includes(t)?t="gbk":["UTF-8","ASCII"].includes(t)||(console.warn(`[LogFileUpload] Detected encoding ${t} is not directly handled by TextDecoder as is, defaulting to GBK for now.`),t="gbk")):(console.warn("[LogFileUpload] jschardet could not detect encoding with sufficient confidence. Will try fallbacks. Result:",l),o=!0)}catch(e){console.error("[LogFileUpload] Error during jschardet.detect:",e.message,e),o=!0,e.message&&e.message.includes("aBuf.slice(...).split is not a function")}else console.warn("[LogFileUpload] File too small for reliable encoding detection. Will try fallbacks."),o=!0;try{if(o)throw Error("jschardet failed or was not confident, proceeding to fallbacks.");console.log(`[LogFileUpload] Attempting to decode with detected/chosen encoding: ${t}`),l=new TextDecoder(t.toLowerCase()).decode(e)}catch(o){console.warn(`[LogFileUpload] Decoding with ${t} failed (or skipped). Error: ${o.message}`);try{t="gbk",console.log(`[LogFileUpload] Attempting to decode with fallback: ${t}`),l=new TextDecoder(t.toLowerCase()).decode(e)}catch(o){console.warn(`[LogFileUpload] Decoding with ${t} failed. Error: ${o.message}`),t="utf-8",console.log(`[LogFileUpload] Attempting to decode with last resort: ${t}`),l=new TextDecoder(t.toLowerCase()).decode(e)}}console.log("[LogFileUpload] Successfully decoded file content using encoding:",t),console.log("[LogFileUpload] handleUpload - Posting message to worker with fileContent."),k.current.postMessage(l)}catch(l){console.error("[LogFileUpload] Final catch after all processing attempts in handleUpload:",l),f(!1),m(null);let e=l.message.includes("aBuf.slice(...).split is not a function")?l.message:`文件解码失败: ${l.message}. 请确保文件是文本格式且编码受支持。`;b.current(e),(0,u.oR)({title:"解码失败",description:e,variant:"destructive"});return}console.log("[LogFileUpload] handleUpload - END (processing will continue in worker).")},[c]);return(0,o.jsxs)(n.Zp,{className:"w-full max-w-md h-[450px] flex flex-col",children:[(0,o.jsxs)(n.aR,{children:[(0,o.jsx)(n.ZB,{children:"胶合日志文件上传"}),(0,o.jsx)(n.BT,{children:"选择一个日志文件进行分析。"})]}),(0,o.jsxs)(n.Wu,{className:"space-y-6 p-6 flex-grow flex flex-col overflow-y-auto",children:[(0,o.jsxs)("div",{className:"flex flex-col items-start gap-1.5",children:[" ",(0,o.jsx)(a.J,{htmlFor:"hidden-log-file",className:"text-sm font-medium",children:"选择文件"})," ",(0,o.jsxs)("label",{htmlFor:"hidden-log-file",className:`w-full h-10 rounded-md border border-input flex items-center text-sm ring-offset-background ${i?"cursor-not-allowed bg-muted":"cursor-pointer focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"}`,children:[(0,o.jsx)("span",{className:`bg-primary text-primary-foreground h-full flex items-center justify-center px-4 rounded-l-md whitespace-nowrap ${i?"opacity-50":""}`,children:"上传文件"}),(0,o.jsx)("span",{className:"flex-grow h-full px-3 py-2 flex items-center text-muted-foreground overflow-hidden",children:(0,o.jsx)("span",{className:"truncate",children:p})})]}),(0,o.jsx)("input",{id:"hidden-log-file",type:"file",className:"sr-only",onChange:e=>{if(e.target.files&&e.target.files[0]){let l=e.target.files[0];d(l),x(l.name),m(null)}else d(null),x("未选择文件"),m(null)},disabled:g||i,accept:".log,.txt,application/octet-stream"})]}),c&&(0,o.jsxs)("div",{className:"space-y-2 text-sm pt-3",children:[(0,o.jsxs)("p",{className:"text-muted-foreground",children:["已选择: ",c.name," (",(c.size/1024).toFixed(2)," KB)"]}),h&&!g&&(0,o.jsxs)("div",{className:"border-t pt-3 mt-3 space-y-1 text-xs text-muted-foreground",children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("span",{className:"font-semibold text-foreground",children:"总行数:"})," ",null!==h.totalLines?h.totalLines.toLocaleString():"N/A"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("span",{className:"font-semibold text-foreground",children:"起始时间:"})," ",h.firstLogTime||"N/A"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("span",{className:"font-semibold text-foreground",children:"结束时间:"})," ",h.lastLogTime||"N/A"]})]})]})]}),(0,o.jsx)(n.wL,{children:(0,o.jsx)(r.$,{onClick:w,disabled:!c||g||i,className:"w-full",children:g?(0,o.jsx)(o.Fragment,{children:"处理中..."}):"上传并分析"})})]})};function W(){let[e,l]=(0,s.useState)([]),[t,i]=(0,s.useState)([]),[a,r]=(0,s.useState)(!1),[c,d]=(0,s.useState)(null),[g,f]=(0,s.useState)(!1),[h,m]=(0,s.useState)([]),p=(0,s.useRef)(null),{toast:x}=(0,u.dj)(),k=l=>{console.log("[LogAnalysisPage] handleBlockSelect - START. Received blockId:",l);let t=e.find(e=>e.block_id===l);t&&i([t]),console.log("[LogAnalysisPage] handleBlockSelect - END")},y=(0,s.useMemo)(()=>(console.log("[LogAnalysisPage] Recalculating chartDataForView. Selected blocks count:",t.length),t),[t]);return console.log("[LogAnalysisPage] Rendering. isLoading:",a,"error:",c,"dataChunks count:",e.length,"selectedBlocksForChart count:",t.length,"chartDataForView count:",y.length),(0,o.jsxs)("div",{className:"flex flex-col h-full p-4 gap-4",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold",children:"日志分析"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsx)("div",{className:"md:col-span-1",children:(0,o.jsx)(I,{onProcessingStart:()=>{console.log("[LogAnalysisPage] handleProcessingStart - START"),r(!0),d(null),i([]),console.log("[LogAnalysisPage] handleProcessingStart - END")},onDataProcessed:e=>{console.log("[LogAnalysisPage] handleDataProcessed - START. Received data count:",e.length),l(e.map(e=>({...e,data:[]}))),r(!1),x({title:"数据已加载",description:`成功处理了 ${e.length} 个数据块。`}),console.log("[LogAnalysisPage] handleDataProcessed - END")},onError:e=>{console.log("[LogAnalysisPage] handleError - START. Received errorMessage:",e),d(e),r(!1),console.log("[LogAnalysisPage] handleError - END")},disabled:a})}),(0,o.jsx)("div",{className:"md:col-span-2",children:(0,o.jsx)(v,{dataChunks:e,onSelectionChange:e=>{console.log("[LogAnalysisPage] handleBlockSelectionChanged - START. Received selectedBlocks count:",e.length),i(e),console.log("[LogAnalysisPage] handleBlockSelectionChanged - END")},onStartExport:l=>{console.log("[LogAnalysisPage] initiateExportProcess - START. exportIds:",l);let t=e.filter(e=>l.includes(e.block_id));if(0===t.length){x({title:"导出错误",description:"没有找到要导出的数据块。",variant:"destructive"});return}m(t),f(!0)}})})]}),(0,o.jsxs)("div",{className:"flex-grow mt-4",children:[a&&(0,o.jsx)(n.Zp,{className:"h-full flex items-center justify-center",children:(0,o.jsx)(n.Wu,{className:"text-center",children:(0,o.jsx)("p",{className:"text-muted-foreground",children:"正在处理文件..."})})}),c&&(0,o.jsx)(n.Zp,{className:"h-full flex items-center justify-center bg-destructive/10",children:(0,o.jsxs)(n.Wu,{className:"text-center",children:[(0,o.jsx)("p",{className:"text-destructive font-semibold",children:"发生错误"}),(0,o.jsx)("p",{className:"text-muted-foreground",children:c})]})}),!a&&!c&&(0,o.jsx)("div",{ref:p,className:"h-full",children:g?(0,o.jsx)(_,{dataChunks:h,selectedBlockIds:h.map(e=>e.block_id),onBlockSelect:k}):y.length>0?(0,o.jsx)(_,{dataChunks:y,selectedBlockIds:y.map(e=>e.block_id),onBlockSelect:k}):(0,o.jsx)(n.Zp,{className:"h-full flex items-center justify-center",children:(0,o.jsx)(n.Wu,{className:"text-center",children:(0,o.jsx)("p",{className:"text-muted-foreground",children:e.length>0?"请从左侧选择数据块以显示图表":"请先上传日志文件"})})})})]})]})}},86287:(e,l,t)=>{t.d(l,{k:()=>a});var o=t(60687),s=t(43210),i=t(14819),n=t(96241);let a=s.forwardRef(({className:e,value:l,...t},s)=>(0,o.jsx)(i.bL,{ref:s,className:(0,n.cn)("relative h-2 w-full overflow-hidden rounded-full bg-gray-200",e),...t,children:(0,o.jsx)(i.C1,{className:"h-full w-full flex-1 bg-blue-500 transition-all",style:{transform:`translateX(-${100-(l||0)}%)`}})}));a.displayName=i.bL.displayName}};