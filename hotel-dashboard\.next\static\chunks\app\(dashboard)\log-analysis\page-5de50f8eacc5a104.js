(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[964],{14503:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f,oR:()=>u});var s=r(12115);let a=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},47043:(e,t,r)=>{Promise.resolve().then(r.bind(r,49587))},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(52596),a=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},82284:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var s=r(12115),a=r(46081),n=r(6101),o=r(99708),i=r(95155);function l(e){let t=e+"CollectionProvider",[r,l]=(0,a.A)(t),[d,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:r}=e,a=s.useRef(null),n=s.useRef(new Map).current;return(0,i.jsx)(d,{scope:t,itemMap:n,collectionRef:a,children:r})};u.displayName=t;let f=e+"CollectionSlot",p=s.forwardRef((e,t)=>{let{scope:r,children:s}=e,a=c(f,r),l=(0,n.s)(t,a.collectionRef);return(0,i.jsx)(o.DX,{ref:l,children:s})});p.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=s.forwardRef((e,t)=>{let{scope:r,children:a,...l}=e,d=s.useRef(null),u=(0,n.s)(t,d),f=c(m,r);return s.useEffect(()=>(f.itemMap.set(d,{ref:d,...l}),()=>void f.itemMap.delete(d))),(0,i.jsx)(o.DX,{[v]:"",ref:u,children:a})});return g.displayName=m,[{Provider:u,Slot:p,ItemSlot:g},function(t){let r=c(e+"CollectionConsumer",t);return s.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(95155),a=r(12115),n=r(40968),o=r(74466),i=r(53999);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.b,{ref:t,className:(0,i.cn)(l(),r),...a})});d.displayName=n.b.displayName},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>u});var s=r(95155),a=r(12115),n=r(53999);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});o.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>l});var s=r(95155),a=r(12115),n=r(99708),o=r(74466),i=r(53999);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:o,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(l({variant:a,size:o,className:r})),ref:t,...c})});d.displayName="Button"}},e=>{var t=t=>e(e.s=t);e.O(0,[650,433,587,441,684,358],()=>t(47043)),_N_E=e.O()}]);