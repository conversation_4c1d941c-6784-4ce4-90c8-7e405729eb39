(()=>{var e={};e.id=813,e.ids=[813],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15429:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>m,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{POST:()=>x});var o=t(96559),n=t(48088),a=t(37719),i=t(32190);let u=require("child_process");var p=t(33873),c=t.n(p),d=t(29021),l=t.n(d);async function x(e){let r=null;try{let t=(await e.json()).query;if(!t||"string"!=typeof t)return i.NextResponse.json({error:"Query is required and must be a string."},{status:400});let s=c().resolve(process.cwd(),"..","csharp-wrapper","bin","Debug","net48"),o=c().join(s,"WrapperApp.exe"),n=process.env.TEMP||process.env.TMP||"C:\\Windows\\Temp";r=c().join(n,"db_query_result.json"),l().existsSync(r)&&l().unlinkSync(r);let a=t.replace(/"/g,'\\"'),p=`"${o}" "${a}" --silent`;console.log(`Executing command: ${p} in ${s}`);let d=`& "${o}" "${a}" --silent 2>$null`,{stdout:x,stderr:g}=await new Promise((e,r)=>{let t=(0,u.spawn)("powershell",["-Command",d],{cwd:s,stdio:["pipe","pipe","pipe"]}),o="",n="";t.stdout.on("data",e=>{o+=e.toString()}),t.stderr.on("data",e=>{n+=e.toString()}),t.on("close",t=>{0!==t?(console.log(`Process stderr: ${n}`),console.log(`Process stdout: ${o}`),r(Error(`Process exited with code ${t}. Stderr: ${n}. Stdout: ${o}`))):e({stdout:o,stderr:n})}),t.on("error",e=>{r(e)})});if(console.log(`Process completed. Checking for file: ${r}`),!l().existsSync(r))return console.error(`File not found: ${r}`),i.NextResponse.json({error:"Unable to find result file from C# wrapper.",details:`Expected file: ${r}`,stdout:x.substring(0,500),stderr:g.substring(0,500)},{status:500});let f=l().readFileSync(r,"utf8"),y=JSON.parse(f);return console.log(`Successfully read data from: ${r}`),i.NextResponse.json({success:!0,data:y,filePath:r})}catch(e){if(console.error("API Error:",e),r&&l().existsSync(r))try{l().unlinkSync(r)}catch(e){console.error("Error cleaning up file:",e)}return i.NextResponse.json({error:e instanceof Error?e.message:"An unknown error occurred."},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/database-query/route",pathname:"/api/database-query",filename:"route",bundlePath:"app/api/database-query/route"},resolvedPagePath:"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\api\\database-query\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:m}=g;function h(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[719,580],()=>t(15429));module.exports=s})();