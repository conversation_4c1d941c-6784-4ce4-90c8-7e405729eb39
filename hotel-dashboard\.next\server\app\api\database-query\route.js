/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/database-query/route";
exports.ids = ["app/api/database-query/route"];
exports.modules = {

/***/ "(rsc)/./app/api/database-query/route.ts":
/*!*****************************************!*\
  !*** ./app/api/database-query/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function POST(request) {\n    let filePath = null;\n    try {\n        const body = await request.json();\n        const sqlQuery = body.query;\n        if (!sqlQuery || typeof sqlQuery !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Query is required and must be a string.'\n            }, {\n                status: 400\n            });\n        }\n        // IMPORTANT: Sanitize or validate sqlQuery here if necessary to prevent SQL injection,\n        // even though it's passed as an argument. For now, we assume the query is safe.\n        const wrapperAppDir = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');\n        const wrapperAppPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(wrapperAppDir, 'WrapperApp.exe');\n        // Escape quotes and other special characters for the command line\n        const escapedQuery = sqlQuery.replace(/\"/g, '\\\\\"');\n        const command = `\"${wrapperAppPath}\" \"${escapedQuery}\" --silent`;\n        console.log(`Executing command: ${command} in ${wrapperAppDir}`);\n        // Use PowerShell to redirect stderr to null and capture stdout\n        const powershellCommand = `& \"${wrapperAppPath}\" \"${escapedQuery}\" --silent 2>$null`;\n        const { stdout, stderr } = await new Promise((resolve, reject)=>{\n            const child = (0,child_process__WEBPACK_IMPORTED_MODULE_1__.spawn)('powershell', [\n                '-Command',\n                powershellCommand\n            ], {\n                cwd: wrapperAppDir,\n                stdio: [\n                    'pipe',\n                    'pipe',\n                    'pipe'\n                ]\n            });\n            let stdoutData = '';\n            let stderrData = '';\n            child.stdout.on('data', (data)=>{\n                stdoutData += data.toString();\n            });\n            child.stderr.on('data', (data)=>{\n                stderrData += data.toString();\n            });\n            child.on('close', (code)=>{\n                if (code !== 0) {\n                    reject(new Error(`Process exited with code ${code}`));\n                } else {\n                    resolve({\n                        stdout: stdoutData,\n                        stderr: stderrData\n                    });\n                }\n            });\n            child.on('error', (error)=>{\n                reject(error);\n            });\n        });\n        if (stderr) {\n            console.error(`C# Wrapper Error: ${stderr}`);\n            // Try to parse stdout for a JSON error message first\n            try {\n                const errorJson = JSON.parse(stdout);\n                if (errorJson.error) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Wrapper Error: ${errorJson.error}`,\n                        details: stderr\n                    }, {\n                        status: 500\n                    });\n                }\n            } catch (e) {\n                // If stdout is not a JSON error, return the raw stderr\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'An error occurred in the C# wrapper.',\n                    details: stderr\n                }, {\n                    status: 500\n                });\n            }\n        }\n        // Clean the output by removing WebSocket binary data and keeping only text lines\n        const cleanOutput = stdout.split('\\n').filter((line)=>{\n            // Remove lines that contain binary data (hex dumps)\n            if (line.match(/^[0-9a-fA-F]{4,8}\\s+[0-9a-fA-F\\s]+/)) return false;\n            // Remove lines with WebSocket protocol data\n            if (line.includes('GET /ws HTTP') || line.includes('WebSocket') || line.includes('Upgrade:')) return false;\n            // Remove lines with only binary characters or control characters\n            if (line.match(/^[\\x00-\\x1F\\x7F-\\xFF\\s]*$/)) return false;\n            // Keep lines that look like file paths\n            if (line.match(/[A-Za-z]:\\\\.*\\.json/)) return true;\n            // Keep other text lines that might be useful\n            return line.trim().length > 0 && line.match(/^[A-Za-z0-9\\\\:._\\-\\s]+$/);\n        }).join('\\n');\n        console.log(`Raw stdout length: ${stdout.length}, Cleaned length: ${cleanOutput.length}`);\n        console.log(`Cleaned output: ${cleanOutput}`);\n        // Try multiple regex patterns to find the file path\n        const patterns = [\n            /[A-Z]:[\\\\\\/][^<>:\"|?*\\r\\n\\x00-\\x1F]*\\.json/gi,\n            /[A-Z]:\\\\\\\\[^<>:\"|?*\\r\\n\\x00-\\x1F]*\\.json/gi,\n            /([A-Z]:[\\\\\\/](?:(?![<>:\"|?*\\r\\n\\x00-\\x1F]).)*\\.json)/gi,\n            /[A-Z]:[\\\\\\/]Users[\\\\\\/][^<>:\"|?*\\r\\n\\x00-\\x1F]*\\.json/gi\n        ];\n        let foundPath = null;\n        for (const pattern of patterns){\n            const matches = cleanOutput.match(pattern);\n            if (matches && matches.length > 0) {\n                console.log(`Pattern ${pattern} found matches: ${matches.join(', ')}`);\n                // Take the first valid-looking path\n                for (const match of matches){\n                    if (match.includes('Temp') && match.endsWith('.json')) {\n                        foundPath = match;\n                        break;\n                    }\n                }\n                if (foundPath) break;\n            }\n        }\n        if (!foundPath) {\n            console.error(`Could not extract file path from stdout. Raw output: ${stdout}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无法从C#包装器的输出中提取文件路径。'\n            }, {\n                status: 500\n            });\n        }\n        filePath = foundPath;\n        console.log(`Extracted file path: ${filePath}`);\n        if (!filePath) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无法从C#包装器的输出中提取文件路径。'\n            }, {\n                status: 500\n            });\n        }\n        if (!fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(filePath) || !fs__WEBPACK_IMPORTED_MODULE_3___default().statSync(filePath).isFile()) {\n            console.error(`File not found or is not a file: ${filePath}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Temporary data file not found.',\n                path: filePath\n            }, {\n                status: 404\n            });\n        }\n        const fileContent = fs__WEBPACK_IMPORTED_MODULE_3___default().readFileSync(filePath, 'utf-8');\n        const result = JSON.parse(fileContent);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An internal server error occurred.',\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        if (filePath && fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(filePath)) {\n            try {\n                fs__WEBPACK_IMPORTED_MODULE_3___default().unlinkSync(filePath);\n                console.log(`Successfully deleted temporary file: ${filePath}`);\n            } catch (unlinkError) {\n                console.error(`Failed to delete temporary file: ${filePath}`, unlinkError);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/database-query/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_pycode_support_chart2_hotel_dashboard_app_api_database_query_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/database-query/route.ts */ \"(rsc)/./app/api/database-query/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/database-query/route\",\n        pathname: \"/api/database-query\",\n        filename: \"route\",\n        bundlePath: \"app/api/database-query/route\"\n    },\n    resolvedPagePath: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\api\\\\database-query\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_pycode_support_chart2_hotel_dashboard_app_api_database_query_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();