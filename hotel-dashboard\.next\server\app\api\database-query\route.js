/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/database-query/route";
exports.ids = ["app/api/database-query/route"];
exports.modules = {

/***/ "(rsc)/./app/api/database-query/route.ts":
/*!*****************************************!*\
  !*** ./app/api/database-query/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function POST(request) {\n    let filePath = null;\n    try {\n        const body = await request.json();\n        const sqlQuery = body.query;\n        if (!sqlQuery || typeof sqlQuery !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Query is required and must be a string.'\n            }, {\n                status: 400\n            });\n        }\n        const wrapperAppDir = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');\n        const wrapperAppPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(wrapperAppDir, 'WrapperApp.exe');\n        // Use fixed file path instead of extracting from stdout\n        const tempDir = process.env.TEMP || process.env.TMP || 'C:\\\\Windows\\\\Temp';\n        filePath = path__WEBPACK_IMPORTED_MODULE_2___default().join(tempDir, 'db_query_result.json');\n        // Delete existing file if it exists\n        if (fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(filePath)) {\n            fs__WEBPACK_IMPORTED_MODULE_3___default().unlinkSync(filePath);\n        }\n        // Escape quotes and other special characters for the command line\n        const escapedQuery = sqlQuery.replace(/\"/g, '\\\\\"');\n        const command = `\"${wrapperAppPath}\" \"${escapedQuery}\" --silent`;\n        console.log(`Executing command: ${command} in ${wrapperAppDir}`);\n        // Use PowerShell to redirect stderr to null\n        const powershellCommand = `& \"${wrapperAppPath}\" \"${escapedQuery}\" --silent 2>$null`;\n        const { stdout, stderr } = await new Promise((resolve, reject)=>{\n            const child = (0,child_process__WEBPACK_IMPORTED_MODULE_1__.spawn)('powershell', [\n                '-Command',\n                powershellCommand\n            ], {\n                cwd: wrapperAppDir,\n                stdio: [\n                    'pipe',\n                    'pipe',\n                    'pipe'\n                ]\n            });\n            let stdoutData = '';\n            let stderrData = '';\n            child.stdout.on('data', (data)=>{\n                stdoutData += data.toString();\n            });\n            child.stderr.on('data', (data)=>{\n                stderrData += data.toString();\n            });\n            child.on('close', (code)=>{\n                if (code !== 0) {\n                    reject(new Error(`Process exited with code ${code}`));\n                } else {\n                    resolve({\n                        stdout: stdoutData,\n                        stderr: stderrData\n                    });\n                }\n            });\n            child.on('error', (error)=>{\n                reject(error);\n            });\n        });\n        console.log(`Process completed. Checking for file: ${filePath}`);\n        // Check if the fixed file exists\n        if (!fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(filePath)) {\n            console.error(`File not found: ${filePath}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unable to find result file from C# wrapper.',\n                details: `Expected file: ${filePath}`,\n                stdout: stdout.substring(0, 500),\n                stderr: stderr.substring(0, 500)\n            }, {\n                status: 500\n            });\n        }\n        // Read the JSON file\n        const jsonContent = fs__WEBPACK_IMPORTED_MODULE_3___default().readFileSync(filePath, 'utf8');\n        const data = JSON.parse(jsonContent);\n        console.log(`Successfully read data from: ${filePath}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data,\n            filePath: filePath\n        });\n    } catch (error) {\n        console.error('API Error:', error);\n        // Clean up the file if it exists\n        if (filePath && fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(filePath)) {\n            try {\n                fs__WEBPACK_IMPORTED_MODULE_3___default().unlinkSync(filePath);\n            } catch (cleanupError) {\n                console.error('Error cleaning up file:', cleanupError);\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : 'An unknown error occurred.'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/database-query/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_pycode_support_chart2_hotel_dashboard_app_api_database_query_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/database-query/route.ts */ \"(rsc)/./app/api/database-query/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/database-query/route\",\n        pathname: \"/api/database-query\",\n        filename: \"route\",\n        bundlePath: \"app/api/database-query/route\"\n    },\n    resolvedPagePath: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\api\\\\database-query\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_pycode_support_chart2_hotel_dashboard_app_api_database_query_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();