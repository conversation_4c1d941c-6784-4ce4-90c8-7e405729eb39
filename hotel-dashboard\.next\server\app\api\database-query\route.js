/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/database-query/route";
exports.ids = ["app/api/database-query/route"];
exports.modules = {

/***/ "(rsc)/./app/api/database-query/route.ts":
/*!*****************************************!*\
  !*** ./app/api/database-query/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function POST(request) {\n    let filePath = null;\n    try {\n        const body = await request.json();\n        const sqlQuery = body.query;\n        if (!sqlQuery || typeof sqlQuery !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Query is required and must be a string.'\n            }, {\n                status: 400\n            });\n        }\n        // IMPORTANT: Sanitize or validate sqlQuery here if necessary to prevent SQL injection,\n        // even though it's passed as an argument. For now, we assume the query is safe.\n        const wrapperAppDir = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');\n        const wrapperAppPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(wrapperAppDir, 'WrapperApp.exe');\n        // Escape quotes and other special characters for the command line\n        const escapedQuery = sqlQuery.replace(/\"/g, '\\\\\"');\n        const command = `\"${wrapperAppPath}\" \"${escapedQuery}\" --silent`;\n        console.log(`Executing command: ${command} in ${wrapperAppDir}`);\n        // Use PowerShell to redirect stderr to null and capture stdout\n        const powershellCommand = `& \"${wrapperAppPath}\" \"${escapedQuery}\" --silent 2>$null`;\n        const { stdout, stderr } = await new Promise((resolve, reject)=>{\n            const child = (0,child_process__WEBPACK_IMPORTED_MODULE_1__.spawn)('powershell', [\n                '-Command',\n                powershellCommand\n            ], {\n                cwd: wrapperAppDir,\n                stdio: [\n                    'pipe',\n                    'pipe',\n                    'pipe'\n                ]\n            });\n            let stdoutData = '';\n            let stderrData = '';\n            child.stdout.on('data', (data)=>{\n                stdoutData += data.toString();\n            });\n            child.stderr.on('data', (data)=>{\n                stderrData += data.toString();\n            });\n            child.on('close', (code)=>{\n                if (code !== 0) {\n                    reject(new Error(`Process exited with code ${code}`));\n                } else {\n                    resolve({\n                        stdout: stdoutData,\n                        stderr: stderrData\n                    });\n                }\n            });\n            child.on('error', (error)=>{\n                reject(error);\n            });\n        });\n        if (stderr) {\n            console.error(`C# Wrapper Error: ${stderr}`);\n            // Try to parse stdout for a JSON error message first\n            try {\n                const errorJson = JSON.parse(stdout);\n                if (errorJson.error) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Wrapper Error: ${errorJson.error}`,\n                        details: stderr\n                    }, {\n                        status: 500\n                    });\n                }\n            } catch (e) {\n                // If stdout is not a JSON error, return the raw stderr\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'An error occurred in the C# wrapper.',\n                    details: stderr\n                }, {\n                    status: 500\n                });\n            }\n        }\n        console.log(`Raw stdout length: ${stdout.length}`);\n        console.log(`Raw stdout content: ${JSON.stringify(stdout)}`);\n        // Clean the output by removing WebSocket binary data and keeping only text lines\n        const cleanOutput = stdout.split('\\n').filter((line)=>{\n            const trimmed = line.trim();\n            // Keep empty lines for now\n            if (trimmed.length === 0) return true;\n            // Remove lines that contain binary data (hex dumps)\n            if (line.match(/^[0-9a-fA-F]{4,8}\\s+[0-9a-fA-F\\s]+/)) return false;\n            // Remove lines with WebSocket protocol data\n            if (line.includes('GET /ws HTTP') || line.includes('WebSocket') || line.includes('Upgrade:')) return false;\n            // Keep lines that look like file paths (more permissive)\n            if (line.match(/[A-Za-z]:[\\\\\\/].*\\.json/)) return true;\n            // Keep other reasonable text lines\n            if (line.match(/^[A-Za-z0-9\\\\:._\\-\\s\\/]+$/)) return true;\n            return false;\n        }).join('\\n');\n        console.log(`Cleaned output length: ${cleanOutput.length}`);\n        console.log(`Cleaned output content: ${JSON.stringify(cleanOutput)}`);\n        // Try multiple regex patterns to find the file path (more permissive)\n        const patterns = [\n            /[A-Za-z]:[\\\\\\/].*\\.json/gi,\n            /[A-Z]:[\\\\\\/][^<>:\"|?*\\r\\n]*\\.json/gi,\n            /[A-Z]:[\\\\\\/]Users[\\\\\\/].*\\.json/gi,\n            /[A-Z]:[\\\\\\/]Windows[\\\\\\/]Temp[\\\\\\/].*\\.json/gi,\n            /[A-Z]:[\\\\\\/].*[\\\\\\/]Temp[\\\\\\/].*\\.json/gi\n        ];\n        let foundPath = null;\n        for (const pattern of patterns){\n            const matches = cleanOutput.match(pattern);\n            if (matches && matches.length > 0) {\n                console.log(`Pattern ${pattern} found matches: ${matches.join(', ')}`);\n                // Take the first valid-looking path\n                for (const match of matches){\n                    if (match.includes('Temp') && match.endsWith('.json')) {\n                        foundPath = match;\n                        break;\n                    }\n                }\n                if (foundPath) break;\n            }\n        }\n        if (!foundPath) {\n            console.error(`Could not extract file path from stdout. Raw output: ${stdout}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无法从C#包装器的输出中提取文件路径。'\n            }, {\n                status: 500\n            });\n        }\n        filePath = foundPath;\n        console.log(`Extracted file path: ${filePath}`);\n        if (!filePath) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无法从C#包装器的输出中提取文件路径。'\n            }, {\n                status: 500\n            });\n        }\n        if (!fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(filePath) || !fs__WEBPACK_IMPORTED_MODULE_3___default().statSync(filePath).isFile()) {\n            console.error(`File not found or is not a file: ${filePath}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Temporary data file not found.',\n                path: filePath\n            }, {\n                status: 404\n            });\n        }\n        const fileContent = fs__WEBPACK_IMPORTED_MODULE_3___default().readFileSync(filePath, 'utf-8');\n        const result = JSON.parse(fileContent);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An internal server error occurred.',\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        if (filePath && fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(filePath)) {\n            try {\n                fs__WEBPACK_IMPORTED_MODULE_3___default().unlinkSync(filePath);\n                console.log(`Successfully deleted temporary file: ${filePath}`);\n            } catch (unlinkError) {\n                console.error(`Failed to delete temporary file: ${filePath}`, unlinkError);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2RhdGFiYXNlLXF1ZXJ5L3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTJDO0FBQ0w7QUFDZDtBQUNKO0FBRWIsZUFBZUksS0FBS0MsT0FBZ0I7SUFDekMsSUFBSUMsV0FBMEI7SUFDOUIsSUFBSTtRQUNGLE1BQU1DLE9BQU8sTUFBTUYsUUFBUUcsSUFBSTtRQUMvQixNQUFNQyxXQUFXRixLQUFLRyxLQUFLO1FBRTNCLElBQUksQ0FBQ0QsWUFBWSxPQUFPQSxhQUFhLFVBQVU7WUFDN0MsT0FBT1QscURBQVlBLENBQUNRLElBQUksQ0FBQztnQkFBRUcsT0FBTztZQUEwQyxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDL0Y7UUFFQSx1RkFBdUY7UUFDdkYsZ0ZBQWdGO1FBRWhGLE1BQU1DLGdCQUFnQlgsbURBQVksQ0FBQ2EsUUFBUUMsR0FBRyxJQUFJLE1BQU0sa0JBQWtCLE9BQU8sU0FBUztRQUMxRixNQUFNQyxpQkFBaUJmLGdEQUFTLENBQUNXLGVBQWU7UUFFaEQsa0VBQWtFO1FBQ2xFLE1BQU1NLGVBQWVWLFNBQVNXLE9BQU8sQ0FBQyxNQUFNO1FBQzVDLE1BQU1DLFVBQVUsQ0FBQyxDQUFDLEVBQUVKLGVBQWUsR0FBRyxFQUFFRSxhQUFhLFVBQVUsQ0FBQztRQUVoRUcsUUFBUUMsR0FBRyxDQUFDLENBQUMsbUJBQW1CLEVBQUVGLFFBQVEsSUFBSSxFQUFFUixlQUFlO1FBRS9ELCtEQUErRDtRQUMvRCxNQUFNVyxvQkFBb0IsQ0FBQyxHQUFHLEVBQUVQLGVBQWUsR0FBRyxFQUFFRSxhQUFhLGtCQUFrQixDQUFDO1FBRXBGLE1BQU0sRUFBRU0sTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBRyxNQUFNLElBQUlDLFFBQTBDLENBQUNiLFNBQVNjO1lBQ3ZGLE1BQU1DLFFBQVE1QixvREFBS0EsQ0FBQyxjQUFjO2dCQUFDO2dCQUFZdUI7YUFBa0IsRUFBRTtnQkFDakVSLEtBQUtIO2dCQUNMaUIsT0FBTztvQkFBQztvQkFBUTtvQkFBUTtpQkFBTztZQUNqQztZQUVBLElBQUlDLGFBQWE7WUFDakIsSUFBSUMsYUFBYTtZQUVqQkgsTUFBTUosTUFBTSxDQUFDUSxFQUFFLENBQUMsUUFBUSxDQUFDQztnQkFDdkJILGNBQWNHLEtBQUtDLFFBQVE7WUFDN0I7WUFFQU4sTUFBTUgsTUFBTSxDQUFDTyxFQUFFLENBQUMsUUFBUSxDQUFDQztnQkFDdkJGLGNBQWNFLEtBQUtDLFFBQVE7WUFDN0I7WUFFQU4sTUFBTUksRUFBRSxDQUFDLFNBQVMsQ0FBQ0c7Z0JBQ2pCLElBQUlBLFNBQVMsR0FBRztvQkFDZFIsT0FBTyxJQUFJUyxNQUFNLENBQUMseUJBQXlCLEVBQUVELE1BQU07Z0JBQ3JELE9BQU87b0JBQ0x0QixRQUFRO3dCQUFFVyxRQUFRTTt3QkFBWUwsUUFBUU07b0JBQVc7Z0JBQ25EO1lBQ0Y7WUFFQUgsTUFBTUksRUFBRSxDQUFDLFNBQVMsQ0FBQ3RCO2dCQUNqQmlCLE9BQU9qQjtZQUNUO1FBQ0Y7UUFFQSxJQUFJZSxRQUFRO1lBQ1ZKLFFBQVFYLEtBQUssQ0FBQyxDQUFDLGtCQUFrQixFQUFFZSxRQUFRO1lBQzNDLHFEQUFxRDtZQUNyRCxJQUFJO2dCQUNGLE1BQU1ZLFlBQVlDLEtBQUtDLEtBQUssQ0FBQ2Y7Z0JBQzdCLElBQUlhLFVBQVUzQixLQUFLLEVBQUU7b0JBQ25CLE9BQU9YLHFEQUFZQSxDQUFDUSxJQUFJLENBQUM7d0JBQUVHLE9BQU8sQ0FBQyxlQUFlLEVBQUUyQixVQUFVM0IsS0FBSyxFQUFFO3dCQUFFOEIsU0FBU2Y7b0JBQU8sR0FBRzt3QkFBRWQsUUFBUTtvQkFBSTtnQkFDMUc7WUFDRixFQUFFLE9BQU84QixHQUFHO2dCQUNWLHVEQUF1RDtnQkFDdkQsT0FBTzFDLHFEQUFZQSxDQUFDUSxJQUFJLENBQUM7b0JBQUVHLE9BQU87b0JBQXdDOEIsU0FBU2Y7Z0JBQU8sR0FBRztvQkFBRWQsUUFBUTtnQkFBSTtZQUM3RztRQUNGO1FBRUFVLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG1CQUFtQixFQUFFRSxPQUFPa0IsTUFBTSxFQUFFO1FBQ2pEckIsUUFBUUMsR0FBRyxDQUFDLENBQUMsb0JBQW9CLEVBQUVnQixLQUFLSyxTQUFTLENBQUNuQixTQUFTO1FBRTNELGlGQUFpRjtRQUNqRixNQUFNb0IsY0FBY3BCLE9BQ2pCcUIsS0FBSyxDQUFDLE1BQ05DLE1BQU0sQ0FBQ0MsQ0FBQUE7WUFDTixNQUFNQyxVQUFVRCxLQUFLRSxJQUFJO1lBQ3pCLDJCQUEyQjtZQUMzQixJQUFJRCxRQUFRTixNQUFNLEtBQUssR0FBRyxPQUFPO1lBQ2pDLG9EQUFvRDtZQUNwRCxJQUFJSyxLQUFLRyxLQUFLLENBQUMsdUNBQXVDLE9BQU87WUFDN0QsNENBQTRDO1lBQzVDLElBQUlILEtBQUtJLFFBQVEsQ0FBQyxtQkFBbUJKLEtBQUtJLFFBQVEsQ0FBQyxnQkFBZ0JKLEtBQUtJLFFBQVEsQ0FBQyxhQUFhLE9BQU87WUFDckcseURBQXlEO1lBQ3pELElBQUlKLEtBQUtHLEtBQUssQ0FBQyw0QkFBNEIsT0FBTztZQUNsRCxtQ0FBbUM7WUFDbkMsSUFBSUgsS0FBS0csS0FBSyxDQUFDLDhCQUE4QixPQUFPO1lBQ3BELE9BQU87UUFDVCxHQUNDakMsSUFBSSxDQUFDO1FBRVJJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHVCQUF1QixFQUFFc0IsWUFBWUYsTUFBTSxFQUFFO1FBQzFEckIsUUFBUUMsR0FBRyxDQUFDLENBQUMsd0JBQXdCLEVBQUVnQixLQUFLSyxTQUFTLENBQUNDLGNBQWM7UUFFcEUsc0VBQXNFO1FBQ3RFLE1BQU1RLFdBQVc7WUFDZjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFFRCxJQUFJQyxZQUFZO1FBQ2hCLEtBQUssTUFBTUMsV0FBV0YsU0FBVTtZQUM5QixNQUFNRyxVQUFVWCxZQUFZTSxLQUFLLENBQUNJO1lBQ2xDLElBQUlDLFdBQVdBLFFBQVFiLE1BQU0sR0FBRyxHQUFHO2dCQUNqQ3JCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLFFBQVEsRUFBRWdDLFFBQVEsZ0JBQWdCLEVBQUVDLFFBQVF0QyxJQUFJLENBQUMsT0FBTztnQkFDckUsb0NBQW9DO2dCQUNwQyxLQUFLLE1BQU1pQyxTQUFTSyxRQUFTO29CQUMzQixJQUFJTCxNQUFNQyxRQUFRLENBQUMsV0FBV0QsTUFBTU0sUUFBUSxDQUFDLFVBQVU7d0JBQ3JESCxZQUFZSDt3QkFDWjtvQkFDRjtnQkFDRjtnQkFDQSxJQUFJRyxXQUFXO1lBQ2pCO1FBQ0Y7UUFFQSxJQUFJLENBQUNBLFdBQVc7WUFDZGhDLFFBQVFYLEtBQUssQ0FBQyxDQUFDLHFEQUFxRCxFQUFFYyxRQUFRO1lBQzlFLE9BQU96QixxREFBWUEsQ0FBQ1EsSUFBSSxDQUFDO2dCQUFFRyxPQUFPO1lBQXNCLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUMzRTtRQUVBTixXQUFXZ0Q7UUFDWGhDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHFCQUFxQixFQUFFakIsVUFBVTtRQUU5QyxJQUFJLENBQUNBLFVBQVU7WUFDYixPQUFPTixxREFBWUEsQ0FBQ1EsSUFBSSxDQUFDO2dCQUFFRyxPQUFPO1lBQXNCLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUMzRTtRQUVBLElBQUksQ0FBQ1Qsb0RBQWEsQ0FBQ0csYUFBYSxDQUFDSCxrREFBVyxDQUFDRyxVQUFVc0QsTUFBTSxJQUFJO1lBQzdEdEMsUUFBUVgsS0FBSyxDQUFDLENBQUMsaUNBQWlDLEVBQUVMLFVBQVU7WUFDNUQsT0FBT04scURBQVlBLENBQUNRLElBQUksQ0FBQztnQkFBRUcsT0FBTztnQkFBa0NULE1BQU1JO1lBQVMsR0FBRztnQkFBRU0sUUFBUTtZQUFJO1FBQ3hHO1FBRUEsTUFBTWlELGNBQWMxRCxzREFBZSxDQUFDRyxVQUFVO1FBQzlDLE1BQU15RCxTQUFTeEIsS0FBS0MsS0FBSyxDQUFDcUI7UUFFMUIsT0FBTzdELHFEQUFZQSxDQUFDUSxJQUFJLENBQUN1RDtJQUUzQixFQUFFLE9BQU9wRCxPQUFZO1FBQ25CVyxRQUFRWCxLQUFLLENBQUMsY0FBY0E7UUFDNUIsT0FBT1gscURBQVlBLENBQUNRLElBQUksQ0FBQztZQUFFRyxPQUFPO1lBQXNDOEIsU0FBUzlCLE1BQU1xRCxPQUFPO1FBQUMsR0FBRztZQUFFcEQsUUFBUTtRQUFJO0lBQ2xILFNBQVU7UUFDUixJQUFJTixZQUFZSCxvREFBYSxDQUFDRyxXQUFXO1lBQ3ZDLElBQUk7Z0JBQ0ZILG9EQUFhLENBQUNHO2dCQUNkZ0IsUUFBUUMsR0FBRyxDQUFDLENBQUMscUNBQXFDLEVBQUVqQixVQUFVO1lBQ2hFLEVBQUUsT0FBTzRELGFBQWE7Z0JBQ3BCNUMsUUFBUVgsS0FBSyxDQUFDLENBQUMsaUNBQWlDLEVBQUVMLFVBQVUsRUFBRTREO1lBQ2hFO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJEOlxccHljb2RlXFxzdXBwb3J0X2NoYXJ0MlxcaG90ZWwtZGFzaGJvYXJkXFxhcHBcXGFwaVxcZGF0YWJhc2UtcXVlcnlcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcclxuaW1wb3J0IHsgc3Bhd24gfSBmcm9tICdjaGlsZF9wcm9jZXNzJztcclxuaW1wb3J0IHBhdGggZnJvbSAncGF0aCc7XHJcbmltcG9ydCBmcyBmcm9tICdmcyc7XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBSZXF1ZXN0KSB7XHJcbiAgbGV0IGZpbGVQYXRoOiBzdHJpbmcgfCBudWxsID0gbnVsbDtcclxuICB0cnkge1xyXG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xyXG4gICAgY29uc3Qgc3FsUXVlcnkgPSBib2R5LnF1ZXJ5O1xyXG5cclxuICAgIGlmICghc3FsUXVlcnkgfHwgdHlwZW9mIHNxbFF1ZXJ5ICE9PSAnc3RyaW5nJykge1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1F1ZXJ5IGlzIHJlcXVpcmVkIGFuZCBtdXN0IGJlIGEgc3RyaW5nLicgfSwgeyBzdGF0dXM6IDQwMCB9KTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBJTVBPUlRBTlQ6IFNhbml0aXplIG9yIHZhbGlkYXRlIHNxbFF1ZXJ5IGhlcmUgaWYgbmVjZXNzYXJ5IHRvIHByZXZlbnQgU1FMIGluamVjdGlvbixcclxuICAgIC8vIGV2ZW4gdGhvdWdoIGl0J3MgcGFzc2VkIGFzIGFuIGFyZ3VtZW50LiBGb3Igbm93LCB3ZSBhc3N1bWUgdGhlIHF1ZXJ5IGlzIHNhZmUuXHJcblxyXG4gICAgY29uc3Qgd3JhcHBlckFwcERpciA9IHBhdGgucmVzb2x2ZShwcm9jZXNzLmN3ZCgpLCAnLi4nLCAnY3NoYXJwLXdyYXBwZXInLCAnYmluJywgJ0RlYnVnJywgJ25ldDQ4Jyk7XHJcbiAgICBjb25zdCB3cmFwcGVyQXBwUGF0aCA9IHBhdGguam9pbih3cmFwcGVyQXBwRGlyLCAnV3JhcHBlckFwcC5leGUnKTtcclxuICAgIFxyXG4gICAgLy8gRXNjYXBlIHF1b3RlcyBhbmQgb3RoZXIgc3BlY2lhbCBjaGFyYWN0ZXJzIGZvciB0aGUgY29tbWFuZCBsaW5lXHJcbiAgICBjb25zdCBlc2NhcGVkUXVlcnkgPSBzcWxRdWVyeS5yZXBsYWNlKC9cIi9nLCAnXFxcXFwiJyk7XHJcbiAgICBjb25zdCBjb21tYW5kID0gYFwiJHt3cmFwcGVyQXBwUGF0aH1cIiBcIiR7ZXNjYXBlZFF1ZXJ5fVwiIC0tc2lsZW50YDtcclxuXHJcbiAgICBjb25zb2xlLmxvZyhgRXhlY3V0aW5nIGNvbW1hbmQ6ICR7Y29tbWFuZH0gaW4gJHt3cmFwcGVyQXBwRGlyfWApO1xyXG5cclxuICAgIC8vIFVzZSBQb3dlclNoZWxsIHRvIHJlZGlyZWN0IHN0ZGVyciB0byBudWxsIGFuZCBjYXB0dXJlIHN0ZG91dFxyXG4gICAgY29uc3QgcG93ZXJzaGVsbENvbW1hbmQgPSBgJiBcIiR7d3JhcHBlckFwcFBhdGh9XCIgXCIke2VzY2FwZWRRdWVyeX1cIiAtLXNpbGVudCAyPiRudWxsYDtcclxuXHJcbiAgICBjb25zdCB7IHN0ZG91dCwgc3RkZXJyIH0gPSBhd2FpdCBuZXcgUHJvbWlzZTx7c3Rkb3V0OiBzdHJpbmcsIHN0ZGVycjogc3RyaW5nfT4oKHJlc29sdmUsIHJlamVjdCkgPT4ge1xyXG4gICAgICBjb25zdCBjaGlsZCA9IHNwYXduKCdwb3dlcnNoZWxsJywgWyctQ29tbWFuZCcsIHBvd2Vyc2hlbGxDb21tYW5kXSwge1xyXG4gICAgICAgIGN3ZDogd3JhcHBlckFwcERpcixcclxuICAgICAgICBzdGRpbzogWydwaXBlJywgJ3BpcGUnLCAncGlwZSddXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgbGV0IHN0ZG91dERhdGEgPSAnJztcclxuICAgICAgbGV0IHN0ZGVyckRhdGEgPSAnJztcclxuXHJcbiAgICAgIGNoaWxkLnN0ZG91dC5vbignZGF0YScsIChkYXRhKSA9PiB7XHJcbiAgICAgICAgc3Rkb3V0RGF0YSArPSBkYXRhLnRvU3RyaW5nKCk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY2hpbGQuc3RkZXJyLm9uKCdkYXRhJywgKGRhdGEpID0+IHtcclxuICAgICAgICBzdGRlcnJEYXRhICs9IGRhdGEudG9TdHJpbmcoKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjaGlsZC5vbignY2xvc2UnLCAoY29kZSkgPT4ge1xyXG4gICAgICAgIGlmIChjb2RlICE9PSAwKSB7XHJcbiAgICAgICAgICByZWplY3QobmV3IEVycm9yKGBQcm9jZXNzIGV4aXRlZCB3aXRoIGNvZGUgJHtjb2RlfWApKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgcmVzb2x2ZSh7IHN0ZG91dDogc3Rkb3V0RGF0YSwgc3RkZXJyOiBzdGRlcnJEYXRhIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjaGlsZC5vbignZXJyb3InLCAoZXJyb3IpID0+IHtcclxuICAgICAgICByZWplY3QoZXJyb3IpO1xyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGlmIChzdGRlcnIpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihgQyMgV3JhcHBlciBFcnJvcjogJHtzdGRlcnJ9YCk7XHJcbiAgICAgIC8vIFRyeSB0byBwYXJzZSBzdGRvdXQgZm9yIGEgSlNPTiBlcnJvciBtZXNzYWdlIGZpcnN0XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgZXJyb3JKc29uID0gSlNPTi5wYXJzZShzdGRvdXQpO1xyXG4gICAgICAgIGlmIChlcnJvckpzb24uZXJyb3IpIHtcclxuICAgICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBgV3JhcHBlciBFcnJvcjogJHtlcnJvckpzb24uZXJyb3J9YCwgZGV0YWlsczogc3RkZXJyIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlKSB7XHJcbiAgICAgICAgLy8gSWYgc3Rkb3V0IGlzIG5vdCBhIEpTT04gZXJyb3IsIHJldHVybiB0aGUgcmF3IHN0ZGVyclxyXG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnQW4gZXJyb3Igb2NjdXJyZWQgaW4gdGhlIEMjIHdyYXBwZXIuJywgZGV0YWlsczogc3RkZXJyIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZyhgUmF3IHN0ZG91dCBsZW5ndGg6ICR7c3Rkb3V0Lmxlbmd0aH1gKTtcclxuICAgIGNvbnNvbGUubG9nKGBSYXcgc3Rkb3V0IGNvbnRlbnQ6ICR7SlNPTi5zdHJpbmdpZnkoc3Rkb3V0KX1gKTtcclxuXHJcbiAgICAvLyBDbGVhbiB0aGUgb3V0cHV0IGJ5IHJlbW92aW5nIFdlYlNvY2tldCBiaW5hcnkgZGF0YSBhbmQga2VlcGluZyBvbmx5IHRleHQgbGluZXNcclxuICAgIGNvbnN0IGNsZWFuT3V0cHV0ID0gc3Rkb3V0XHJcbiAgICAgIC5zcGxpdCgnXFxuJylcclxuICAgICAgLmZpbHRlcihsaW5lID0+IHtcclxuICAgICAgICBjb25zdCB0cmltbWVkID0gbGluZS50cmltKCk7XHJcbiAgICAgICAgLy8gS2VlcCBlbXB0eSBsaW5lcyBmb3Igbm93XHJcbiAgICAgICAgaWYgKHRyaW1tZWQubGVuZ3RoID09PSAwKSByZXR1cm4gdHJ1ZTtcclxuICAgICAgICAvLyBSZW1vdmUgbGluZXMgdGhhdCBjb250YWluIGJpbmFyeSBkYXRhIChoZXggZHVtcHMpXHJcbiAgICAgICAgaWYgKGxpbmUubWF0Y2goL15bMC05YS1mQS1GXXs0LDh9XFxzK1swLTlhLWZBLUZcXHNdKy8pKSByZXR1cm4gZmFsc2U7XHJcbiAgICAgICAgLy8gUmVtb3ZlIGxpbmVzIHdpdGggV2ViU29ja2V0IHByb3RvY29sIGRhdGFcclxuICAgICAgICBpZiAobGluZS5pbmNsdWRlcygnR0VUIC93cyBIVFRQJykgfHwgbGluZS5pbmNsdWRlcygnV2ViU29ja2V0JykgfHwgbGluZS5pbmNsdWRlcygnVXBncmFkZTonKSkgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgIC8vIEtlZXAgbGluZXMgdGhhdCBsb29rIGxpa2UgZmlsZSBwYXRocyAobW9yZSBwZXJtaXNzaXZlKVxyXG4gICAgICAgIGlmIChsaW5lLm1hdGNoKC9bQS1aYS16XTpbXFxcXFxcL10uKlxcLmpzb24vKSkgcmV0dXJuIHRydWU7XHJcbiAgICAgICAgLy8gS2VlcCBvdGhlciByZWFzb25hYmxlIHRleHQgbGluZXNcclxuICAgICAgICBpZiAobGluZS5tYXRjaCgvXltBLVphLXowLTlcXFxcOi5fXFwtXFxzXFwvXSskLykpIHJldHVybiB0cnVlO1xyXG4gICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgfSlcclxuICAgICAgLmpvaW4oJ1xcbicpO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKGBDbGVhbmVkIG91dHB1dCBsZW5ndGg6ICR7Y2xlYW5PdXRwdXQubGVuZ3RofWApO1xyXG4gICAgY29uc29sZS5sb2coYENsZWFuZWQgb3V0cHV0IGNvbnRlbnQ6ICR7SlNPTi5zdHJpbmdpZnkoY2xlYW5PdXRwdXQpfWApO1xyXG5cclxuICAgIC8vIFRyeSBtdWx0aXBsZSByZWdleCBwYXR0ZXJucyB0byBmaW5kIHRoZSBmaWxlIHBhdGggKG1vcmUgcGVybWlzc2l2ZSlcclxuICAgIGNvbnN0IHBhdHRlcm5zID0gW1xyXG4gICAgICAvW0EtWmEtel06W1xcXFxcXC9dLipcXC5qc29uL2dpLCAgLy8gVmVyeSBzaW1wbGUgcGF0dGVyblxyXG4gICAgICAvW0EtWl06W1xcXFxcXC9dW148PjpcInw/Klxcclxcbl0qXFwuanNvbi9naSwgIC8vIFN0YW5kYXJkIFdpbmRvd3MgcGF0aFxyXG4gICAgICAvW0EtWl06W1xcXFxcXC9dVXNlcnNbXFxcXFxcL10uKlxcLmpzb24vZ2ksIC8vIFVzZXJzIGRpcmVjdG9yeVxyXG4gICAgICAvW0EtWl06W1xcXFxcXC9dV2luZG93c1tcXFxcXFwvXVRlbXBbXFxcXFxcL10uKlxcLmpzb24vZ2ksIC8vIFdpbmRvd3MgVGVtcFxyXG4gICAgICAvW0EtWl06W1xcXFxcXC9dLipbXFxcXFxcL11UZW1wW1xcXFxcXC9dLipcXC5qc29uL2dpLCAvLyBBbnkgVGVtcCBkaXJlY3RvcnlcclxuICAgIF07XHJcblxyXG4gICAgbGV0IGZvdW5kUGF0aCA9IG51bGw7XHJcbiAgICBmb3IgKGNvbnN0IHBhdHRlcm4gb2YgcGF0dGVybnMpIHtcclxuICAgICAgY29uc3QgbWF0Y2hlcyA9IGNsZWFuT3V0cHV0Lm1hdGNoKHBhdHRlcm4pO1xyXG4gICAgICBpZiAobWF0Y2hlcyAmJiBtYXRjaGVzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICBjb25zb2xlLmxvZyhgUGF0dGVybiAke3BhdHRlcm59IGZvdW5kIG1hdGNoZXM6ICR7bWF0Y2hlcy5qb2luKCcsICcpfWApO1xyXG4gICAgICAgIC8vIFRha2UgdGhlIGZpcnN0IHZhbGlkLWxvb2tpbmcgcGF0aFxyXG4gICAgICAgIGZvciAoY29uc3QgbWF0Y2ggb2YgbWF0Y2hlcykge1xyXG4gICAgICAgICAgaWYgKG1hdGNoLmluY2x1ZGVzKCdUZW1wJykgJiYgbWF0Y2guZW5kc1dpdGgoJy5qc29uJykpIHtcclxuICAgICAgICAgICAgZm91bmRQYXRoID0gbWF0Y2g7XHJcbiAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoZm91bmRQYXRoKSBicmVhaztcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGlmICghZm91bmRQYXRoKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoYENvdWxkIG5vdCBleHRyYWN0IGZpbGUgcGF0aCBmcm9tIHN0ZG91dC4gUmF3IG91dHB1dDogJHtzdGRvdXR9YCk7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAn5peg5rOV5LuOQyPljIXoo4XlmajnmoTovpPlh7rkuK3mj5Dlj5bmlofku7bot6/lvoTjgIInIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgZmlsZVBhdGggPSBmb3VuZFBhdGg7XHJcbiAgICBjb25zb2xlLmxvZyhgRXh0cmFjdGVkIGZpbGUgcGF0aDogJHtmaWxlUGF0aH1gKTtcclxuXHJcbiAgICBpZiAoIWZpbGVQYXRoKSB7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAn5peg5rOV5LuOQyPljIXoo4XlmajnmoTovpPlh7rkuK3mj5Dlj5bmlofku7bot6/lvoTjgIInIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKCFmcy5leGlzdHNTeW5jKGZpbGVQYXRoKSB8fCAhZnMuc3RhdFN5bmMoZmlsZVBhdGgpLmlzRmlsZSgpKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihgRmlsZSBub3QgZm91bmQgb3IgaXMgbm90IGEgZmlsZTogJHtmaWxlUGF0aH1gKTtcclxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1RlbXBvcmFyeSBkYXRhIGZpbGUgbm90IGZvdW5kLicsIHBhdGg6IGZpbGVQYXRoIH0sIHsgc3RhdHVzOiA0MDQgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZmlsZUNvbnRlbnQgPSBmcy5yZWFkRmlsZVN5bmMoZmlsZVBhdGgsICd1dGYtOCcpO1xyXG4gICAgY29uc3QgcmVzdWx0ID0gSlNPTi5wYXJzZShmaWxlQ29udGVudCk7XHJcbiAgICBcclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihyZXN1bHQpO1xyXG5cclxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdBUEkgRXJyb3I6JywgZXJyb3IpO1xyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdBbiBpbnRlcm5hbCBzZXJ2ZXIgZXJyb3Igb2NjdXJyZWQuJywgZGV0YWlsczogZXJyb3IubWVzc2FnZSB9LCB7IHN0YXR1czogNTAwIH0pO1xyXG4gIH0gZmluYWxseSB7XHJcbiAgICBpZiAoZmlsZVBhdGggJiYgZnMuZXhpc3RzU3luYyhmaWxlUGF0aCkpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBmcy51bmxpbmtTeW5jKGZpbGVQYXRoKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhgU3VjY2Vzc2Z1bGx5IGRlbGV0ZWQgdGVtcG9yYXJ5IGZpbGU6ICR7ZmlsZVBhdGh9YCk7XHJcbiAgICAgIH0gY2F0Y2ggKHVubGlua0Vycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIGRlbGV0ZSB0ZW1wb3JhcnkgZmlsZTogJHtmaWxlUGF0aH1gLCB1bmxpbmtFcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn0iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwic3Bhd24iLCJwYXRoIiwiZnMiLCJQT1NUIiwicmVxdWVzdCIsImZpbGVQYXRoIiwiYm9keSIsImpzb24iLCJzcWxRdWVyeSIsInF1ZXJ5IiwiZXJyb3IiLCJzdGF0dXMiLCJ3cmFwcGVyQXBwRGlyIiwicmVzb2x2ZSIsInByb2Nlc3MiLCJjd2QiLCJ3cmFwcGVyQXBwUGF0aCIsImpvaW4iLCJlc2NhcGVkUXVlcnkiLCJyZXBsYWNlIiwiY29tbWFuZCIsImNvbnNvbGUiLCJsb2ciLCJwb3dlcnNoZWxsQ29tbWFuZCIsInN0ZG91dCIsInN0ZGVyciIsIlByb21pc2UiLCJyZWplY3QiLCJjaGlsZCIsInN0ZGlvIiwic3Rkb3V0RGF0YSIsInN0ZGVyckRhdGEiLCJvbiIsImRhdGEiLCJ0b1N0cmluZyIsImNvZGUiLCJFcnJvciIsImVycm9ySnNvbiIsIkpTT04iLCJwYXJzZSIsImRldGFpbHMiLCJlIiwibGVuZ3RoIiwic3RyaW5naWZ5IiwiY2xlYW5PdXRwdXQiLCJzcGxpdCIsImZpbHRlciIsImxpbmUiLCJ0cmltbWVkIiwidHJpbSIsIm1hdGNoIiwiaW5jbHVkZXMiLCJwYXR0ZXJucyIsImZvdW5kUGF0aCIsInBhdHRlcm4iLCJtYXRjaGVzIiwiZW5kc1dpdGgiLCJleGlzdHNTeW5jIiwic3RhdFN5bmMiLCJpc0ZpbGUiLCJmaWxlQ29udGVudCIsInJlYWRGaWxlU3luYyIsInJlc3VsdCIsIm1lc3NhZ2UiLCJ1bmxpbmtTeW5jIiwidW5saW5rRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/database-query/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_pycode_support_chart2_hotel_dashboard_app_api_database_query_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/database-query/route.ts */ \"(rsc)/./app/api/database-query/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/database-query/route\",\n        pathname: \"/api/database-query\",\n        filename: \"route\",\n        bundlePath: \"app/api/database-query/route\"\n    },\n    resolvedPagePath: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\api\\\\database-query\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_pycode_support_chart2_hotel_dashboard_app_api_database_query_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();