/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/database-query/route";
exports.ids = ["app/api/database-query/route"];
exports.modules = {

/***/ "(rsc)/./app/api/database-query/route.ts":
/*!*****************************************!*\
  !*** ./app/api/database-query/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// Promisify exec for async/await usage\nconst execPromise = util__WEBPACK_IMPORTED_MODULE_3___default().promisify(child_process__WEBPACK_IMPORTED_MODULE_1__.exec);\nasync function POST(request) {\n    let filePath = null;\n    try {\n        const body = await request.json();\n        const sqlQuery = body.query;\n        if (!sqlQuery || typeof sqlQuery !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Query is required and must be a string.'\n            }, {\n                status: 400\n            });\n        }\n        // IMPORTANT: Sanitize or validate sqlQuery here if necessary to prevent SQL injection,\n        // even though it's passed as an argument. For now, we assume the query is safe.\n        const wrapperAppDir = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');\n        const wrapperAppPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(wrapperAppDir, 'WrapperApp.exe');\n        // Escape quotes and other special characters for the command line\n        const escapedQuery = sqlQuery.replace(/\"/g, '\\\\\"');\n        const command = `\"${wrapperAppPath}\" \"${escapedQuery}\"`;\n        console.log(`Executing command: ${command} in ${wrapperAppDir}`);\n        const { stdout, stderr } = await execPromise(command, {\n            cwd: wrapperAppDir\n        });\n        if (stderr) {\n            console.error(`C# Wrapper Error: ${stderr}`);\n            // Try to parse stdout for a JSON error message first\n            try {\n                const errorJson = JSON.parse(stdout);\n                if (errorJson.error) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Wrapper Error: ${errorJson.error}`,\n                        details: stderr\n                    }, {\n                        status: 500\n                    });\n                }\n            } catch (e) {\n                // If stdout is not a JSON error, return the raw stderr\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'An error occurred in the C# wrapper.',\n                    details: stderr\n                }, {\n                    status: 500\n                });\n            }\n        }\n        // New logic to extract file path from stdout using regex\n        // Look for Windows file paths (C:\\...\\filename.json) in the output\n        // The path might be mixed with other output, so we need a more robust regex\n        console.log(`Raw stdout length: ${stdout.length}`);\n        console.log(`First 500 chars of stdout: ${stdout.substring(0, 500)}`);\n        console.log(`Last 500 chars of stdout: ${stdout.substring(Math.max(0, stdout.length - 500))}`);\n        // Try multiple regex patterns to find the file path\n        const patterns = [\n            /[A-Z]:[\\\\\\/][^<>:\"|?*\\r\\n\\x00-\\x1F]*\\.json/gi,\n            /[A-Z]:\\\\\\\\[^<>:\"|?*\\r\\n\\x00-\\x1F]*\\.json/gi,\n            /([A-Z]:[\\\\\\/](?:(?![<>:\"|?*\\r\\n\\x00-\\x1F]).)*\\.json)/gi,\n            /[A-Z]:[\\\\\\/]Users[\\\\\\/][^<>:\"|?*\\r\\n\\x00-\\x1F]*\\.json/gi\n        ];\n        let foundPath = null;\n        for (const pattern of patterns){\n            const matches = stdout.match(pattern);\n            if (matches && matches.length > 0) {\n                console.log(`Pattern ${pattern} found matches: ${matches.join(', ')}`);\n                // Take the first valid-looking path\n                for (const match of matches){\n                    if (match.includes('Temp') && match.endsWith('.json')) {\n                        foundPath = match;\n                        break;\n                    }\n                }\n                if (foundPath) break;\n            }\n        }\n        if (!foundPath) {\n            console.error(`Could not extract file path from stdout. Raw output: ${stdout}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无法从C#包装器的输出中提取文件路径。'\n            }, {\n                status: 500\n            });\n        }\n        filePath = foundPath;\n        console.log(`Extracted file path: ${filePath}`);\n        if (!filePath) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无法从C#包装器的输出中提取文件路径。'\n            }, {\n                status: 500\n            });\n        }\n        if (!fs__WEBPACK_IMPORTED_MODULE_4___default().existsSync(filePath) || !fs__WEBPACK_IMPORTED_MODULE_4___default().statSync(filePath).isFile()) {\n            console.error(`File not found or is not a file: ${filePath}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Temporary data file not found.',\n                path: filePath\n            }, {\n                status: 404\n            });\n        }\n        const fileContent = fs__WEBPACK_IMPORTED_MODULE_4___default().readFileSync(filePath, 'utf-8');\n        const result = JSON.parse(fileContent);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An internal server error occurred.',\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        if (filePath && fs__WEBPACK_IMPORTED_MODULE_4___default().existsSync(filePath)) {\n            try {\n                fs__WEBPACK_IMPORTED_MODULE_4___default().unlinkSync(filePath);\n                console.log(`Successfully deleted temporary file: ${filePath}`);\n            } catch (unlinkError) {\n                console.error(`Failed to delete temporary file: ${filePath}`, unlinkError);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/database-query/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_pycode_support_chart2_hotel_dashboard_app_api_database_query_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/database-query/route.ts */ \"(rsc)/./app/api/database-query/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/database-query/route\",\n        pathname: \"/api/database-query\",\n        filename: \"route\",\n        bundlePath: \"app/api/database-query/route\"\n    },\n    resolvedPagePath: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\api\\\\database-query\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_pycode_support_chart2_hotel_dashboard_app_api_database_query_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdatabase-query%2Froute&page=%2Fapi%2Fdatabase-query%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdatabase-query%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();