(()=>{var e={};e.id=601,e.ids=[601],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45402:()=>{},62599:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>i,serverHooks:()=>d,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>n});var s=r(96559),a=r(48088),o=r(37719),p=r(45402);let i=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/log-data/route",pathname:"/api/log-data",filename:"route",bundlePath:"app/api/log-data/route"},resolvedPagePath:"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\api\\log-data\\route.ts",nextConfigOutput:"",userland:p}),{workAsyncStorage:u,workUnitAsyncStorage:n,serverHooks:d}=i;function c(){return(0,o.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:n})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[719],()=>r(62599));module.exports=s})();