{"version": 1, "files": ["../../../../../../node_modules/canvas/build/Release/canvas.node", "../../../../../../node_modules/canvas/build/Release/libbrotlicommon.dll", "../../../../../../node_modules/canvas/build/Release/libbrotlidec.dll", "../../../../../../node_modules/canvas/build/Release/libbz2-1.dll", "../../../../../../node_modules/canvas/build/Release/libcairo-2.dll", "../../../../../../node_modules/canvas/build/Release/libcairo-gobject-2.dll", "../../../../../../node_modules/canvas/build/Release/libdatrie-1.dll", "../../../../../../node_modules/canvas/build/Release/libdeflate.dll", "../../../../../../node_modules/canvas/build/Release/libexpat-1.dll", "../../../../../../node_modules/canvas/build/Release/libffi-8.dll", "../../../../../../node_modules/canvas/build/Release/libfontconfig-1.dll", "../../../../../../node_modules/canvas/build/Release/libfreetype-6.dll", "../../../../../../node_modules/canvas/build/Release/libfribidi-0.dll", "../../../../../../node_modules/canvas/build/Release/libgcc_s_seh-1.dll", "../../../../../../node_modules/canvas/build/Release/libgdk_pixbuf-2.0-0.dll", "../../../../../../node_modules/canvas/build/Release/libgif-7.dll", "../../../../../../node_modules/canvas/build/Release/libgio-2.0-0.dll", "../../../../../../node_modules/canvas/build/Release/libglib-2.0-0.dll", "../../../../../../node_modules/canvas/build/Release/libgmodule-2.0-0.dll", "../../../../../../node_modules/canvas/build/Release/libgobject-2.0-0.dll", "../../../../../../node_modules/canvas/build/Release/libgraphite2.dll", "../../../../../../node_modules/canvas/build/Release/libharfbuzz-0.dll", "../../../../../../node_modules/canvas/build/Release/libiconv-2.dll", "../../../../../../node_modules/canvas/build/Release/libintl-8.dll", "../../../../../../node_modules/canvas/build/Release/libjbig-0.dll", "../../../../../../node_modules/canvas/build/Release/libjpeg-8.dll", "../../../../../../node_modules/canvas/build/Release/liblerc.dll", "../../../../../../node_modules/canvas/build/Release/liblzma-5.dll", "../../../../../../node_modules/canvas/build/Release/libpango-1.0-0.dll", "../../../../../../node_modules/canvas/build/Release/libpangocairo-1.0-0.dll", "../../../../../../node_modules/canvas/build/Release/libpangoft2-1.0-0.dll", "../../../../../../node_modules/canvas/build/Release/libpangowin32-1.0-0.dll", "../../../../../../node_modules/canvas/build/Release/libpcre2-8-0.dll", "../../../../../../node_modules/canvas/build/Release/libpixman-1-0.dll", "../../../../../../node_modules/canvas/build/Release/libpng16-16.dll", "../../../../../../node_modules/canvas/build/Release/librsvg-2-2.dll", "../../../../../../node_modules/canvas/build/Release/libsharpyuv-0.dll", "../../../../../../node_modules/canvas/build/Release/libstdc++-6.dll", "../../../../../../node_modules/canvas/build/Release/libthai-0.dll", "../../../../../../node_modules/canvas/build/Release/libtiff-6.dll", "../../../../../../node_modules/canvas/build/Release/libwebp-7.dll", "../../../../../../node_modules/canvas/build/Release/libwinpthread-1.dll", "../../../../../../node_modules/canvas/build/Release/libxml2-2.dll", "../../../../../../node_modules/canvas/build/Release/libzstd.dll", "../../../../../../node_modules/canvas/build/Release/zlib1.dll", "../../../../../../node_modules/canvas/index.js", "../../../../../../node_modules/canvas/lib/DOMMatrix.js", "../../../../../../node_modules/canvas/lib/bindings.js", "../../../../../../node_modules/canvas/lib/canvas.js", "../../../../../../node_modules/canvas/lib/context2d.js", "../../../../../../node_modules/canvas/lib/image.js", "../../../../../../node_modules/canvas/lib/jpegstream.js", "../../../../../../node_modules/canvas/lib/pattern.js", "../../../../../../node_modules/canvas/lib/pdfstream.js", "../../../../../../node_modules/canvas/lib/pngstream.js", "../../../../../../node_modules/canvas/package.json", "../../../../../../node_modules/next/dist/compiled/@opentelemetry/api/index.js", "../../../../../../node_modules/next/dist/compiled/@opentelemetry/api/package.json", "../../../../../../node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js", "../../../../../../node_modules/next/dist/compiled/next-server/app-route.runtime.prod.js", "../../../../../../node_modules/next/dist/server/app-render/action-async-storage-instance.js", "../../../../../../node_modules/next/dist/server/app-render/action-async-storage.external.js", "../../../../../../node_modules/next/dist/server/app-render/after-task-async-storage-instance.js", "../../../../../../node_modules/next/dist/server/app-render/after-task-async-storage.external.js", "../../../../../../node_modules/next/dist/server/app-render/async-local-storage.js", "../../../../../../node_modules/next/dist/server/app-render/clean-async-snapshot-instance.js", "../../../../../../node_modules/next/dist/server/app-render/clean-async-snapshot.external.js", "../../../../../../node_modules/next/dist/server/app-render/work-async-storage-instance.js", "../../../../../../node_modules/next/dist/server/app-render/work-async-storage.external.js", "../../../../../../node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js", "../../../../../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js", "../../../../../../node_modules/next/dist/server/lib/trace/constants.js", "../../../../../../node_modules/next/dist/server/lib/trace/tracer.js", "../../../../../../node_modules/next/dist/shared/lib/is-thenable.js", "../../../../../../node_modules/next/package.json", "../../../../../../package.json", "../../../../../package.json", "../../../../chunks/226.js", "../../../../chunks/580.js", "../../../../chunks/719.js", "../../../../webpack-runtime.js", "route_client-reference-manifest.js"]}