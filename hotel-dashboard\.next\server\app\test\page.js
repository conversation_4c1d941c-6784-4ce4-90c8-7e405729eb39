(()=>{var e={};e.id=11,e.ids=[11],e.modules={1759:(e,t,r)=>{Promise.resolve().then(r.bind(r,67746))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15265:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},18936:()=>{},19044:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\test\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40979:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>p,routeModule:()=>u,tree:()=>l});var s=r(65239),o=r(48088),n=r(88170),i=r.n(n),d=r(30893),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);r.d(t,a);let l={children:["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,19044)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["D:\\pycode\\support_chart2\\hotel-dashboard\\app\\test\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},51713:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},53784:()=>{},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,metadata:()=>o});var s=r(37413);r(82704);let o={title:"v0 App",description:"Created with v0",generator:"v0.dev"};function n({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{children:e})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67746:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(60687);function o(){return(0,s.jsxs)("div",{className:"container mx-auto p-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Test Page"}),(0,s.jsx)("p",{children:"This is a test page to check if the server is working."})]})}},71495:(e,t,r)=>{Promise.resolve().then(r.bind(r,19044))},82704:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[719,825],()=>r(40979));module.exports=s})();