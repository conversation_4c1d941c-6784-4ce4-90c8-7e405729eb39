"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import { Download } from "lucide-react";

// Define a type for the API response
type QueryResult = {
  [tableName: string]: Record<string, any>[];
};

export default function DatabaseQueryPage() {
  const [query, setQuery] = useState("SELECT TOP 10 * FROM dbo.LOG_DATA;");
  const [result, setResult] = useState<QueryResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // CSV导出函数
  const exportToCSV = (tableName: string, data: Record<string, any>[]) => {
    if (data.length === 0) {
      toast({
        title: "No Data",
        description: "No data to export.",
        variant: "destructive",
      });
      return;
    }

    const headers = Object.keys(data[0]);

    // 创建CSV内容
    const csvContent = [
      // CSV头部
      headers.join(','),
      // CSV数据行
      ...data.map(row =>
        headers.map(header => {
          const value = String(row[header] || '');
          // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    // 创建Blob并下载
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `${tableName}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Export Successful",
      description: `${tableName} exported to CSV successfully.`,
    });
  };

  const handleQuery = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/database-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      const response_data = await response.json();

      if (!response.ok) {
        throw new Error(response_data.error || 'An unknown error occurred.');
      }

      // Extract the actual data from the new API response format
      const actualData = response_data.data || {};

      // Check if the result is an empty object, which is a valid success case
      if (Object.keys(actualData).length === 0) {
        toast({
          title: "Query Successful",
          description: "The query ran successfully but returned no data.",
        });
        setResult({});
      } else {
        setResult(actualData);
      }

    } catch (err: any) {
      setError(err.message);
      toast({
        title: "Error",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderTable = (tableName: string, data: Record<string, any>[]) => {
    if (data.length === 0) {
      return <p key={tableName}>Table '{tableName}' has no rows.</p>;
    }
    const headers = Object.keys(data[0]);
    return (
      <Card key={tableName} className="mt-4">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {tableName}
              <span className="text-sm text-gray-500 font-normal">
                ({data.length} rows)
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => exportToCSV(tableName, data)}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export CSV
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {/* 水平和垂直滚动容器 */}
          <div className="overflow-auto max-h-[600px] border rounded-md">
            <Table>
              <TableHeader className="sticky top-0 bg-white z-10 shadow-sm">
                <TableRow>
                  {headers.map((header) => (
                    <TableHead
                      key={header}
                      className="whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2"
                      style={{ minWidth: '120px' }}
                    >
                      {header}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((row, rowIndex) => (
                  <TableRow key={rowIndex} className="hover:bg-gray-50">
                    {headers.map((header) => (
                      <TableCell
                        key={`${rowIndex}-${header}`}
                        className="whitespace-nowrap px-4 py-2 text-sm border-b"
                        style={{ minWidth: '120px' }}
                        title={String(row[header])} // 鼠标悬停显示完整内容
                      >
                        <div className="max-w-[200px] truncate">
                          {String(row[header])}
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          {/* 显示总行数 */}
          <div className="px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t">
            Total: {data.length} records
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Database Query</h1>
      <Card>
        <CardHeader>
          <CardTitle>SQL Query</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter your SQL query here..."
            rows={5}
            className="font-mono"
          />
          <Button onClick={handleQuery} disabled={isLoading} className="mt-4">
            {isLoading ? "Querying..." : "Run Query"}
          </Button>
        </CardContent>
      </Card>

      {error && (
        <Card className="mt-4 bg-destructive/10">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <div className="mt-4">
          <h2 className="text-xl font-bold mb-2">Results</h2>
          {Object.keys(result).length > 0 ? (
             Object.entries(result).map(([tableName, data]) => renderTable(tableName, data))
          ) : (
            <p>The query executed successfully and returned no tables.</p>
          )}
        </div>
      )}
    </div>
  );
}