"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";

// Define a type for the API response
type QueryResult = {
  [tableName: string]: Record<string, any>[];
};

export default function DatabaseQueryPage() {
  const [query, setQuery] = useState("SELECT TOP 10 * FROM dbo.LOG_DATA;");
  const [result, setResult] = useState<QueryResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleQuery = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/database-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'An unknown error occurred.');
      }
      
      // Check if the result is an empty object, which is a valid success case
      if (Object.keys(data).length === 0) {
        toast({
          title: "Query Successful",
          description: "The query ran successfully but returned no data.",
        });
        setResult({});
      } else {
        setResult(data);
      }

    } catch (err: any) {
      setError(err.message);
      toast({
        title: "Error",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderTable = (tableName: string, data: Record<string, any>[]) => {
    if (data.length === 0) {
      return <p key={tableName}>Table '{tableName}' has no rows.</p>;
    }
    const headers = Object.keys(data[0]);
    return (
      <Card key={tableName} className="mt-4">
        <CardHeader>
          <CardTitle>{tableName}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  {headers.map((header) => (
                    <TableHead key={header}>{header}</TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((row, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {headers.map((header) => (
                      <TableCell key={`${rowIndex}-${header}`}>{String(row[header])}</TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Database Query</h1>
      <Card>
        <CardHeader>
          <CardTitle>SQL Query</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter your SQL query here..."
            rows={5}
            className="font-mono"
          />
          <Button onClick={handleQuery} disabled={isLoading} className="mt-4">
            {isLoading ? "Querying..." : "Run Query"}
          </Button>
        </CardContent>
      </Card>

      {error && (
        <Card className="mt-4 bg-destructive/10">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <div className="mt-4">
          <h2 className="text-xl font-bold mb-2">Results</h2>
          {Object.keys(result).length > 0 ? (
             Object.entries(result).map(([tableName, data]) => renderTable(tableName, data))
          ) : (
            <p>The query executed successfully and returned no tables.</p>
          )}
        </div>
      )}
    </div>
  );
}