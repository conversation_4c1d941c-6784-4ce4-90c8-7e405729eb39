"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { Download, Database, RefreshCw, Filter, Calendar, Hash, Type } from "lucide-react";

// Define a type for the API response
type QueryResult = {
  [tableName: string]: Record<string, any>[];
};

// Define types for column information
type ColumnInfo = {
  name: string;
  type: string;
  dataType: 'string' | 'number' | 'date' | 'boolean';
};

// Define types for filter conditions
type FilterCondition = {
  column: string;
  operator: string;
  value: string | number | [string | number, string | number];
  enabled: boolean;
};

type StringFilterOptions = {
  useRegex: boolean;
  exactMatch: boolean;
};

export default function DatabaseQueryPage() {
  const [query, setQuery] = useState("SELECT TOP 10 * FROM dbo.LOG_DATA;");
  const [result, setResult] = useState<QueryResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tables, setTables] = useState<string[]>([]);
  const [isLoadingTables, setIsLoadingTables] = useState(false);

  // 新增状态：结构化查询相关
  const [selectedTable, setSelectedTable] = useState<string>("");
  const [columns, setColumns] = useState<ColumnInfo[]>([]);
  const [isLoadingColumns, setIsLoadingColumns] = useState(false);
  const [filters, setFilters] = useState<FilterCondition[]>([]);
  const [showAdvancedQuery, setShowAdvancedQuery] = useState(false);

  // CSV导出函数
  const exportToCSV = (tableName: string, data: Record<string, any>[]) => {
    if (data.length === 0) {
      toast({
        title: "No Data",
        description: "No data to export.",
        variant: "destructive",
      });
      return;
    }

    const headers = Object.keys(data[0]);

    // 创建CSV内容
    const csvContent = [
      // CSV头部
      headers.join(','),
      // CSV数据行
      ...data.map(row =>
        headers.map(header => {
          const value = String(row[header] || '');
          // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    // 创建Blob并下载
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `${tableName}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Export Successful",
      description: `${tableName} exported to CSV successfully.`,
    });
  };

  // 获取数据库表列表
  const fetchTables = async () => {
    setIsLoadingTables(true);

    try {
      // 尝试多种不同的查询方式
      const queries = [
        "USE gina_db; SHOW TABLES;",
        "SHOW TABLES FROM gina_db;",
        "SELECT name FROM gina_db.sys.tables ORDER BY name;",
        "SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;",
      ];

      for (let i = 0; i < queries.length; i++) {
        try {
          console.log(`Trying query ${i + 1}: ${queries[i]}`);

          const response = await fetch('/api/database-query', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: queries[i]
            }),
          });

          const response_data = await response.json();

          if (response.ok) {
            console.log('Query succeeded:', response_data);

            // 提取表名列表
            const tableData = response_data.data;
            if (tableData && Object.keys(tableData).length > 0) {
              const firstKey = Object.keys(tableData)[0];
              const tableRows = tableData[firstKey];
              if (Array.isArray(tableRows) && tableRows.length > 0) {
                // 尝试不同的列名
                const possibleColumns = ['TABLE_NAME', 'name', 'Tables_in_gina_db'];
                let tableNames: string[] = [];

                // 首先尝试已知的列名
                for (const colName of possibleColumns) {
                  if (tableRows[0].hasOwnProperty(colName)) {
                    tableNames = tableRows.map((row: any) => String(row[colName])).filter(Boolean);
                    break;
                  }
                }

                // 如果没有找到已知列名，使用第一列
                if (tableNames.length === 0) {
                  tableNames = tableRows.map((row: any) => {
                    const values = Object.values(row);
                    return values.length > 0 ? String(values[0]) : null;
                  }).filter((name): name is string => Boolean(name));
                }

                if (tableNames.length > 0) {
                  setTables(tableNames);
                  console.log('Found tables:', tableNames);
                  return; // 成功获取表列表，退出函数
                }
              }
            }
          } else {
            console.log(`Query ${i + 1} failed:`, response_data.error);
          }
        } catch (err) {
          console.log(`Query ${i + 1} error:`, err);
          continue; // 尝试下一个查询
        }
      }

      // 所有查询都失败了
      throw new Error('All table listing queries failed. Please check database connection and permissions.');

    } catch (err: any) {
      console.error('Error fetching tables:', err);
      toast({
        title: "Error",
        description: "Failed to fetch table list: " + err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoadingTables(false);
    }
  };

  // 获取表的列信息
  const fetchColumns = async (tableName: string) => {
    if (!tableName) return;

    setIsLoadingColumns(true);
    try {
      const response = await fetch('/api/database-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '${tableName}' ORDER BY ORDINAL_POSITION;`
        }),
      });

      const response_data = await response.json();

      if (response.ok) {
        const tableData = response_data.data;
        if (tableData && Object.keys(tableData).length > 0) {
          const firstKey = Object.keys(tableData)[0];
          const columnRows = tableData[firstKey];
          if (Array.isArray(columnRows)) {
            const columnInfo: ColumnInfo[] = columnRows.map((row: any) => {
              const columnName = row.COLUMN_NAME || row.column_name || '';
              const dataType = (row.DATA_TYPE || row.data_type || '').toLowerCase();

              // 根据数据类型判断字段类型
              let fieldType: 'string' | 'number' | 'date' | 'boolean' = 'string';
              if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('numeric')) {
                fieldType = 'number';
              } else if (dataType.includes('date') || dataType.includes('time')) {
                fieldType = 'date';
              } else if (dataType.includes('bit') || dataType.includes('boolean')) {
                fieldType = 'boolean';
              }

              return {
                name: columnName,
                type: dataType,
                dataType: fieldType
              };
            });

            setColumns(columnInfo);
            console.log('Found columns:', columnInfo);
          }
        }
      } else {
        throw new Error(response_data.error || 'Failed to fetch columns.');
      }
    } catch (err: any) {
      console.error('Error fetching columns:', err);
      toast({
        title: "Error",
        description: "Failed to fetch column information: " + err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoadingColumns(false);
    }
  };

  // 页面加载时获取表列表
  useEffect(() => {
    fetchTables();
  }, []);

  // 处理表选择
  const handleTableSelect = (tableName: string) => {
    setSelectedTable(tableName);
    setQuery(`SELECT * FROM ${tableName} LIMIT 100;`);
    setFilters([]); // 清空之前的筛选条件
    fetchColumns(tableName); // 获取列信息
  };

  // 构建结构化查询
  const buildStructuredQuery = () => {
    if (!selectedTable) return "";

    let whereClause = "";
    const activeFilters = filters.filter(f => f.enabled && f.value !== "" && f.value !== null);

    if (activeFilters.length > 0) {
      const conditions = activeFilters.map(filter => {
        const column = filter.column;
        const value = filter.value;

        switch (filter.operator) {
          case 'equals':
            return `${column} = '${value}'`;
          case 'contains':
            return `${column} LIKE '%${value}%'`;
          case 'starts_with':
            return `${column} LIKE '${value}%'`;
          case 'ends_with':
            return `${column} LIKE '%${value}'`;
          case 'regex':
            return `${column} REGEXP '${value}'`;
          case 'greater_than':
            return `${column} > ${value}`;
          case 'less_than':
            return `${column} < ${value}`;
          case 'between':
            if (Array.isArray(value) && value.length === 2) {
              return `${column} BETWEEN ${value[0]} AND ${value[1]}`;
            }
            return `${column} = ${value}`;
          case 'date_range':
            if (Array.isArray(value) && value.length === 2) {
              return `${column} BETWEEN '${value[0]}' AND '${value[1]}'`;
            }
            return `${column} = '${value}'`;
          default:
            return `${column} = '${value}'`;
        }
      });

      whereClause = " WHERE " + conditions.join(" AND ");
    }

    return `SELECT * FROM ${selectedTable}${whereClause} LIMIT 100;`;
  };

  // 执行结构化查询
  const handleStructuredQuery = () => {
    const structuredQuery = buildStructuredQuery();
    setQuery(structuredQuery);
    handleQuery();
  };

  const handleQuery = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/database-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      const response_data = await response.json();

      if (!response.ok) {
        throw new Error(response_data.error || 'An unknown error occurred.');
      }

      // Extract the actual data from the new API response format
      const actualData = response_data.data || {};

      // Check if the result is an empty object, which is a valid success case
      if (Object.keys(actualData).length === 0) {
        toast({
          title: "Query Successful",
          description: "The query ran successfully but returned no data.",
        });
        setResult({});
      } else {
        setResult(actualData);
      }

    } catch (err: any) {
      setError(err.message);
      toast({
        title: "Error",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 添加筛选条件
  const addFilter = () => {
    if (columns.length === 0) return;

    const newFilter: FilterCondition = {
      column: columns[0].name,
      operator: 'equals',
      value: '',
      enabled: true
    };

    setFilters([...filters, newFilter]);
  };

  // 更新筛选条件
  const updateFilter = (index: number, updates: Partial<FilterCondition>) => {
    const newFilters = [...filters];
    newFilters[index] = { ...newFilters[index], ...updates };
    setFilters(newFilters);
  };

  // 删除筛选条件
  const removeFilter = (index: number) => {
    const newFilters = filters.filter((_, i) => i !== index);
    setFilters(newFilters);
  };

  const renderTable = (tableName: string, data: Record<string, any>[]) => {
    if (data.length === 0) {
      return <p key={tableName}>Table '{tableName}' has no rows.</p>;
    }
    const headers = Object.keys(data[0]);
    return (
      <Card key={tableName} className="mt-4">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {tableName}
              <span className="text-sm text-gray-500 font-normal">
                ({data.length} rows)
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => exportToCSV(tableName, data)}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export CSV
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {/* 水平和垂直滚动容器 */}
          <div className="overflow-auto max-h-[600px] border rounded-md">
            <Table>
              <TableHeader className="sticky top-0 bg-white z-10 shadow-sm">
                <TableRow>
                  {headers.map((header) => (
                    <TableHead
                      key={header}
                      className="whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2"
                      style={{ minWidth: '120px' }}
                    >
                      {header}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((row, rowIndex) => (
                  <TableRow key={rowIndex} className="hover:bg-gray-50">
                    {headers.map((header) => (
                      <TableCell
                        key={`${rowIndex}-${header}`}
                        className="whitespace-nowrap px-4 py-2 text-sm border-b"
                        style={{ minWidth: '120px' }}
                        title={String(row[header])} // 鼠标悬停显示完整内容
                      >
                        <div className="max-w-[200px] truncate">
                          {String(row[header])}
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          {/* 显示总行数 */}
          <div className="px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t">
            Total: {data.length} records
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Database Query</h1>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Database Query</span>
            <div className="flex items-center gap-2">
              <Label htmlFor="advanced-query" className="text-sm">
                Advanced SQL
              </Label>
              <Switch
                id="advanced-query"
                checked={showAdvancedQuery}
                onCheckedChange={setShowAdvancedQuery}
              />
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* 表选择下拉列表 */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              <Database className="inline h-4 w-4 mr-1" />
              Quick Table Selection (gina_db)
            </label>
            <div className="flex gap-2">
              <Select onValueChange={handleTableSelect}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={isLoadingTables ? "Loading tables..." : "Select a table to query"} />
                </SelectTrigger>
                <SelectContent>
                  {tables.map((tableName) => (
                    <SelectItem key={tableName} value={tableName}>
                      {tableName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchTables}
                disabled={isLoadingTables}
                className="flex items-center gap-1"
              >
                <RefreshCw className={`h-4 w-4 ${isLoadingTables ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
            {tables.length > 0 && (
              <p className="text-xs text-gray-500 mt-1">
                Found {tables.length} tables in gina_db database
              </p>
            )}
          </div>

          {/* 结构化查询界面 */}
          {!showAdvancedQuery && selectedTable && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-4">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filter Conditions for {selectedTable}
                </Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addFilter}
                  disabled={isLoadingColumns || columns.length === 0}
                >
                  Add Filter
                </Button>
              </div>

              {isLoadingColumns && (
                <div className="text-sm text-gray-500 mb-2">Loading columns...</div>
              )}

              {filters.length > 0 && (
                <div className="space-y-3 mb-4">
                  {filters.map((filter, index) => (
                    <div key={index} className="flex items-center gap-2 p-3 border rounded-lg">
                      <Switch
                        checked={filter.enabled}
                        onCheckedChange={(enabled) => updateFilter(index, { enabled })}
                      />

                      <Select
                        value={filter.column}
                        onValueChange={(column) => updateFilter(index, { column })}
                      >
                        <SelectTrigger className="w-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {columns.map((col) => (
                            <SelectItem key={col.name} value={col.name}>
                              <div className="flex items-center gap-2">
                                {col.dataType === 'string' && <Type className="h-3 w-3" />}
                                {col.dataType === 'number' && <Hash className="h-3 w-3" />}
                                {col.dataType === 'date' && <Calendar className="h-3 w-3" />}
                                {col.name}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Select
                        value={filter.operator}
                        onValueChange={(operator) => updateFilter(index, { operator })}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {(() => {
                            const selectedColumn = columns.find(col => col.name === filter.column);
                            if (selectedColumn?.dataType === 'string') {
                              return [
                                <SelectItem key="equals" value="equals">Equals</SelectItem>,
                                <SelectItem key="contains" value="contains">Contains</SelectItem>,
                                <SelectItem key="starts_with" value="starts_with">Starts with</SelectItem>,
                                <SelectItem key="ends_with" value="ends_with">Ends with</SelectItem>,
                                <SelectItem key="regex" value="regex">Regex</SelectItem>
                              ];
                            } else if (selectedColumn?.dataType === 'number') {
                              return [
                                <SelectItem key="equals" value="equals">Equals</SelectItem>,
                                <SelectItem key="greater_than" value="greater_than">Greater than</SelectItem>,
                                <SelectItem key="less_than" value="less_than">Less than</SelectItem>,
                                <SelectItem key="between" value="between">Between</SelectItem>
                              ];
                            } else if (selectedColumn?.dataType === 'date') {
                              return [
                                <SelectItem key="equals" value="equals">Equals</SelectItem>,
                                <SelectItem key="date_range" value="date_range">Date range</SelectItem>
                              ];
                            }
                            return [<SelectItem key="equals" value="equals">Equals</SelectItem>];
                          })()}
                        </SelectContent>
                      </Select>

                      {filter.operator === 'between' || filter.operator === 'date_range' ? (
                        <div className="flex items-center gap-1">
                          <Input
                            type={filter.operator === 'date_range' ? 'date' : 'text'}
                            placeholder="From"
                            className="w-24"
                            value={Array.isArray(filter.value) ? filter.value[0] : ''}
                            onChange={(e) => {
                              const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                              updateFilter(index, { value: [e.target.value, currentValue[1]] });
                            }}
                          />
                          <span className="text-sm text-gray-500">to</span>
                          <Input
                            type={filter.operator === 'date_range' ? 'date' : 'text'}
                            placeholder="To"
                            className="w-24"
                            value={Array.isArray(filter.value) ? filter.value[1] : ''}
                            onChange={(e) => {
                              const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                              updateFilter(index, { value: [currentValue[0], e.target.value] });
                            }}
                          />
                        </div>
                      ) : (
                        <Input
                          type={columns.find(col => col.name === filter.column)?.dataType === 'number' ? 'number' : 'text'}
                          placeholder="Value"
                          className="flex-1"
                          value={Array.isArray(filter.value) ? '' : filter.value}
                          onChange={(e) => updateFilter(index, { value: e.target.value })}
                        />
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFilter(index)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex gap-2">
                <Button onClick={handleStructuredQuery} disabled={isLoading} className="flex-1">
                  {isLoading ? "Querying..." : "Query with Filters"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setQuery(`SELECT * FROM ${selectedTable} LIMIT 100;`);
                    handleQuery();
                  }}
                  disabled={isLoading}
                >
                  Show All (100 rows)
                </Button>
              </div>
            </div>
          )}

          {/* 高级SQL查询输入框 */}
          {showAdvancedQuery && (
            <div className="mb-4">
              <Label className="block text-sm font-medium mb-2">
                Custom SQL Query
              </Label>
              <Textarea
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Enter your SQL query here..."
                rows={5}
                className="font-mono"
              />
              <Button onClick={handleQuery} disabled={isLoading} className="w-full mt-2">
                {isLoading ? "Querying..." : "Run Query"}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {error && (
        <Card className="mt-4 bg-destructive/10">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <div className="mt-4">
          <h2 className="text-xl font-bold mb-2">Results</h2>
          {Object.keys(result).length > 0 ? (
             Object.entries(result).map(([tableName, data]) => renderTable(tableName, data))
          ) : (
            <p>The query executed successfully and returned no tables.</p>
          )}
        </div>
      )}
    </div>
  );
}