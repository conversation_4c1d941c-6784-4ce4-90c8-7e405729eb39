(()=>{var e={};e.id=194,e.ids=[194],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51479:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>j,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>f});var r={};t.r(r),t.d(r,{GET:()=>g,POST:()=>m});var a=t(96559),o=t(48088),n=t(37719),u=t(32190),i=t(29021),c=t(33873),p=t.n(c);t(55511);let d=p().join(process.cwd(),"database-password.txt"),l="admin123";async function x(){try{return(await i.promises.readFile(d,"utf-8")).trim()}catch(e){return console.log("密码文件不存在，创建默认密码文件"),await i.promises.writeFile(d,l,"utf-8"),l}}async function m(e){try{let{password:s,action:t}=await e.json();if("verify"===t){let e=await x();if(s===e)return u.NextResponse.json({success:!0,message:"密码验证成功"});return u.NextResponse.json({success:!1,message:"密码错误"},{status:401})}if("change"!==t)return u.NextResponse.json({success:!1,message:"无效的操作"},{status:400});{let{oldPassword:s,newPassword:t}=await e.json(),r=await x();if(s!==r)return u.NextResponse.json({success:!1,message:"原密码错误"},{status:401});return await i.promises.writeFile(d,t,"utf-8"),u.NextResponse.json({success:!0,message:"密码修改成功"})}}catch(e){return console.error("密码验证API错误:",e),u.NextResponse.json({success:!1,message:"服务器错误: "+e.message},{status:500})}}async function g(){try{let e=await x();return u.NextResponse.json({success:!0,message:"密码文件已就绪",hasPassword:!0,defaultPassword:e===l?l:null})}catch(e){return console.error("获取密码状态错误:",e),u.NextResponse.json({success:!1,message:"服务器错误: "+e.message},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth-database/route",pathname:"/api/auth-database",filename:"route",bundlePath:"app/api/auth-database/route"},resolvedPagePath:"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\api\\auth-database\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:w,workUnitAsyncStorage:f,serverHooks:j}=h;function y(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:f})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[719,580],()=>t(51479));module.exports=r})();