"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/database-query/page.tsx":
/*!*************************************************!*\
  !*** ./app/(dashboard)/database-query/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseQueryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DatabaseQueryPage() {\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SELECT TOP 10 * FROM dbo.LOG_DATA;\");\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tables, setTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingTables, setIsLoadingTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 新增状态：结构化查询相关\n    const [selectedTable, setSelectedTable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [columns, setColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingColumns, setIsLoadingColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // CSV导出函数\n    const exportToCSV = (tableName, data)=>{\n        if (data.length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"No Data\",\n                description: \"No data to export.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const headers = Object.keys(data[0]);\n        // 创建CSV内容\n        const csvContent = [\n            // CSV头部\n            headers.join(','),\n            // CSV数据行\n            ...data.map((row)=>headers.map((header)=>{\n                    const value = String(row[header] || '');\n                    // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号\n                    if (value.includes(',') || value.includes('\"') || value.includes('\\n')) {\n                        return '\"'.concat(value.replace(/\"/g, '\"\"'), '\"');\n                    }\n                    return value;\n                }).join(','))\n        ].join('\\n');\n        // 创建Blob并下载\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat(tableName, \"_\").concat(new Date().toISOString().slice(0, 19).replace(/:/g, '-'), \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n            title: \"Export Successful\",\n            description: \"\".concat(tableName, \" exported to CSV successfully.\")\n        });\n    };\n    // 获取数据库表列表\n    const fetchTables = async ()=>{\n        setIsLoadingTables(true);\n        try {\n            // 尝试多种不同的查询方式\n            const queries = [\n                \"USE gina_db; SHOW TABLES;\",\n                \"SHOW TABLES FROM gina_db;\",\n                \"SELECT name FROM gina_db.sys.tables ORDER BY name;\",\n                \"SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;\"\n            ];\n            for(let i = 0; i < queries.length; i++){\n                try {\n                    console.log(\"Trying query \".concat(i + 1, \": \").concat(queries[i]));\n                    const response = await fetch('/api/database-query', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            query: queries[i]\n                        })\n                    });\n                    const response_data = await response.json();\n                    if (response.ok) {\n                        console.log('Query succeeded:', response_data);\n                        // 提取表名列表\n                        const tableData = response_data.data;\n                        if (tableData && Object.keys(tableData).length > 0) {\n                            const firstKey = Object.keys(tableData)[0];\n                            const tableRows = tableData[firstKey];\n                            if (Array.isArray(tableRows) && tableRows.length > 0) {\n                                // 尝试不同的列名\n                                const possibleColumns = [\n                                    'TABLE_NAME',\n                                    'name',\n                                    'Tables_in_gina_db'\n                                ];\n                                let tableNames = [];\n                                // 首先尝试已知的列名\n                                for (const colName of possibleColumns){\n                                    if (tableRows[0].hasOwnProperty(colName)) {\n                                        tableNames = tableRows.map((row)=>String(row[colName])).filter(Boolean);\n                                        break;\n                                    }\n                                }\n                                // 如果没有找到已知列名，使用第一列\n                                if (tableNames.length === 0) {\n                                    tableNames = tableRows.map((row)=>{\n                                        const values = Object.values(row);\n                                        return values.length > 0 ? String(values[0]) : null;\n                                    }).filter((name)=>Boolean(name));\n                                }\n                                if (tableNames.length > 0) {\n                                    setTables(tableNames);\n                                    console.log('Found tables:', tableNames);\n                                    return; // 成功获取表列表，退出函数\n                                }\n                            }\n                        }\n                    } else {\n                        console.log(\"Query \".concat(i + 1, \" failed:\"), response_data.error);\n                    }\n                } catch (err) {\n                    console.log(\"Query \".concat(i + 1, \" error:\"), err);\n                    continue; // 尝试下一个查询\n                }\n            }\n            // 所有查询都失败了\n            throw new Error('所有表列表查询都失败了。请检查数据库连接和权限。');\n        } catch (err) {\n            console.error('Error fetching tables:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: \"获取表列表失败: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingTables(false);\n        }\n    };\n    // 获取表的列信息\n    const fetchColumns = async (tableName)=>{\n        if (!tableName) return;\n        setIsLoadingColumns(true);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: \"SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '\".concat(tableName, \"' ORDER BY ORDINAL_POSITION;\")\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                const tableData = response_data.data;\n                if (tableData && Object.keys(tableData).length > 0) {\n                    const firstKey = Object.keys(tableData)[0];\n                    const columnRows = tableData[firstKey];\n                    if (Array.isArray(columnRows)) {\n                        const columnInfo = columnRows.map((row)=>{\n                            const columnName = row.COLUMN_NAME || row.column_name || '';\n                            const dataType = (row.DATA_TYPE || row.data_type || '').toLowerCase();\n                            // 根据数据类型判断字段类型\n                            let fieldType = 'string';\n                            if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('numeric') || dataType.includes('double') || dataType.includes('real') || dataType.includes('money') || dataType.includes('smallmoney')) {\n                                fieldType = 'number';\n                            } else if (dataType.includes('date') || dataType.includes('time') || dataType.includes('timestamp')) {\n                                fieldType = 'date';\n                            } else if (dataType.includes('bit') || dataType.includes('boolean')) {\n                                fieldType = 'boolean';\n                            }\n                            return {\n                                name: columnName,\n                                type: dataType,\n                                dataType: fieldType\n                            };\n                        });\n                        // 去重处理，以防有重复的列名\n                        const uniqueColumns = columnInfo.filter((col, index, self)=>index === self.findIndex((c)=>c.name === col.name));\n                        setColumns(uniqueColumns);\n                        console.log('Found columns:', uniqueColumns);\n                    }\n                }\n            } else {\n                throw new Error(response_data.error || '获取列信息失败。');\n            }\n        } catch (err) {\n            console.error('Error fetching columns:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: \"获取列信息失败: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingColumns(false);\n        }\n    };\n    // 页面加载时获取表列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DatabaseQueryPage.useEffect\": ()=>{\n            fetchTables();\n        }\n    }[\"DatabaseQueryPage.useEffect\"], []);\n    // 处理表选择\n    const handleTableSelect = (tableName)=>{\n        setSelectedTable(tableName);\n        setQuery(\"SELECT * FROM \".concat(tableName, \" LIMIT 100;\"));\n        setFilters([]); // 清空之前的筛选条件\n        fetchColumns(tableName); // 获取列信息\n    };\n    // 构建结构化查询\n    const buildStructuredQuery = ()=>{\n        if (!selectedTable) return \"\";\n        let whereClause = \"\";\n        const activeFilters = filters.filter((f)=>f.enabled && f.value !== \"\" && f.value !== null);\n        if (activeFilters.length > 0) {\n            const conditions = activeFilters.map((filter)=>{\n                const column = filter.column;\n                const value = filter.value;\n                switch(filter.operator){\n                    case 'equals':\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    case 'contains':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"%'\");\n                    case 'starts_with':\n                        return \"\".concat(column, \" LIKE '\").concat(value, \"%'\");\n                    case 'ends_with':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"'\");\n                    case 'regex':\n                        return \"\".concat(column, \" REGEXP '\").concat(value, \"'\");\n                    case 'greater_than':\n                        return \"\".concat(column, \" > \").concat(value);\n                    case 'less_than':\n                        return \"\".concat(column, \" < \").concat(value);\n                    case 'between':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN \").concat(value[0], \" AND \").concat(value[1]);\n                        }\n                        return \"\".concat(column, \" = \").concat(value);\n                    case 'date_range':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN '\").concat(value[0], \"' AND '\").concat(value[1], \"'\");\n                        }\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    default:\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                }\n            });\n            whereClause = \" WHERE \" + conditions.join(\" AND \");\n        }\n        return \"SELECT * FROM \".concat(selectedTable).concat(whereClause, \" LIMIT 100;\");\n    };\n    // 执行结构化查询\n    const handleStructuredQuery = async ()=>{\n        const structuredQuery = buildStructuredQuery();\n        setQuery(structuredQuery);\n        // 直接执行查询，不依赖状态更新\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: structuredQuery\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                setResult(response_data.data);\n            } else {\n                throw new Error(response_data.error || '查询失败。');\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleQuery = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query\n                })\n            });\n            const response_data = await response.json();\n            if (!response.ok) {\n                throw new Error(response_data.error || '发生未知错误。');\n            }\n            // Extract the actual data from the new API response format\n            const actualData = response_data.data || {};\n            // Check if the result is an empty object, which is a valid success case\n            if (Object.keys(actualData).length === 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"Query Successful\",\n                    description: \"The query ran successfully but returned no data.\"\n                });\n                setResult({});\n            } else {\n                setResult(actualData);\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 添加筛选条件\n    const addFilter = ()=>{\n        if (columns.length === 0) return;\n        const newFilter = {\n            column: columns[0].name,\n            operator: 'equals',\n            value: '',\n            enabled: true\n        };\n        setFilters([\n            ...filters,\n            newFilter\n        ]);\n    };\n    // 更新筛选条件\n    const updateFilter = (index, updates)=>{\n        const newFilters = [\n            ...filters\n        ];\n        newFilters[index] = {\n            ...newFilters[index],\n            ...updates\n        };\n        setFilters(newFilters);\n    };\n    // 删除筛选条件\n    const removeFilter = (index)=>{\n        const newFilters = filters.filter((_, i)=>i !== index);\n        setFilters(newFilters);\n    };\n    const renderTable = (tableName, data)=>{\n        if (data.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Table '\",\n                    tableName,\n                    \"' has no rows.\"\n                ]\n            }, tableName, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 441,\n                columnNumber: 14\n            }, this);\n        }\n        const headers = Object.keys(data[0]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    tableName,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 font-normal\",\n                                        children: [\n                                            \"(\",\n                                            data.length,\n                                            \" rows)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>exportToCSV(tableName, data),\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Export CSV\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto max-h-[600px] border rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHeader, {\n                                        className: \"sticky top-0 bg-white z-10 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                            children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2\",\n                                                    style: {\n                                                        minWidth: '120px'\n                                                    },\n                                                    children: header\n                                                }, header, false, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableBody, {\n                                        children: data.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                                className: \"hover:bg-gray-50\",\n                                                children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"whitespace-nowrap px-4 py-2 text-sm border-b\",\n                                                        style: {\n                                                            minWidth: '120px'\n                                                        },\n                                                        title: String(row[header]),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-w-[200px] truncate\",\n                                                            children: String(row[header])\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, \"\".concat(rowIndex, \"-\").concat(header), false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, rowIndex, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t\",\n                            children: [\n                                \"总计: \",\n                                data.length,\n                                \" 条记录\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, tableName, true, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n            lineNumber: 445,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"数据库查询\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 513,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"数据库查询\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"快速表选择 (gina_db)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                onValueChange: handleTableSelect,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                        className: \"w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                            placeholder: isLoadingTables ? \"加载表中...\" : \"选择要查询的表\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                        children: tables.map((tableName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                value: tableName,\n                                                                children: tableName\n                                                            }, tableName, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: fetchTables,\n                                                disabled: isLoadingTables,\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(isLoadingTables ? 'animate-spin' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"刷新\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this),\n                                    tables.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"在 gina_db 数据库中找到 \",\n                                            tables.length,\n                                            \" 个表\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedTable,\n                                                    \" 的筛选条件\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: addFilter,\n                                                disabled: isLoadingColumns || columns.length === 0,\n                                                children: \"添加筛选\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this),\n                                    isLoadingColumns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 mb-2\",\n                                        children: \"加载列中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 17\n                                    }, this),\n                                    filters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-4\",\n                                        children: filters.map((filter, index)=>{\n                                            var _columns_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                        checked: filter.enabled,\n                                                        onCheckedChange: (enabled)=>updateFilter(index, {\n                                                                enabled\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.column,\n                                                        onValueChange: (column)=>updateFilter(index, {\n                                                                column\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-40\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: columns.map((col, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: col.name,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                col.dataType === 'string' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 598,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 599,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'date' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 600,\n                                                                                    columnNumber: 61\n                                                                                }, this),\n                                                                                col.name,\n                                                                                \" (\",\n                                                                                col.type,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, \"\".concat(col.name, \"-\").concat(colIndex), false, {\n                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.operator,\n                                                        onValueChange: (operator)=>updateFilter(index, {\n                                                                operator\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: (()=>{\n                                                                    const selectedColumn = columns.find((col)=>col.name === filter.column);\n                                                                    if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'string') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"Equals\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"contains\",\n                                                                                children: \"Contains\"\n                                                                            }, \"contains\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"starts_with\",\n                                                                                children: \"Starts with\"\n                                                                            }, \"starts_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 622,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"ends_with\",\n                                                                                children: \"Ends with\"\n                                                                            }, \"ends_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 623,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"regex\",\n                                                                                children: \"Regex\"\n                                                                            }, \"regex\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'number') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"Equals\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 628,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"greater_than\",\n                                                                                children: \"Greater than\"\n                                                                            }, \"greater_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"less_than\",\n                                                                                children: \"Less than\"\n                                                                            }, \"less_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 630,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"between\",\n                                                                                children: \"Between\"\n                                                                            }, \"between\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 631,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'date') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"Equals\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"date_range\",\n                                                                                children: \"Date range\"\n                                                                            }, \"date_range\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 636,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    }\n                                                                    return [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: \"equals\",\n                                                                            children: \"Equals\"\n                                                                        }, \"equals\", false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ];\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    filter.operator === 'between' || filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 654,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[0] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[0]), \"yyyy-MM-dd\") : \"From date\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 650,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[0] ? new Date(filter.value[0]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            dateStr,\n                                                                                            currentValue[1]\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 662,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"to\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 681,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[1] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[1]), \"yyyy-MM-dd\") : \"To date\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 676,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[1] ? new Date(filter.value[1]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            currentValue[0],\n                                                                                            dateStr\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 689,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"From\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[0] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                e.target.value,\n                                                                                currentValue[1]\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"to\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"To\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[1] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                currentValue[0],\n                                                                                e.target.value\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        type: ((_columns_find = columns.find((col)=>col.name === filter.column)) === null || _columns_find === void 0 ? void 0 : _columns_find.dataType) === 'number' ? 'number' : 'text',\n                                                        placeholder: \"Value\",\n                                                        className: \"flex-1\",\n                                                        value: Array.isArray(filter.value) ? '' : filter.value,\n                                                        onChange: (e)=>updateFilter(index, {\n                                                                value: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeFilter(index),\n                                                        children: \"Remove\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: handleStructuredQuery,\n                                                disabled: isLoading,\n                                                className: \"flex-1\",\n                                                children: isLoading ? \"查询中...\" : \"使用筛选查询\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    setQuery(\"SELECT * FROM \".concat(selectedTable, \" LIMIT 100;\"));\n                                                    handleQuery();\n                                                },\n                                                disabled: isLoading,\n                                                children: \"显示全部 (100行)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                onClick: async ()=>{\n                                                    // 测试 COUNT 查询\n                                                    const countQuery = \"SELECT COUNT(*) as total FROM \".concat(selectedTable, \";\");\n                                                    console.log('Testing COUNT query:', countQuery);\n                                                    const response = await fetch('/api/database-query', {\n                                                        method: 'POST',\n                                                        headers: {\n                                                            'Content-Type': 'application/json'\n                                                        },\n                                                        body: JSON.stringify({\n                                                            query: countQuery\n                                                        })\n                                                    });\n                                                    const data = await response.json();\n                                                    console.log('COUNT query result:', data);\n                                                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                                                        title: \"COUNT查询测试\",\n                                                        description: \"查询结果已输出到控制台\"\n                                                    });\n                                                },\n                                                disabled: isLoading || !selectedTable,\n                                                size: \"sm\",\n                                                children: \"测试COUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        className: \"text-xs font-medium text-gray-600 mb-1 block\",\n                                        children: \"生成的查询语句:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"text-xs text-gray-800 font-mono\",\n                                        children: buildStructuredQuery() || \"SELECT * FROM \".concat(selectedTable, \" LIMIT 100;\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 798,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 514,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mt-4 bg-destructive/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-destructive\",\n                            children: \"错误\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 813,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 811,\n                columnNumber: 9\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-2\",\n                        children: \"Results\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 823,\n                        columnNumber: 11\n                    }, this),\n                    Object.keys(result).length > 0 ? Object.entries(result).map((param)=>{\n                        let [tableName, data] = param;\n                        return renderTable(tableName, data);\n                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"The query executed successfully and returned no tables.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 822,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n        lineNumber: 512,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseQueryPage, \"37jn/e4rdN0iVNZCMb7xeH+UoSk=\");\n_c = DatabaseQueryPage;\nvar _c;\n$RefreshReg$(_c, \"DatabaseQueryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/database-query/page.tsx\n"));

/***/ })

});