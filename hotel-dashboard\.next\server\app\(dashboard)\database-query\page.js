(()=>{var e={};e.id=643,e.ids=[643],e.modules={2756:(e,t,r)=>{Promise.resolve().then(r.bind(r,66369))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8340:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\database-query\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},37668:(e,t,r)=>{Promise.resolve().then(r.bind(r,8340))},46059:(e,t,r)=>{"use strict";r.d(t,{C:()=>s});var n=r(43210),o=r(98599),a=r(66156),s=e=>{let{present:t,children:r}=e,s=function(e){var t,r;let[o,s]=n.useState(),d=n.useRef({}),u=n.useRef(e),l=n.useRef("none"),[p,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=i(d.current);l.current="mounted"===p?e:"none"},[p]),(0,a.N)(()=>{let t=d.current,r=u.current;if(r!==e){let n=l.current,o=i(t);e?c("MOUNT"):"none"===o||t?.display==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),u.current=e}},[e,c]),(0,a.N)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=i(d.current).includes(r.animationName);if(r.target===o&&n&&(c("ANIMATION_END"),!u.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(l.current=i(d.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(p),ref:n.useCallback(e=>{e&&(d.current=getComputedStyle(e)),s(e)},[])}}(t),d="function"==typeof r?r({present:s.isPresent}):n.Children.only(r),u=(0,o.s)(s.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(d));return"function"==typeof r||s.isPresent?n.cloneElement(d,{ref:u}):null};function i(e){return e?.animationName||"none"}s.displayName="Presence"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},99371:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>c,tree:()=>u});var n=r(65239),o=r(48088),a=r(88170),s=r.n(a),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let u={children:["",{children:["(dashboard)",{children:["database-query",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8340)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\database-query\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\database-query\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(dashboard)/database-query/page",pathname:"/database-query",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[719,825,569,571,97,541,382],()=>r(99371));module.exports=n})();