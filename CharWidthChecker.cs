using System;
using System.Linq;

namespace CharWidthExample
{
    /// <summary>
    /// 提供用于检查半角和全角字符的辅助方法。
    /// </summary>
    public static class CharWidthUtils
    {
        /// <summary>
        /// 判断一个字符是否为半角字符。
        /// 半角字符通常指 ASCII 字符，其 Unicode 值小于等于 255 (&#92;u00FF)。
        /// </summary>
        /// <param name="c">要检查的字符。</param>
        /// <returns>如果字符是半角，则为 true；否则为 false。</returns>
        public static bool IsHalfWidth(char c)
        {
            return c <= '\u00FF';
        }

        /// <summary>
        /// 判断一个字符是否为全角字符。
        /// 全角字符通常指 CJK 字符、全角形式的字母数字等，其 Unicode 值大于 255 (&#92;u00FF)。
        /// </summary>
        /// <param name="c">要检查的字符。</param>
        /// <returns>如果字符是全角，则为 true；否则为 false。</returns>
        public static bool IsFullWidth(char c)
        {
            return c > '\u00FF';
        }

        /// <summary>
        /// 检查字符串是否完全由半角字符组成。
        /// </summary>
        /// <param name="s">要检查的字符串。</param>
        /// <returns>如果所有字符都是半角，则为 true；否则为 false。</returns>
        public static bool IsAllHalfWidth(string s)
        {
            if (string.IsNullOrEmpty(s))
            {
                return true; // 空字符串或 null 可被视作符合要求
            }
            return s.All(IsHalfWidth);
        }

        /// <summary>
        /// 检查字符串是否完全由全角字符组成。
        /// </summary>
        /// <param name="s">要检查的字符串。</param>
        /// <returns>如果所有字符都是全角，则为 true；否则为 false。</returns>
        public static bool IsAllFullWidth(string s)
        {
            if (string.IsNullOrEmpty(s))
            {
                return true; // 空字符串或 null 可被视作符合要求
            }
            return s.All(IsFullWidth);
        }

        /// <summary>
        /// 检查字符串是否包含任何全角字符。
        /// </summary>
        /// <param name="s">要检查的字符串。</param>
        /// <returns>如果包含至少一个全角字符，则为 true；否则为 false。</returns>
        public static bool ContainsFullWidth(string s)
        {
            if (string.IsNullOrEmpty(s))
            {
                return false;
            }
            return s.Any(IsFullWidth);
        }

        /// <summary>
        /// 将字符串中的全角字符（字母、数字、符号）转换为对应的半角字符。
        /// </summary>
        /// <param name="fullWidthStr">包含全角字符的字符串。</param>
        /// <returns>转换后的字符串。</returns>
        public static string ToHalfWidth(string fullWidthStr)
        {
            if (string.IsNullOrEmpty(fullWidthStr))
            {
                return fullWidthStr;
            }

            var sb = new System.Text.StringBuilder(fullWidthStr.Length);
            foreach (char c in fullWidthStr)
            {
                // 全角空格转半角空格
                if (c == '\u3000')
                {
                    sb.Append('\u0020');
                }
                // 全角字母、数字、符号转半角
                else if (c >= '\uFF01' && c <= '\uFF5E')
                {
                    sb.Append((char)(c - 65248));
                }
                else
                {
                    sb.Append(c); // 其他字符（如中文字符）不变
                }
            }
            return sb.ToString();
        }
    }

    public class Program
    {
        public static void Main(string[] args)
        {
            string text1 = "Hello World 123!@#";
            string text2 = "你好，世界！１２３";
            string text3 = "Hello 你好";

            Console.WriteLine($"--- 分析: '{text1}' ---");
            Console.WriteLine($"完全是半角吗? {CharWidthUtils.IsAllHalfWidth(text1)}"); // 预期: True
            Console.WriteLine($"完全是全角吗? {CharWidthUtils.IsAllFullWidth(text1)}"); // 预期: False
            Console.WriteLine($"包含全角字符吗? {CharWidthUtils.ContainsFullWidth(text1)}"); // 预期: False

            Console.WriteLine();

            Console.WriteLine($"--- 分析: '{text2}' ---");
            Console.WriteLine($"完全是半角吗? {CharWidthUtils.IsAllHalfWidth(text2)}"); // 预期: False
            Console.WriteLine($"完全是全角吗? {CharWidthUtils.IsAllFullWidth(text2)}"); // 预期: True
            Console.WriteLine($"包含全角字符吗? {CharWidthUtils.ContainsFullWidth(text2)}"); // 预期: True

            Console.WriteLine();

            Console.WriteLine($"--- 分析: '{text3}' ---");
            Console.WriteLine($"完全是半角吗? {CharWidthUtils.IsAllHalfWidth(text3)}"); // 预期: False
            Console.WriteLine($"完全是全角吗? {CharWidthUtils.IsAllFullWidth(text3)}"); // 预期: False
            Console.WriteLine($"包含全角字符吗? {CharWidthUtils.ContainsFullWidth(text3)}"); // 预期: True

            Console.WriteLine("\n--- 如何在输入框事件中使用 ---");
            Console.WriteLine("模拟一个文本框的 TextChanged 事件。");
            string userInput = "测试Text123";
            if (CharWidthUtils.ContainsFullWidth(userInput))
            {
                Console.WriteLine($"输入 '{userInput}' 包含全角字符，不符合要求。");
            }
            else
            {
                Console.WriteLine($"输入 '{userInput}' 是合法的（全是半角）。");
            }
            
            string userInput2 = "OK123";
            if (CharWidthUtils.ContainsFullWidth(userInput2))
            {
                Console.WriteLine($"输入 '{userInput2}' 包含全角字符，不符合要求。");
            }
            else
            {
                Console.WriteLine($"输入 '{userInput2}' 是合法的（全是半角）。");
            }

            Console.WriteLine("\n--- 演示强制转换为半角 ---");
            string fullWidthText = "ＨＥＬＬＯ　ＷＯＲＬＤ！１２３";
            string halfWidthText = CharWidthUtils.ToHalfWidth(fullWidthText);
            Console.WriteLine($"原文: '{fullWidthText}'");
            Console.WriteLine($"转换后: '{halfWidthText}'");

            string mixedText = "你好，World，１２３";
            string convertedMixedText = CharWidthUtils.ToHalfWidth(mixedText);
            Console.WriteLine($"原文: '{mixedText}'");
            Console.WriteLine($"转换后: '{convertedMixedText}'");
        }
    }
}