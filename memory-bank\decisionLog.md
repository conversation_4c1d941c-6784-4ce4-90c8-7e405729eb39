# Decision Log

This file records architectural and implementation decisions using a list format.
2025-06-10 15:10:54 - Log of updates made.

*
---
---
### Decision (UI Layout)
[2025-06-20 14:34:00] - [Refactor: 调整日志分析页面的布局以避免组件重叠]

**Rationale:**
*   **问题:** 用户反馈在日志分析页面上，图表视图遮挡了文件上传和块选择组件。
*   **根本原因:** 页面使用了复杂的 Flexbox 布局，其中一个容器具有 `flex-grow`，而另一个容器具有固定高度，这可能在某些屏幕尺寸下导致重叠。
*   **解决方案:** 采用更简单、更健壮的 Grid 布局。将页面分为一个两行的网格。第一行包含文件上传和块选择组件（在桌面上并排显示），第二行包含图表视图。这确保了元素在垂直方向上清晰地分离，并且图表可以自然地填充剩余的垂直空间，而不会重叠。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx)
*   **更改:**
    1.  将主要的 Flexbox 布局 (`flex flex-col md:flex-row`) 替换为 `grid grid-cols-1 md:grid-cols-3 gap-4`。
    2.  将 `LogFileUpload` 放置在第一列 (`md:col-span-1`)。
    3.  将 `LogDisplayArea` 放置在第二列，并使其占据两列的宽度 (`md:col-span-2`)。
    4.  将图表容器的固定高度 `h-[450px]` 移除，并添加 `flex-grow`，使其能够填充剩余的垂直空间。
---
### Decision (Refactor)
[2025-06-20 14:22:00] - [Refactor: 恢复日志分析页面的文件上传工作流]

**Rationale:**
*   **问题:** 前一次修复引入了一个模拟 API (`/api/log-data`)，这破坏了核心的文件上传和客户端处理功能，导致页面无法按预期工作。
*   **目标:** 恢复原始的、正确的工作流程，即用户上传日志文件，文件在客户端通过 Web Worker 处理，然后显示分析结果。
*   **解决方案:**
    1.  **移除模拟 API:** 完全清空 `hotel-dashboard/app/api/log-data/route.ts` 以删除无效的后端端点。
    2.  **恢复前端逻辑:** 修改 `hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`，移除在页面加载时调用 API 的 `useEffect`。
    3.  **重新集成上传组件:** 在页面上重新渲染 `LogFileUpload` 组件，并将数据处理回调函数 (`handleDataProcessed`, `handleError`, etc.) 传递给它。
    4.  **修复类型错误:** 解决在重构过程中出现的 TypeScript 类型不匹配问题，确保 `page.tsx` 和 `LogFileUpload.tsx` 之间的 props 和回调函数类型一致。

**Implementation Details:**
*   **文件:**
    *   [`hotel-dashboard/app/api/log-data/route.ts`](hotel-dashboard/app/api/log-data/route.ts)
    *   [`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx)
    *   [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
*   **更改:**
    *   `route.ts` 被清空。
    *   `page.tsx` 移除了 `fetch` 调用，重新引入了 `LogFileUpload`，并调整了布局和状态管理。
    *   `LogFileUpload.tsx` 的 props 接口被更新以接受 `disabled` 属性，增强了其可控性。
---
### Decision (Bug Fix)
[2025-06-20 13:27:38] - [Bug Fix: 修复前端因模拟数据缺少属性导致的 `TypeError`]

**Rationale:**
*   **问题:** 应用在 `LogDisplayArea.tsx` 中抛出 `TypeError: Cannot read properties of undefined (reading 'length')` 错误。
*   **根本原因:** `/api/log-data` 端点返回的模拟数据对象数组中，每个对象都缺少 `glue_thickness_values` 和 `collimation_diff_values` 属性，而前端组件期望这些属性存在并为数组类型。
*   **解决方案:** 修改 `hotel-dashboard/app/api/log-data/route.ts` 中的模拟数据，为 `processedBlocks` 数组中的每个对象添加 `glue_thickness_values: []` 和 `collimation_diff_values: []`。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/app/api/log-data/route.ts`](hotel-dashboard/app/api/log-data/route.ts)
*   **更改:**
    1.  更新了 `mockLogData` 常量，为 `processedBlocks` 数组中的每个对象添加了 `glue_thickness_values` 和 `collimation_diff_values` 属性，并将其初始化为空数组 `[]`。
    2.  同时将 `id` 字段重命名为 `block_id` 以匹配前端组件的期望。
---
### Decision (Bug Fix)
[2025-06-20 13:19:39] - [Bug Fix: 修复 /api/log-data 接口返回数据格式不匹配的问题]

**Rationale:**
*   **问题:** 应用在访问 `/api/log-data` 后抛出 `Error: Invalid data format` 错误。
*   **根本原因:** 前端组件 `hotel-dashboard/app/(dashboard)/log-analysis/page.tsx` 期望从 API 收到一个 JSON **数组**，但后端 API `hotel-dashboard/app/api/log-data/route.ts` 的 `GET` 处理程序返回的是一个包含数组的**对象**。
*   **解决方案:** 修改 `GET` 处理程序，使其直接返回模拟数据对象中的 `processedBlocks` 数组，以匹配前端期望的数据结构。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/app/api/log-data/route.ts`](hotel-dashboard/app/api/log-data/route.ts)
*   **更改:**
    1.  在 `GET` 函数中，将 `return NextResponse.json(mockLogData);` 修改为 `return NextResponse.json(mockLogData.processedBlocks);`。
    2.  这确保了 API 响应体直接为前端期望的数组格式，解决了数据格式不匹配的错误。
---
### Decision (Bug Fix)
[2025-06-17 13:39:00] - [Bug Fix: 通过将绘图函数移入useEffect彻底解决SSR构建失败问题]

**Rationale:**
*   **问题:** 即便在将 `dpr` 作为参数传递后，`next build` 命令仍然失败。
*   **根本原因:** 问题的根源在于，任何在组件顶层作用域内定义的、包含了浏览器特有API（如 `document.createElement`）的辅助函数，都可能在Next.js的服务器端构建过程中被分析和评估，从而导致构建失败，即使这些函数只在客户端的 `useEffect` 中被调用。
*   **最终解决方案:** 将所有包含浏览器特有API的绘图辅助函数（`getShapeCenter`, `drawMtfPattern`, `drawShape`）的定义完全移入到 `Canvas.tsx` 的 `useEffect` hook 内部。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    1.  **函数迁移:** `getShapeCenter`, `drawMtfPattern`, 和 `drawShape` 三个函数的完整定义被从组件的顶层作用域剪切并粘贴到了 `useEffect` hook 的内部。
    2.  **作用域:** 这确保了这些函数及其对 `document.createElement` 的调用只在客户端的 `useEffect` hook 的上下文中存在和执行，完全避免了服务器端对它们的任何接触。
    3.  **依赖复制:** 由于 `findShapeAt` 函数（由事件处理器调用，不能移入 `useEffect`）依赖 `getShapeCenter` 的逻辑，因此将计算中心点的逻辑复制到了 `findShapeAt` 内部，以解除其对现在位于 `useEffect` 内部的函数的依赖。
    4.  这个重构是解决此类SSR问题的最稳健的方法，它保证了服务器端构建环境的纯净性。
---
### Decision (Feature Enhancement)
[2025-06-17 13:21:00] - [Enhancement: 完善二值化逻辑以处理白色背景]

**Rationale:**
*   **用户反馈:** 用户要求更新二值化逻辑，使灰度值低于200的像素变为黑色，高于或等于200的像素变为白色。
*   **问题:** 之前的实现只处理了转黑色的情况，未处理其他像素，这可能导致背景或浅色区域保留原始的、未经处理的颜色。
*   **解决方案:** 在二值化函数中添加 `else` 分支，将所有不符合“转黑”条件的像素强制转换为纯白色 `(255, 255, 255)`。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
*   **更改:**
    *   在 `exportCanvas` 函数内的 `applyBinarizationToContext` 辅助函数中，修改了像素处理循环：
        ```javascript
        if (grayscale < 200) {
          // ... set to black
        } else {
          data[i] = 255;   // R
          data[i + 1] = 255; // G
          data[i + 2] = 255; // B
        }
        ```
---
### Decision (Bug Fix & Feature)
[2025-06-17 13:17:00] - [Fix: 将二值化逻辑移至导出功能以解决无效问题]

**Rationale:**
*   **问题:** 用户反馈之前实现的“二值化”开关对导出的图片不起作用。
*   **根本原因:** `exportCanvas` 函数在一个临时的、内存中的画布上重新绘制所有图形，完全绕过了在屏幕上对可见画布进行的二值化后处理。
*   **解决方案:** 将二值化逻辑直接集成到 `exportCanvas` 函数中。这样可以确保在生成最终的导出图片之前，对临时画布上的像素进行处理。同时，重新引入UI开关，使其专门控制*导出时*是否应用二值化。

**Implementation Details:**
*   **文件:**
    *   [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
    *   [`hotel-dashboard/components/drawing-board/Toolbar.tsx`](hotel-dashboard/components/drawing-board/Toolbar.tsx)
*   **撤销与重构:**
    1.  **撤销 `Canvas.tsx` 的更改:** 移除了 `Canvas.tsx` 中的 `binarizationEnabled` prop 和 `applyBinarization` 函数，使其恢复到只负责绘图的单一职责。
    2.  **状态管理 (`DrawingBoard.tsx`):**
        *   重新引入了 `const [binarizationEnabled, setBinarizationEnabled] = useState(true);` 状态，默认为 `true` 以满足用户需求。
        *   将状态和设置器传递给 `Toolbar`。
    3.  **导出逻辑 (`DrawingBoard.tsx`):**
        *   在 `exportCanvas` 函数内部，创建了一个 `applyBinarizationToContext` 辅助函数。
        *   在所有图形都绘制到临时画布上之后，增加了一个条件块：`if (binarizationEnabled) { applyBinarizationToContext(ctx); }`。
    4.  **UI 开关 (`Toolbar.tsx`):**
        *   重新引入了 `Switch` 组件，标签更新为“导出时二值化”，以明确其功能范围。
---
### Decision (Bug Fix)
[2025-06-17 12:12:00] - [Bug Fix: 通过处理设备像素比(DPR)彻底解决Canvas绘图模糊问题]

**Rationale:**
*   **问题:** 即便在禁用了图像平滑并对坐标取整后，绘制的图形（圆形和MTF图案）在高DPI屏幕上仍然存在灰色和模糊的边缘。
*   **根本原因:** 浏览器在高DPI（高像素密度）屏幕上会拉伸Canvas的位图，导致模糊。根本的解决方法是让Canvas的后台缓冲区拥有与设备物理像素匹配的分辨率。
*   **修复目标:** 在所有屏幕（特别是高DPI屏幕）上实现像素级的清晰锐利渲染。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **核心更改 (DPR Scaling):**
    1.  **主画布缩放 (在 `useEffect` 中):**
        *   获取 `const dpr = window.devicePixelRatio || 1;`。
        *   设置画布的物理（内存）尺寸：`canvas.width = width * dpr;` 和 `canvas.height = height * dpr;`。
        *   使用CSS设置画布的显示（逻辑）尺寸：`canvas.style.width = \`\${width}px\`;` 和 `canvas.style.height = \`\${height}px\`;`。
        *   缩放整个2D绘图上下文：`ctx.scale(dpr, dpr);`。这样，所有后续的绘图命令（如 `fillRect`, `arc`）都将使用逻辑坐标，但会被渲染到更高分辨率的缓冲区上。
    2.  **临时画布缩放 (在 `drawMtfPattern` 中):**
        *   对用于预渲染MTF图案的临时画布也应用了相同的DPR缩放逻辑，以确保图案本身是高分辨率的，避免将低分辨率图像绘制到高分辨率主画布上。
    3.  **圆形绘制调整:**
        *   由于整个上下文已被缩放，之前为解决亚像素问题而添加的 `Math.round()` 已不再必要，因此被移除，以避免潜在的精度损失。
        *   保留了 `ctx.imageSmoothingEnabled = false;` 作为确保最锐利边缘的最佳实践。
---
---
### Decision (Bug Fix)
[2025-06-13 18:05:00] - [Bug Fix: 修复MTF图案渲染超出边界的问题]

**Rationale:**
*   **问题:** 当前绘制的 MTF 图案的线条可能会超出其指定的尺寸边界（例如，一个 200x200 的图案可能会画到 200x200 区域之外）。
*   **修复目标:** 使用 Canvas 的剪切（clipping）功能来严格约束绘制区域，确保所有线条都在指定的边界框内。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    1.  在 `drawMtfPattern` 函数的开头，调用 `ctx.save()` 保存当前画布状态。
    2.  使用 `ctx.beginPath()` 和 `ctx.rect(x, y, size, size)` 创建一个与图案边界完全匹配的矩形路径。
    3.  调用 `ctx.clip()` 将后续的所有绘制操作限制在该矩形区域内。
    4.  在函数的所有绘制操作完成后，调用 `ctx.restore()` 来移除剪切区域，恢复画布的原始状态。
---
### Decision (Bug Fix)
[2025-06-13 18:02:00] - [Bug Fix: 修复MTF图案渲染循环停止条件以完全填充象限]

**Rationale:**
*   **问题:** 当前 MTF 图案的渲染不正确，问题出在 `for` 循环的停止条件上，导致线条没有完全填满各自的象限，与最新的 Python 参考代码不符。
*   **修复目标:** 严格按照最新的 Python 参考代码更新 `drawMtfPattern` 函数中所有四个象限的 `for` 循环的停止条件，并移除多余的中心线绘制逻辑。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    1.  **移除中心十字线:** 删除了在 `drawMtfPattern` 函数中单独绘制中心十字线的 `fillRect` 调用。
    2.  **更新循环停止条件:**
        *   左上角和右上角的循环条件从 `> rX` 或 `> rY` 更新为 `> -lineWidth`。
        *   左下角和右下角的循环条件从 `< rY + rSize` 或 `< rX + rSize` 更新为 `< rY + rSize + lineWidth` 或 `< rX + rSize + lineWidth`。
    3.  这些更改确保了线条能够一直绘制到或稍微超出图案的边界，从而完全填充象限并自然形成中心十字，与 Python 参考代码的行为保持一致。
---
### Decision (Bug Fix)
[2025-06-13 17:56:00] - [Bug Fix: 修复MTF图案渲染逻辑以匹配Python参考]

**Rationale:**
*   **问题:** 当前绘制的 MTF 图案与用户提供的标准 Python 参考代码和图像不完全一致。具体差异在于中心十字线的形成方式以及四个象限中线条的精确绘制范围。
*   **修复目标:** 严格按照 Python 参考代码重写 `drawMtfPattern` 函数，以确保渲染结果的像素级一致性。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    1.  **移除显式中心十字线:** 删除了单独绘制贯穿整个图案的水平和垂直中心线的代码。
    2.  **遵循Python逻辑:** 修改了所有四个象限的绘制逻辑，使其线条从图案的边缘一直绘制到中心线，从而自然地形成中心十字，这与Python参考代码的行为一致。
    3.  **修正循环和坐标:** 仔细检查并修正了所有四个象限的 `for` 循环的起始值、结束条件和步长，以及 `fillRect` 的坐标和尺寸参数，使其与Python的 `range()` 和 `draw.rectangle()` 逻辑精确对应。特别是修正了左下角图案的起始 `y` 坐标。
    4.  **确保整数坐标:** 在最终的 `fillRect` 调用中继续使用 `Math.round()`，以避免亚像素渲染问题。
### Decision (Bug Fix)
[2025-06-13 17:48:00] - [Bug Fix: 修复MTF图案渲染中的亚像素模糊问题]

**Rationale:**
*   **问题:** 即使禁用了 `imageSmoothingEnabled`，绘制的MTF图案的竖线边缘仍然有灰色的模糊像素。
*   **根本原因:** 传递给 `ctx.fillRect()` 的坐标和尺寸是浮点数，导致了亚像素渲染，从而产生模糊效果。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    *   在 `drawMtfPattern` 函数中，对所有传递给 `ctx.fillRect()` 的参数（x, y, width, height）都使用了 `Math.round()` 进行处理。
    *   这确保了所有的绘制操作都与像素网格对齐，从而消除了亚像素渲染导致的模糊，生成了清晰的线条。
### Decision (Bug Fix)
[2025-06-13 17:40:00] - [Bug Fix: 修复MTF图案生成中的定位与抗锯齿问题]

**Rationale:**
*   **定位错误:** 生成的MTF图案中，左下角区域的第一条黑线距离中心的水平线有3个像素的间距，而预期的间距应该是2像素。
*   **边缘模糊:** 放大查看MTF图案时，黑白线条的边缘存在灰色的模糊像素，这是由于抗锯齿（image smoothing）导致的。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    *   **定位修复:** 在 `drawMtfPattern` 函数中，将绘制左下角水平线的 `for` 循环的起始值从 `center_y + lineWidth + 2` 修正为 `center_y + lineWidth / 2 + 2`。这确保了第一条黑线的上边缘与中心水平线的下边缘之间有精确的2像素间距。
    *   **抗锯齿禁用:** 在 `drawMtfPattern` 函数的开头添加了 `ctx.imageSmoothingEnabled = false;`，以禁用抗锯齿，从而生成像素分明、边缘清晰的线条。

---
### Decision (Bug Fix)
[2025-06-13 17:34:00] - [Bug Fix: "画图板"导出的图片中MTF图案显示不正确]

**Rationale:**
*   **问题:** 当画布上绘制了新的“MTF 图案”后，导出的图片仍然显示为旧的纯色方块，而不是正确的 MTF 图案。
*   **根本原因:** 在 `hotel-dashboard/components/drawing-board/DrawingBoard.tsx` 的 `exportCanvas` 函数中，用于在临时画布上重新绘制图形的逻辑没有更新。它仍然在使用旧的 `ctx.rect()` 来绘制方形，而不是调用与屏幕显示逻辑一致的 `drawMtfPattern` 函数。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
*   **更改:**
    *   将 `Canvas.tsx` 中的 `drawMtfPattern` 函数的实现复制到了 `exportCanvas` 函数的本地作用域中。
    *   修改了 `exportCanvas` 中遍历 `shapes` 的循环，当 `shape.type` 为 `'square'` 时，调用这个本地的 `drawMtfPattern` 函数来绘制图形，而不是原来的 `ctx.rect()`。
    *   这确保了导出时使用的绘图逻辑与屏幕上显示的绘图逻辑完全一致。
---
### Decision (Bug Fix)
[2025-06-13 17:18:44] - [Bug Fix: "画图板"导出图片背景色不正确]

**Rationale:**
*   **问题:** 当用户使用“替换颜色”功能更改了画布的背景色后，导出的图片仍然是白色背景，没有反映出新的背景颜色。
*   **根本原因:** 在 `hotel-dashboard/components/drawing-board/DrawingBoard.tsx` 的 `exportCanvas` 函数中，用于填充临时画布背景的颜色被硬编码为 `'#FFFFFF'`，而不是使用当前的 `backgroundColor` 状态。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
*   **更改:**
    *   在 `exportCanvas` 函数中，将 `ctx.fillStyle = '#FFFFFF';` 修改为 `ctx.fillStyle = backgroundColor;`，以确保导出时使用正确的背景颜色。
---
### Decision (Bug Fix)
[2025-06-13 16:24:00] - [Bug Fix: "画图板"功能运行时错误修复]

**Rationale:**
*   **运行时错误:** 在 `hotel-dashboard/components/drawing-board/DrawingBoard.tsx` 中，代码仍然尝试向 `Toolbar` 和 `Canvas` 组件传递 `backgroundColor` 和 `setBackgroundColor` props，但其对应的 `useState` 已在之前的代码清理中被移除，导致 `ReferenceError: backgroundColor is not defined`。
*   **代码一致性:** 确保组件的 props 与其父组件的状态保持一致。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
*   **更改:**
    *   从传递给 `Toolbar` 组件的 props 中移除了 `backgroundColor={backgroundColor}` 和 `setBackgroundColor={setBackgroundColor}`。
    *   从传递给 `Canvas` 组件的 props 中移除了 `backgroundColor={backgroundColor}`。
---
### Decision (Code Simplification)
[2025-06-13 16:17:00] - [Simplification: "画图板"功能简化]

**Rationale:**
*   **用户需求:** 根据用户请求，对“画图板”功能进行最终简化，移除背景颜色选择功能，以简化UI和状态管理。
*   **代码简化:** 移除不再需要的状态和 props，可以使代码更清晰、更易于维护。

**Implementation Details:**
*   **移除背景颜色选择器:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Toolbar.tsx`](hotel-dashboard/components/drawing-board/Toolbar.tsx)
    *   **更改:** 完全移除了“画布背景”颜色选择器 input 及其关联的 `Label`。同时，从 `ToolbarProps` 接口和组件的 props 解构中删除了 `backgroundColor` 和 `setBackgroundColor`。
*   **移除背景颜色状态:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
    *   **更改:** 移除了 `backgroundColor` 的 `useState`。在 `exportCanvas` 函数中，背景色被硬编码为 `'#FFFFFF'`。传递给 `Toolbar` 和 `Canvas` 的相关 props 也被移除。
*   **固定画布背景色:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
    *   **更改:**
        1.  从 `CanvasProps` 接口和组件的 props 解构中移除了 `backgroundColor`。
        2.  在 `useEffect` hook 中，画布的 `fillStyle` 被硬编码为 `'#FFFFFF'`。
        3.  `drawGrid` 函数不再接收 `backgroundColor` 参数，其网格线颜色 `strokeStyle` 被固定为 `'rgba(0, 0, 0, 0.5)'`，不再需要动态计算。
---
### Decision (Feature Enhancement)
[2025-06-13 16:06:00] - [Feature Enhancement: "画图板"高级功能增强]

**Rationale:**
*   **用户需求:** 增强“画图板”功能，包括全局颜色替换、动态网格线颜色和单元格尺寸显示，以提高其专业性和可用性。
*   **像素级操作:** 为了实现全局颜色替换（包括背景），需要从状态驱动的绘图转向直接的像素操作。
*   **UI/UX 优化:** 动态网格线和单元格尺寸显示为用户提供了更好的视觉反馈和精确性。

**Implementation Details:**
*   **全局颜色替换:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
    *   **更改:** `replaceColor` 函数被重写。现在它使用 `canvas.getContext('2d').getImageData()` 读取整个画布的像素。通过遍历像素数据，将匹配的旧颜色（RGB）替换为新颜色，然后使用 `putImageData()` 将结果写回画布。同时保留了对 `shapes` 状态的更新，以确保后续重绘的持久性。
*   **动态网格线颜色:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
    *   **更改:**
        1.  添加了 `backgroundColor` 状态，并将其从 `DrawingBoard` 传递到 `Canvas` 和 `Toolbar`。
        2.  `drawGrid` 函数现在包含一个 `isColorDark` 的辅助函数，用于计算背景色的亮度。
        3.  根据背景色是深色还是浅色，网格线的 `strokeStyle` 被动态设置为半透明的白色或黑色。
        4.  画布的背景填充现在也使用 `backgroundColor` 状态。
*   **显示单元格尺寸:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Toolbar.tsx`](hotel-dashboard/components/drawing-board/Toolbar.tsx)
    *   **更改:**
        1.  `Toolbar` 现在从 `DrawingBoard`接收 `canvasSize` prop。
        2.  使用 `React.useEffect` hook 监听 `canvasSize` 和 `grid` 的变化。
        3.  当这些值变化时，计算出每个单元格的精确宽度和高度。
        4.  结果以 `宽: xxx.xx px, 高: yyy.yy px` 的格式显示在工具栏中。

---
### Decision (Bug Fix)
[2025-06-13 15:22:00] - [Bug Fix: "画图板"功能修复]

**Rationale:**
*   **运行时错误:** `Canvas.tsx` 组件在处理画布交互时，错误地引用了未定义的 `canvasRef`，导致 `ReferenceError`。
*   **UI 布局问题:** `Toolbar` 组件在页面上显示不正确，过于靠右，原因是其父容器在页面布局中被不必要地限制了宽度并居中显示。

**Implementation Details:**
*   **`ReferenceError` 修复:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
    *   **更改:** 在 `handleCanvasInteraction` 函数中，将对 `canvasRef.current` 的引用更正为 `internalCanvasRef.current`，与组件内部定义的 ref 保持一致。
*   **工具栏布局修复:**
    *   **文件:** [`hotel-dashboard/app/(dashboard)/drawing-board/page.tsx`](hotel-dashboard/app/(dashboard)/drawing-board/page.tsx)
    *   **更改:** 移除了包裹 `<DrawingBoard />` 组件的多余 `div` 元素 (`<div class="w-[90%] h-[90%] mx-auto my-auto">`)。这使得 `DrawingBoard` 组件可以扩展到其父容器 (`<div class="flex-grow w-full h-full">`) 的全部宽度，从而解决了工具栏的对齐问题。
---
### Decision (Architecture)
[2025-06-13 14:53:00] - [Architecture: "画图"功能实现]

**Rationale:**
*   **用户需求:** 实现一个全新的“画图”功能，允许用户创建自定义尺寸的画板，绘制网格，并在网格单元中放置和对齐图形，同时支持颜色操作。
*   **模块化设计:** 将功能拆分为独立的React组件，以提高可维护性和代码复用性。`DrawingBoard.tsx` 作为协调器，`Canvas.tsx` 负责渲染，`Toolbar.tsx` 提供用户交互控件。
*   **技术选型:** 使用标准的HTML5 Canvas API进行2D绘图，因为它性能良好且浏览器支持广泛。利用React的状态管理（`useState`）来驱动画布的重新渲染。

**Implementation Details:**
*   **新页面:** 在 `hotel-dashboard/app/(dashboard)/drawing-board/page.tsx` 创建新页面。
*   **组件结构:**
    *   `hotel-dashboard/components/drawing-board/DrawingBoard.tsx`: 包含所有状态管理和核心逻辑，将 props 传递给子组件。
    *   `hotel-dashboard/components/drawing-board/Canvas.tsx`: 使用 `useRef` 访问 canvas 元素，并在 `useEffect` hook 中根据 props (尺寸, 网格, 图形) 的变化来重绘。
    *   `hotel-dashboard/components/drawing-board/Toolbar.tsx`: 包含用于修改画布尺寸、网格、图形颜色、直径等设置的输入控件。
*   **类型定义:** 在 `hotel-dashboard/types/drawing-board.ts` 中定义了共享的类型，如 `Shape`, `Alignment`, `ShapeType`。
*   **数据流:**
    1.  用户在 `Toolbar` 中修改设置。
    2.  `Toolbar` 通过回调函数更新 `DrawingBoard` 中的状态。
    3.  `DrawingBoard` 将更新后的状态作为 props 传递给 `Canvas`。
    4.  `Canvas` 的 `useEffect` hook 检测到 props 变化，触发画布重绘。
    5.  用户点击 `Canvas`，触发 `addShape` 回调，更新 `DrawingBoard` 中的 `shapes` 状态，导致重绘。
*   **集成:** 在 `hotel-dashboard/dashboard.tsx` 的侧边栏中添加了指向新页面的链接。
---
### Decision (Architecture)
[2025-06-12 14:06:00] - [Architecture: C# Wrapper for DLL Invocation]

**Rationale:**
*   **Dependency Isolation:** The core logic is encapsulated within a provided DLL. Creating a C# console application wrapper allows Node.js to interact with it through a clean command-line interface, without requiring native Node.js addons or complex inter-process communication (IPC) setups. This isolates the .NET dependencies from the Node.js environment.
*   **Simplified Interoperability:** Invoking a console application from Node.js (via `child_process`) is a well-understood and straightforward pattern for interoperability between different technology stacks. It avoids the complexities and potential brittleness of direct bindings (e.g., using `edge-js`).
*   **Maintainability:** The C# wrapper can be developed, tested, and maintained independently of the Next.js application.

**Implementation Details:**
*   **C# Project:** A new C# console application project (`WrapperApp`) is created in the `csharp-wrapper/` directory.
*   **Interface:** The wrapper accepts command-line arguments (`sn`, `featureCode`).
*   **Node.js API:** A new API route (`/api/feature/update`) in the Next.js application is responsible for receiving requests from the frontend and executing the C# wrapper executable.
*   **Data Flow:** Frontend Form -> Next.js API Route -> C# Wrapper -> DLL.
---
### Decision (Debug)
[2025-06-12 13:11:00] - [Bug Fix Strategy: 修复预览API中ZIP处理的TypeError]

**Rationale:**
在 `app/api/surface-data/preview/route.ts` 的 `handleZipDecompression` 函数中，使用 `new StreamZip.async({ buffer: fileBuffer })` 直接从内存缓冲区初始化 `node-stream-zip` 存在类型不兼容问题，并可能在某些环境下导致 `TypeError: The "path" argument must be of type string...` 的运行时错误。正确的做法是为库提供一个文件路径。

**Details:**
*   **Affected File:** [`hotel-dashboard/app/api/surface-data/preview/route.ts`](hotel-dashboard/app/api/surface-data/preview/route.ts)
*   **Fix Implemented:**
    1.  **临时文件写入:** 将从FTP下载的 `fileBuffer` 写入到一个在临时目录 (`os.tmpdir()`) 中生成的唯一命名的ZIP文件（使用 `uuid`）。
    2.  **路径初始化:** 使用这个临时文件的路径来初始化 `node-stream-zip`，即 `new StreamZip.async({ file: tmpZipPath })`。
    3.  **依赖添加:** 在 `hotel-dashboard` 项目中添加 `uuid` 及其类型定义 `@types/uuid`。
    4.  **清理临时文件:** 在 `finally` 块中添加了健壮的清理逻辑，确保无论解压成功与否，都会尝试删除临时文件 (`fs.unlink`)，避免临时文件堆积。
*   **Expected Impact:** 解决了 `TypeError` 运行时错误，使ZIP文件预览功能更加稳定和可靠。
---
### Decision (Performance Optimization)
[2025-06-12 12:16:00] - 面形数据预览API性能优化策略

**Rationale:**
*   **响应时间:** 原始的预览API在处理大型点云文件（尤其是压缩文件）时，可能会因完整解析和渲染所有数据点而导致响应时间过长，甚至超时。
*   **资源消耗:** 在服务器上处理数百万个点会消耗大量CPU和内存，影响服务器的整体性能和可扩展性。
*   **I/O瓶颈:** 对于ZIP文件，将缓冲区写入临时文件再进行解压会引入不必要的磁盘I/O，成为性能瓶颈。

**Implementation Details:**
*   **数据采样:**
   *   **Affected File:** [`hotel-dashboard/utils/pointCloudUtils.ts`](hotel-dashboard/utils/pointCloudUtils.ts)
   *   **Change:** 修改 `parsePointCloudData` 函数，增加一个可选的 `options` 参数，允许指定 `maxPoints`。如果输入数据的点数超过 `maxPoints`，则进行均匀采样，只解析和返回一部分数据点。
   *   **API Integration:** 在 `/api/surface-data/preview` 中调用 `parsePointCloudData` 时，设置 `maxPoints` 为 `150,000`，确保用于预览的点数在一个合理的范围内。
*   **内存中解压:**
   *   **Affected File:** [`hotel-dashboard/app/api/surface-data/preview/route.ts`](hotel-dashboard/app/api/surface-data/preview/route.ts)
   *   **Change:** 重构了ZIP文件处理逻辑。不再将FTP下载的 `Buffer` 写入临时文件，而是直接使用 `node-stream-zip` 从内存 `Buffer` 中创建实例并解压。
   *   **Impact:** 消除了磁盘I/O，减少了延迟，并简化了代码（无需处理临时文件的创建和删除）。
*   **代码结构优化:**
   *   **Affected File:** [`hotel-dashboard/app/api/surface-data/preview/route.ts`](hotel-dashboard/app/api/surface-data/preview/route.ts)
   *   **Change:** 将API路由处理函数分解为更小的、单一职责的辅助函数（如 `getPointCloudData`, `generatePreviewImage`, `handleZipDecompression`），提高了代码的可读性和可维护性。

---
### Decision (UI/UX Enhancement)
[2025-06-12 12:16:00] - 为点云预览引入交互式缩放和平移功能

**Rationale:**
*   **用户体验:** 静态的预览图对于观察点云细节（如局部异常或特征）作用有限。提供缩放和平移功能可以极大地提升用户体验和预览的实用性。
*   **技术选型:** `react-zoom-pan-pinch` 是一个轻量级、功能强大且易于集成的库，专门用于为React组件添加平移和缩放功能，非常适合此场景。

**Implementation Details:**
*   **Affected File:** [`hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx`](hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx)
*   **Dependency:** 添加了 `react-zoom-pan-pinch` 库。
*   **Integration:**
   *   将原有的 `<img>` 标签包裹在 `TransformWrapper` 和 `TransformComponent` 组件中。
   *   `TransformWrapper` 负责处理所有的鼠标和触摸事件，以实现平移和缩放。
   *   添加了一个浮动的控件面板，包含“放大”、“缩小”和“重置视图”的按钮，这些按钮通过 `TransformWrapper` 提供的 `utils` 函数来控制视图。
   *   使用 `useRef` 来获取 `TransformWrapper` 的实例，以便在模态框关闭或重新加载数据时可以调用 `resetTransform()` 方法，确保UI状态的一致性。
*   **TypeScript支持:**
   *   由于 `react-zoom-pan-pinch` 可能缺少官方的类型声明，创建了自定义的声明文件 (`hotel-dashboard/types/react-zoom-pan-pinch.d.ts`) 来提供必要的类型定义，解决了TypeScript编译错误。

---
### Decision (Architecture Design)
[2025-06-12 11:35:41] - 面形数据预览功能技术架构设计

**Rationale:**
*   **用户体验提升:** 在下载完整数据前提供快速的点云数据俯视图预览。
*   **性能优化:** 后端处理解压、解析和渲染，前端轻量化展示。
*   **集成与复用:** 利用现有FTP客户端和UI组件。
*   **算法移植:** 将C#中的点云可视化逻辑移植到Web环境。

**Implementation Details:**
*   **组件划分:**
    *   **前端:** [`PointCloudPreviewModal.tsx`](hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx) (新增), [`ResultsDisplay.tsx`](hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx) (修改), [`pointCloudUtils.ts`](hotel-dashboard/lib/pointCloudUtils.ts) (新增)。
    *   **API层:** 新增 `/api/surface-data/preview` 端点 (Next.js API Route)。
    *   **数据处理层 (后端):** 包含解压缩、XYZ点云解析、俯视图渲染算法（移植自[`Form1.cs`](Form1.cs)）。
*   **数据流:** 用户点击预览 -> 前端请求API -> 后端下载、解压、解析、渲染 -> 后端返回图像数据 -> 前端Canvas显示。
*   **API设计 (`/api/surface-data/preview`):**
    *   **请求 (POST):** `{ "filePath": "string" }`
    *   **响应 (成功):** `{ "imageData": "base64_string" | "pixel_array", "imageType": "string", "width": number, "height": number, "originalBounds": object }`
    *   **响应 (失败):** `{ "error": "string" }`
*   **关键技术决策:**
    *   **后端渲染俯视图:** 避免大文件传输，利用服务器计算能力。
    *   **解压缩:** 根据文件类型选择Node.js `zlib`或`unzipper`/`pako`。
    *   **渲染算法移植:** 将[`Form1.cs`](Form1.cs)中的2D投影和画布映射逻辑用TypeScript/JavaScript实现，可使用`node-canvas`或手动操作像素数组。
*   **Memory Bank 更新:**
    *   [`productContext.md`](memory-bank/productContext.md): 添加新功能描述。
    *   [`decisionLog.md`](memory-bank/decisionLog.md): 记录此架构决策。
    *   相关开发和用户文档将进行更新。
## Decision

*   [2025-06-10 15:11:26] - 确定将 `web_tool` 日志分析功能集成到 `hotel-dashboard` 的系统架构。采用基于 React 组件（`LogFileUpload`, `LogDisplayArea`, `LogChartView`）和 Web Worker (`logParser.worker.ts`) 的设计。

**Rationale:**
*   组件化设计易于管理和维护。
*   Web Worker 用于日志解析，避免阻塞 UI 主线程，提升用户体验。
*   利用 `hotel-dashboard` 现有的 Next.js 和 shadcn/ui 基础。

**Implementation Details:**
*   **目录结构:**
    *   `components/log-analysis/` 存放新的 React 组件。
    *   `app/(dashboard)/log-analysis/page.tsx` 作为日志分析主页面。
    *   `workers/logParser.worker.ts` (或 `public/workers/`) 存放 Web Worker。
*   **数据流:** 用户上传 -> `LogFileUpload` -> `logParser.worker` -> `LogDisplayArea` -> `LogChartView`。
*   **集成点:** 修改 `components/sidebar.tsx` 添加导航链接；`app/(dashboard)/layout.tsx` 承载整体布局。
*   **状态管理:** 初步考虑使用 React Context API，可根据后续开发调整。
---
### Decision (Debug)
[2025-06-11 17:39:00] - [Bug Fix Strategy: 深度修复Recharts图表尺寸问题]

**Rationale:**
尽管之前设置了minWidth/minHeight，Recharts图表尺寸警告仍然存在。根据Memory Bank中的历史调试信息，问题的根本原因是Y轴domain计算导致的。当domain为`[0, 0]`时，Recharts无法正确计算图表的内部尺寸，即使外部容器有尺寸。

**Details:**
*   **Root Cause Analysis:** 
    *   当没有数据时，`glueDomain`和`collimationDomain`都被设置为`[0, 0]`
    *   `timeDomain`也被设置为`[0, 0]`
    *   这些无效的domain导致Recharts内部计算出0尺寸的图表区域
*   **Comprehensive Fix (`LogChartView.tsx`):**
    1.  **Domain默认值修复:**
        *   `glueDomain`: 从`[0, 0]`改为`[0, 1000]` (胶厚的合理默认范围)
        *   `collimationDomain`: 从`[0, 0]`改为`[0, 0.1]` (准直差的合理默认范围)  
        *   `timeDomain`: 从`[0, 0]`改为`[Date.now() - 3600000, Date.now()]` (默认1小时时间范围)
    2.  **Y轴类型明确化:**
        *   为两个Y轴都添加了`type="number"`属性，确保Recharts正确处理数值域
*   **Expected Impact:**
    *   解决"width(0) and height(0) of chart should be greater than 0"警告
    *   确保即使在没有数据时图表也能正确渲染占位符
    *   提高图表渲染的稳定性和可预测性

---
---
### Decision (Debug)
[2025-06-11 17:53:00] - [Bug Fix Strategy: 修复图表显示和宽度问题]

**Rationale:**
用户反馈发现了真正的问题：1) 点击"显示"不会显示图表，只有点击"导出图片"才显示，2) 图表宽度没有填满容器。这表明问题不在Recharts尺寸警告，而在于Card的显示逻辑和图表的响应式布局。

**Details:**
*   **Root Cause Analysis:**
    *   Card的className逻辑问题：使用了错误的显示/隐藏逻辑
    *   图表宽度问题：使用固定宽度800px而不是响应式宽度
*   **Fixes Implemented:**
    1.  **Card显示逻辑修复:**
        *   恢复正确的className: `selectedBlockIds.includes(chunk.block_id) ? 'block' : 'hidden'`
        *   移除了错误的内联style显示逻辑
    2.  **图表宽度修复:**
        *   恢复ResponsiveContainer以实现100%宽度填充
        *   移除固定的width={800}，改为响应式布局
        *   保持height={400}的容器高度约束
*   **Expected Result:**
    *   选中数据块时图表立即显示
    *   图表宽度完全填满容器
    *   保持所有之前修复的有效性（domain默认值、编码检测等）
### Decision (Code)
[2025-06-10 15:59:06] - 日志分析图表 (`LogChartView.tsx`) 数据处理与可视化策略

**Rationale:**
确保图表能够准确、清晰地展示来自一个或多个日志数据块的胶厚、准直数据及相关事件，同时保证时间轴的正确性和可读性。

**Details:**
*   **时间戳统一处理:**
   *   所有从日志解析出的字符串时间戳 (e.g., "YYYY-MM-DD HH:MM:SS,mmm") 被转换为自 Epoch 以来的毫秒级数字时间戳 (`new Date(timestampString).getTime()`)。这适用于 `recharts` 的数值型X轴，便于排序、定义域计算和格式化。
*   **数据准备与转换:**
   *   利用 `logParser.worker.ts` 中的 `formatBlockDataForFrontend` 函数对每个 `ProcessedBlock` 进行初步格式化，生成包含 `data_points` (时间戳、胶厚、准直) 和 `valve_open_events` 的结构。
   *   来自多个选定 `ProcessedBlock` 的 `data_points` 被合并，并为每个数据系列（如 `glue_thickness_block_X`, `collimation_diff_block_X`）创建唯一的 `dataKey`，以便在同一图表上区分显示。
   *   所有合并后的数据点 (`allChartDataPoints`) 最终按时间戳排序。
*   **图表库与组件:**
   *   使用 `recharts` 库。采用 `<ComposedChart>` 作为基础，主要使用 `<Line>` 组件展示胶厚和准直数据。`<ComposedChart>` 提供未来扩展其他图表类型（如柱状图）的灵活性。
   *   X轴 (`<XAxis>`) 设置为 `type="number"`，使用转换后的数字时间戳，并提供 `tickFormatter` 将数字时间戳转换为可读的时间字符串。
   *   Y轴 (`<YAxis>`) 分别对应胶厚和准直数据，使用不同的 `yAxisId`。
*   **事件可视化:**
   *   `valve_open_events`（放气阀打开事件）通过 `<ReferenceLine>` 组件在图表上标记其发生的时间点，并附带标签。
*   **多数据块处理:**
   *   图表设计为可以同时显示来自多个数据块的数据。每个数据块的系列使用不同的颜色（通过 `COLORS` 数组循环获取）和图例名称加以区分。
   *   `connectNulls={true}` 用于 `<Line>` 组件，以处理数据点可能不连续的情况。

---
### Decision (Debug)
[2025-06-10 16:06:26] - [Bug Fix Strategy: SSR `self is not defined` Error in Web Worker]

**Rationale:**
The `ReferenceError: self is not defined` occurs because Web Worker code (`self.onmessage`) is executed in a Node.js environment (SSR) where `self` is not a defined global. The fix ensures Worker instantiation and its lifecycle management occur only on the client-side.

**Details:**
*   **Affected Component:** [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
*   **Fix Implemented:**
    *   The Web Worker instance (`logParser.worker.ts`) is now created and managed within a `useEffect` hook in `LogFileUpload.tsx`.
    *   A condition `typeof window !== 'undefined'` is used inside `useEffect` to ensure the Worker is only instantiated in the browser environment.
    *   The Worker instance is stored in a `useRef` hook (`workerRef`) to persist it across re-renders and allow access in event handlers.
    *   The `useEffect` hook also includes a cleanup function to terminate the Worker when the component unmounts.
    *   The `handleUpload` function now uses `workerRef.current` to post messages to the Worker.

---
### Decision (Debug)
[2025-06-10 16:12:30] - [Bug Fix Strategy: Log Parser Worker Fails to Extract Data Blocks Due to Encoding Issues]

**Rationale:**
The Web Worker ([`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts)) was unable to parse data blocks from uploaded log files because the file content was being read with an incorrect encoding (defaulting to UTF-8 via `selectedFile.text()`). If the log files are encoded in GBK or other non-UTF-8 formats, the string passed to the worker would be garbled, causing regex matches to fail. The previous project (`web_tool`) successfully handled this by using `jschardet` for encoding detection and `TextDecoder` for decoding.

**Details:**
*   **Affected Component:** [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
*   **Fix Implemented:**
    *   Installed the `jschardet` library in the `hotel-dashboard` project.
    *   Modified the `handleUpload` function in `LogFileUpload.tsx`:
        *   File is read as an `ArrayBuffer` using `selectedFile.arrayBuffer()`.
        *   `jschardet.detect()` is used on the `ArrayBuffer` (via a `Uint8Array`) to determine the file's encoding.
        *   A `TextDecoder` instance is created using the detected encoding (with fallbacks to 'gbk' and then 'utf-8' if detection is uncertain or the primary detected encoding fails, mirroring the logic from the `web_tool` project).
        *   The `ArrayBuffer` is decoded into a string using the `TextDecoder`.
        *   This correctly decoded string is then sent to the Web Worker for parsing.

---
### Decision (Debug)
[2025-06-10 16:21:00] - [Bug Fix Strategy: Web Worker重复实例化及`self is not defined`错误]

**Rationale:**
控制台反复输出 "[Worker] logParser.worker.ts script loaded..." 表明 Web Worker 被多次实例化。同时，出现 `self is not defined` 错误，指示 Worker 脚本在服务器端被执行。`jschardet.detect` 也存在 TypeScript 类型问题。

**Details:**
*   **Affected Files:**
    *   [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
    *   [`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts)
*   **Fixes Implemented:**
    1.  **Worker 重复实例化 (`LogFileUpload.tsx`):**
        *   `useEffect` Hook (用于 Worker 初始化和清理) 的依赖项数组从 `[onDataProcessed, onError]` 更改为 `[]`。这确保 Worker 仅在组件挂载时创建，在卸载时销毁。
        *   为确保 `onmessage` 和 `onerror` 事件处理器能调用最新的 `onDataProcessed` 和 `onError` prop 函数，这些 prop 通过 `useRef` 存储，并在 `useEffect` 中更新这些 ref。事件处理器则调用 ref 的 `.current` 版本。
        *   在 `useEffect` 的清理函数中，明确将 Worker 的 `onmessage` 和 `onerror` 设置为 `null`，然后再调用 `terminate()`。
    2.  **`self is not defined` 错误 (`logParser.worker.ts`):**
        *   在 `logParser.worker.ts` 文件顶部添加了一个检查 `if (typeof self === 'undefined' || typeof self.postMessage !== 'function' || (typeof window !== 'undefined' && self === window))`。
        *   如果条件为真（即不在有效的 Worker 环境中），则会打印警告，并且 Worker 的主要逻辑（包括 `self.onmessage` 的设置）被包裹在一个 `else` 块中，从而不会执行。所有顶层 `export` 语句保持在顶层，以便模块在非 Worker 环境中仍可导入（例如用于类型共享）。
    3.  **`jschardet.detect` 类型错误 (`LogFileUpload.tsx`):**
        *   对 `jschardet.detect(uInt8ArrayForDetection)` 的调用修改为 `jschardet.detect(uInt8ArrayForDetection as any)`，以抑制 TypeScript 关于 `Uint8Array` 与 `Buffer` 不兼容的错误。
        *   置信度阈值检查从 `jschardet.Constants.MINIMUM_THRESHOLD` 改为直接使用数值 `0.2`，因为 `Constants` 对象未按预期导出。

---
### Decision (Debug)
[2025-06-10 16:33:00] - [Bug Fix Strategy: `Uncaught ReferenceError: window is not defined` in `logParser.worker.ts`]

**Rationale:**
The error `Uncaught ReferenceError: window is not defined` at line 48 of [`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts) was caused by code (specifically `[...new Set(...)]`) that, while not directly calling `window`, might have an implicit dependency or behavior in some JavaScript engines within the Web Worker environment that leads to attempts to access `window`. Additionally, the existing Worker environment check was not sufficiently robust.

**Details:**
*   **Affected File:** [`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts)
*   **Fixes Implemented:**
    1.  **Timestamp Deduplication (Line 48 area):**
        *   Replaced the use of `[...new Set([...glueValues.map(item => item.timestamp), ...collimValues.map(item => item.timestamp)])]` with a manual approach using an object to create a set of unique timestamps:
            ```typescript
            const timestampSet: { [key: string]: boolean } = {};
            for (const item of glueValues) {
                if (item.timestamp) timestampSet[item.timestamp] = true;
            }
            for (const item of collimValues) {
                if (item.timestamp) timestampSet[item.timestamp] = true;
            }
            const allTimestamps = Object.keys(timestampSet);
            allTimestamps.sort();
            ```
        *   This avoids potential `window` access by `Set` or spread syntax in certain Worker contexts.
    2.  **Worker Environment Check (Line 72 area):**
        *   Strengthened the condition for executing worker-specific logic by checking against `DedicatedWorkerGlobalScope`:
            ```typescript
            if (
                typeof DedicatedWorkerGlobalScope === "undefined" ||
                !(self instanceof DedicatedWorkerGlobalScope) ||
                typeof self.postMessage !== 'function'
            ) {
            // ... console.warn and do not run worker logic
            } else {
            // ... run worker logic
            }
            ```
        *   This ensures that the core worker logic only runs if the script is truly executing within a dedicated worker's global scope.
## Rationale

*

## Implementation Details

*
---
### Decision (Debug)
[2025-06-10 17:17:00] - [Bug Fix Strategy: Timestamp Parsing in Chart View Produces NaN]

**Rationale:**
Console logs after user selected a data block showed that `new Date(dp.timestamp).getTime()` in `LogChartView.tsx` was resulting in `NaN` for the `time` property of chart data points. This occurred despite the timestamp strings (e.g., "2025-06-06 08:45:17,890") appearing generally parsable. The use of a comma as a millisecond separator, and a space as the date-time separator, while often handled by `new Date()`, is not as universally robust as the ISO 8601 standard (which uses a period for milliseconds and 'T' as the date-time separator). This inconsistency likely led to parsing failures.

**Details:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Fix Implemented:**
    1.  A new helper function `parseTimestamp(ts: string | null | undefined): number` was introduced in `LogChartView.tsx`.
    2.  This function first checks if the input timestamp string `ts` is null or undefined, returning `NaN` if so.
    3.  It then standardizes the timestamp string by:
        *   Replacing the comma (`,`) millisecond separator with a period (`.`).
        *   Replacing the space (" ") separator between date and time with a 'T'.
        (e.g., "2025-06-06 08:45:17,890" becomes "2025-06-06T08:45:17.890").
    4.  The standardized string is then passed to `new Date()`, and `.getTime()` is called on the result.
    5.  Added a `console.warn` within `parseTimestamp` if `getTime()` still results in `NaN` after standardization, to aid future debugging.
    6.  All internal uses of `new Date(dp.timestamp).getTime()` and `new Date(event.timestamp).getTime()` within `LogChartView.tsx` were replaced with calls to `parseTimestamp(dp.timestamp)` and `parseTimestamp(event.timestamp)` respectively.
    7.  Updated chart rendering logic (e.g., `tickFormatter`, `labelFormatter`, `ReferenceLine` rendering) to also handle potential `NaN` time values gracefully, preventing crashes and providing visual feedback (e.g., "Invalid Time").
    8.  Adjusted `timeDomain` calculation to be more robust against `NaN` values to prevent chart errors.
---
### Decision (Debug)
[2025-06-10 18:01:00] - [Bug Fix Strategy: Isolate Chart Rendering with Minimal Config]

**Rationale:**
After previous fixes (timestamp parsing, robust decoding), chart lines and points still failed to render with real data, and also with initial fake data attempts. Only `ReferenceLine` was visible. DOM inspection showed Y-axes were not rendering. Forcing a fixed Y-axis domain with fake data made Y-axes appear, but still no lines/points. The latest test with an extremely simplified chart configuration (single Y-axis with fixed domain, single Line component with basic props and highly visible dots) using fake data *successfully rendered both dots and lines*.

This indicates the core Recharts rendering mechanism is functional. The problem likely lies in:
1.  The interaction of more complex configurations (e.g., dual Y-axes, `ComposedChart` vs `LineChart`, specific `<Line type="...">` props, `Tooltip`, `Legend`).
2.  How `domain={['auto', 'auto']}` for Y-axes behaves with the actual data (potentially all nulls for a series, or values that Recharts struggles to automatically scale).
3.  The combination of the above with real data characteristics.

**Strategy Adopted:**
The successful rendering with a minimal setup using fake data is a key baseline. The next step is to revert to using *real data* but maintain a *simplified chart configuration* (initially, a single Y-axis with `domain="auto"` and a single Line component) to see if real data can be rendered in this minimal setup. If successful, configurations will be added back incrementally.

**Details:**
*   **Observation:** Minimal chart with fake data, fixed Y-axis domain, and prominent dots renders correctly.
*   **Hypothesis:** The issue is not with the fundamental SVG rendering or container sizes, but with specific Recharts configurations or their interaction with data properties when using `auto` domains or multiple series/axes.
*   **Next Step:** Modify `LogChartView.tsx` to use real data (`useTestData = false`), but keep the chart configuration minimal (e.g., single Y-axis with `domain="auto"`, one `<Line>`).
---
### Decision (Debug)
[2025-06-10 18:03:00] - [Bug Fix Strategy: Incremental Restoration of Chart Configuration]

**Rationale:**
Testing ريال data with a highly simplified chart configuration (single `LineChart`, single Y-axis with `domain="auto"`, single `<Line>` component) resulted in successful rendering of data points and lines. This confirms that the data processing, timestamp parsing, and basic Recharts rendering are functional. The issue of the chart appearing blank must therefore stem from the more complex configurations present in the original `LogChartView.tsx` code.

**Strategy Adopted:**
Incrementally reintroduce components and configurations that were present in the original, more complex chart implementation. After each addition or small group of additions, the chart will be tested with real data to pinpoint which specific part of the configuration causes the rendering to fail.

**Initial Restoration Steps:**
1.  Change chart type from `LineChart` back to `ComposedChart`.
2.  Restore the original `Tooltip` and `Legend` components and their configurations.
3.  Restore the `type="monotone"` prop to the `<Line>` component.

Subsequent steps will involve reintroducing the second Y-axis and the second `<Line>` component for collimation data, and finally the `ReferenceLine` for events, as these are the most complex parts.
---
### Decision (Debug)
[2025-06-10 18:16:00] - [Bug Fix Strategy: Test Dual Y-Axes with Fixed Domains Using Fake Data]

**Rationale:**
After successfully rendering a single data series (glue thickness) with `domain="auto"` on its Y-axis, reintroducing the second Y-axis and second data series (collimation difference), both also with `domain="auto"`, caused the chart to fail rendering correctly again (blank or only one series partially visible). This strongly suggests an issue with how Recharts calculates or handles `domain="auto"` when multiple Y-axes are present, especially if one data series might have characteristics (e.g., all nulls, very small range, or extreme values not properly handled by auto-scaling in a dual-axis context) that interfere with domain calculation for itself or the other axis.

**Strategy Adopted:**
To isolate whether the `domain="auto"` calculation is the culprit in a dual-axis setup, the next step is to:
1.  Revert to using the internally generated fake data (which is known to be well-structured for both series).
2.  Configure *both* Y-axes (`yAxisId="left"` for glue thickness and `yAxisId="right"` for collimation difference) with *fixed, explicit domains* that are known to encompass the range of the fake data.
3.  Render both `<Line>` components.

If both series render correctly under this setup, it will confirm that the issue lies with `domain="auto"` in a multi-axis scenario with the specific characteristics of the real (or even fake, if auto-domain fails there too) data. If it still fails, the problem is more fundamental to the dual-axis/dual-line rendering in Recharts.

**Details:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Modification:**
    *   Set `useTestData = true` to use `generateTestData()`.
    *   Set `domain={[900, 1200]}` (or similar appropriate fixed range based on fake data) for the left Y-axis.
    *   Set `domain={[0, 0.1]}` (or similar appropriate fixed range based on fake data) for the right Y-axis.
    *   Ensure both `<Line>` components are included in the render.
---
### Decision (Debug)
[2025-06-10 18:20:00] - [Bug Fix Strategy: Incremental Reintroduction from Minimal Working Chart]

**Rationale:**
The test with an extremely simplified chart configuration using hardcoded data was successful: a single line with data points rendered correctly. This confirms that the basic Recharts `LineChart`, `XAxis`, `YAxis`, and `Line` components can function within the project environment. The failure to render lines/points in previous, more complex configurations (even with valid fake data and fixed Y-axis domains) must be due to elements introduced beyond this minimal setup.

**Strategy Adopted:**
Starting from the known-good minimal hardcoded example, incrementally reintroduce the features and data processing steps that were part of the original, non-working `LogChartView` implementation. This will help pinpoint exactly which addition causes the rendering to fail.

**First Incremental Step:**
1.  Reintroduce the `LogChartViewProps` (including `dataChunks` and `useTestData`).
2.  Set `useTestData = true` to use the `generateTestData()` function.
3.  Restore the data processing pipeline: `generateTestData()` -> `formatBlockDataForFrontend()` -> creation of `allChartDataPoints` -> `finalChartData`.
4.  Maintain a `LineChart` with a single X-axis and a single Y-axis (no `yAxisId`, default).
5.  The single `<Line>` component will use the dynamically generated `dataKey` (e.g., `glue_thickness_test_block_1`) derived from the `finalChartData`.
6.  The Y-axis will use a fixed domain appropriate for the "glue" series of the fake data (e.g., `[900, 1200]`).
7.  Keep auxiliary components (`Tooltip`, `Legend`, `CartesianGrid`) minimal or as they were in the working hardcoded version.

This step aims to verify if the data processing pipeline and dynamic `dataKey` usage are compatible with the basic `LineChart` rendering.
---
### Decision (Debug)
[2025-06-11 08:56:00] - [Bug Fix Strategy: Test `ComposedChart` with Single Series]

**Rationale:**
The previous test confirmed that a `LineChart` rendering a single series (glue thickness) with dynamically processed fake data and a dynamic `dataKey` works correctly. The version that failed before this (showing axes but no lines/dots) used `ComposedChart` and attempted to render two series with two Y-axes, both with fixed domains. To isolate the cause of failure, the next step is to determine if `ComposedChart` itself behaves differently from `LineChart` even when rendering only a single series.

**Strategy Adopted:**
Modify the last working configuration (single series `LineChart` with fake data and fixed Y-axis domain) by changing only the chart container from `LineChart` to `ComposedChart`. All other aspects (single X-axis, single Y-axis, single `<Line>` component, data processing, `dataKey`) will remain the same.

**Details:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Modification:**
    *   Change `<LineChart ...>` to `<ComposedChart ...>`.
    *   Ensure all other configurations from the last successful test (single series, fake data, fixed Y-axis domain for that series, dynamic `dataKey`) are maintained.
    *   The `ComposedChart` will contain one `XAxis`, one `YAxis`, and one `Line` component.
---
### Decision (Debug)
[2025-06-11 09:01:00] - [Bug Fix Strategy: Test Dual Series in `ComposedChart` with Fixed Domains]

**Rationale:**
The previous test successfully rendered a single series (glue thickness) using `ComposedChart` with fake data, a dynamic `dataKey`, and a fixed Y-axis domain. This confirms that `ComposedChart` itself, along with `Tooltip`, `Legend`, and `type="monotone"` for the line, are not the primary cause of the rendering failure when multiple series are involved. The issue is now strongly suspected to be related to the introduction of a second Y-axis and a second `<Line>` component, particularly how their Y-axis domains are handled.

**Strategy Adopted:**
Maintain the `ComposedChart` setup from the last successful test. Reintroduce the second Y-axis (for collimation data) and the second `<Line>` component (for collimation data). Crucially, *both* Y-axes will be configured with fixed, explicit domains known to be appropriate for their respective series in the fake data. This will test if the dual-axis, dual-line structure itself is problematic even when `domain="auto"` is not a factor.

**Details:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Modification:**
    *   Continue using `useTestData = true`.
    *   The `ComposedChart` will contain one `XAxis`.
    *   Add back the second `YAxis` (`yAxisId="right"`, `orientation="right"`) with its `<RechartsLabel>`.
    *   Set a fixed `domain` for the left Y-axis (e.g., `[900, 1200]` for glue thickness from fake data).
    *   Set a fixed `domain` for the right Y-axis (e.g., `[0, 0.1]` for collimation diff from fake data).
    *   Add back the second `<Line>` component, associating it with `yAxisId="right"` and using the dynamic `dataKey` for collimation data (e.g., `collimation_diff_test_block_1`).
    *   Restore `Tooltip` formatter logic for both series.
---
### Decision (Debug)
[2025-06-11 09:04:00] - [Bug Identified: Y-Axis `domain="auto"` Calculation with Dual Axes]

**Rationale:**
Previous tests culminated in a successful rendering of a