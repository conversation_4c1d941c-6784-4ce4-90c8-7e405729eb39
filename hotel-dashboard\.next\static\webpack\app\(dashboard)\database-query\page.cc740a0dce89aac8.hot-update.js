"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/database-query/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/database-query/page.tsx":
/*!*************************************************!*\
  !*** ./app/(dashboard)/database-query/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseQueryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DatabaseQueryPage() {\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SELECT TOP 10 * FROM dbo.LOG_DATA;\");\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tables, setTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingTables, setIsLoadingTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 新增状态：结构化查询相关\n    const [selectedTable, setSelectedTable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [columns, setColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingColumns, setIsLoadingColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // CSV导出函数\n    const exportToCSV = (tableName, data)=>{\n        if (data.length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"No Data\",\n                description: \"No data to export.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const headers = Object.keys(data[0]);\n        // 创建CSV内容\n        const csvContent = [\n            // CSV头部\n            headers.join(','),\n            // CSV数据行\n            ...data.map((row)=>headers.map((header)=>{\n                    const value = String(row[header] || '');\n                    // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号\n                    if (value.includes(',') || value.includes('\"') || value.includes('\\n')) {\n                        return '\"'.concat(value.replace(/\"/g, '\"\"'), '\"');\n                    }\n                    return value;\n                }).join(','))\n        ].join('\\n');\n        // 创建Blob并下载\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat(tableName, \"_\").concat(new Date().toISOString().slice(0, 19).replace(/:/g, '-'), \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n            title: \"Export Successful\",\n            description: \"\".concat(tableName, \" exported to CSV successfully.\")\n        });\n    };\n    // 获取数据库表列表\n    const fetchTables = async ()=>{\n        setIsLoadingTables(true);\n        try {\n            // 尝试多种不同的查询方式\n            const queries = [\n                \"USE gina_db; SHOW TABLES;\",\n                \"SHOW TABLES FROM gina_db;\",\n                \"SELECT name FROM gina_db.sys.tables ORDER BY name;\",\n                \"SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;\"\n            ];\n            for(let i = 0; i < queries.length; i++){\n                try {\n                    console.log(\"Trying query \".concat(i + 1, \": \").concat(queries[i]));\n                    const response = await fetch('/api/database-query', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            query: queries[i]\n                        })\n                    });\n                    const response_data = await response.json();\n                    if (response.ok) {\n                        console.log('Query succeeded:', response_data);\n                        // 提取表名列表\n                        const tableData = response_data.data;\n                        if (tableData && Object.keys(tableData).length > 0) {\n                            const firstKey = Object.keys(tableData)[0];\n                            const tableRows = tableData[firstKey];\n                            if (Array.isArray(tableRows) && tableRows.length > 0) {\n                                // 尝试不同的列名\n                                const possibleColumns = [\n                                    'TABLE_NAME',\n                                    'name',\n                                    'Tables_in_gina_db'\n                                ];\n                                let tableNames = [];\n                                // 首先尝试已知的列名\n                                for (const colName of possibleColumns){\n                                    if (tableRows[0].hasOwnProperty(colName)) {\n                                        tableNames = tableRows.map((row)=>String(row[colName])).filter(Boolean);\n                                        break;\n                                    }\n                                }\n                                // 如果没有找到已知列名，使用第一列\n                                if (tableNames.length === 0) {\n                                    tableNames = tableRows.map((row)=>{\n                                        const values = Object.values(row);\n                                        return values.length > 0 ? String(values[0]) : null;\n                                    }).filter((name)=>Boolean(name));\n                                }\n                                if (tableNames.length > 0) {\n                                    setTables(tableNames);\n                                    console.log('Found tables:', tableNames);\n                                    return; // 成功获取表列表，退出函数\n                                }\n                            }\n                        }\n                    } else {\n                        console.log(\"Query \".concat(i + 1, \" failed:\"), response_data.error);\n                    }\n                } catch (err) {\n                    console.log(\"Query \".concat(i + 1, \" error:\"), err);\n                    continue; // 尝试下一个查询\n                }\n            }\n            // 所有查询都失败了\n            throw new Error('All table listing queries failed. Please check database connection and permissions.');\n        } catch (err) {\n            console.error('Error fetching tables:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"Error\",\n                description: \"Failed to fetch table list: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingTables(false);\n        }\n    };\n    // 获取表的列信息\n    const fetchColumns = async (tableName)=>{\n        if (!tableName) return;\n        setIsLoadingColumns(true);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: \"SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '\".concat(tableName, \"' ORDER BY ORDINAL_POSITION;\")\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                const tableData = response_data.data;\n                if (tableData && Object.keys(tableData).length > 0) {\n                    const firstKey = Object.keys(tableData)[0];\n                    const columnRows = tableData[firstKey];\n                    if (Array.isArray(columnRows)) {\n                        const columnInfo = columnRows.map((row)=>{\n                            const columnName = row.COLUMN_NAME || row.column_name || '';\n                            const dataType = (row.DATA_TYPE || row.data_type || '').toLowerCase();\n                            // 根据数据类型判断字段类型\n                            let fieldType = 'string';\n                            if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('numeric') || dataType.includes('double') || dataType.includes('real') || dataType.includes('money') || dataType.includes('smallmoney')) {\n                                fieldType = 'number';\n                            } else if (dataType.includes('date') || dataType.includes('time') || dataType.includes('timestamp')) {\n                                fieldType = 'date';\n                            } else if (dataType.includes('bit') || dataType.includes('boolean')) {\n                                fieldType = 'boolean';\n                            }\n                            return {\n                                name: columnName,\n                                type: dataType,\n                                dataType: fieldType\n                            };\n                        });\n                        // 去重处理，以防有重复的列名\n                        const uniqueColumns = columnInfo.filter((col, index, self)=>index === self.findIndex((c)=>c.name === col.name));\n                        setColumns(uniqueColumns);\n                        console.log('Found columns:', uniqueColumns);\n                    }\n                }\n            } else {\n                throw new Error(response_data.error || 'Failed to fetch columns.');\n            }\n        } catch (err) {\n            console.error('Error fetching columns:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"Error\",\n                description: \"Failed to fetch column information: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingColumns(false);\n        }\n    };\n    // 页面加载时获取表列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DatabaseQueryPage.useEffect\": ()=>{\n            fetchTables();\n        }\n    }[\"DatabaseQueryPage.useEffect\"], []);\n    // 处理表选择\n    const handleTableSelect = (tableName)=>{\n        setSelectedTable(tableName);\n        setQuery(\"SELECT * FROM \".concat(tableName, \" LIMIT 100;\"));\n        setFilters([]); // 清空之前的筛选条件\n        fetchColumns(tableName); // 获取列信息\n    };\n    // 构建结构化查询\n    const buildStructuredQuery = ()=>{\n        if (!selectedTable) return \"\";\n        let whereClause = \"\";\n        const activeFilters = filters.filter((f)=>f.enabled && f.value !== \"\" && f.value !== null);\n        if (activeFilters.length > 0) {\n            const conditions = activeFilters.map((filter)=>{\n                const column = filter.column;\n                const value = filter.value;\n                switch(filter.operator){\n                    case 'equals':\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    case 'contains':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"%'\");\n                    case 'starts_with':\n                        return \"\".concat(column, \" LIKE '\").concat(value, \"%'\");\n                    case 'ends_with':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"'\");\n                    case 'regex':\n                        return \"\".concat(column, \" REGEXP '\").concat(value, \"'\");\n                    case 'greater_than':\n                        return \"\".concat(column, \" > \").concat(value);\n                    case 'less_than':\n                        return \"\".concat(column, \" < \").concat(value);\n                    case 'between':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN \").concat(value[0], \" AND \").concat(value[1]);\n                        }\n                        return \"\".concat(column, \" = \").concat(value);\n                    case 'date_range':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN '\").concat(value[0], \"' AND '\").concat(value[1], \"'\");\n                        }\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    default:\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                }\n            });\n            whereClause = \" WHERE \" + conditions.join(\" AND \");\n        }\n        return \"SELECT * FROM \".concat(selectedTable).concat(whereClause, \" LIMIT 100;\");\n    };\n    // 执行结构化查询\n    const handleStructuredQuery = async ()=>{\n        const structuredQuery = buildStructuredQuery();\n        setQuery(structuredQuery);\n        // 直接执行查询，不依赖状态更新\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: structuredQuery\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                setResult(response_data.data);\n            } else {\n                throw new Error(response_data.error || 'Query failed.');\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"Error\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleQuery = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query\n                })\n            });\n            const response_data = await response.json();\n            if (!response.ok) {\n                throw new Error(response_data.error || 'An unknown error occurred.');\n            }\n            // Extract the actual data from the new API response format\n            const actualData = response_data.data || {};\n            // Check if the result is an empty object, which is a valid success case\n            if (Object.keys(actualData).length === 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"Query Successful\",\n                    description: \"The query ran successfully but returned no data.\"\n                });\n                setResult({});\n            } else {\n                setResult(actualData);\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"Error\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 添加筛选条件\n    const addFilter = ()=>{\n        if (columns.length === 0) return;\n        const newFilter = {\n            column: columns[0].name,\n            operator: 'equals',\n            value: '',\n            enabled: true\n        };\n        setFilters([\n            ...filters,\n            newFilter\n        ]);\n    };\n    // 更新筛选条件\n    const updateFilter = (index, updates)=>{\n        const newFilters = [\n            ...filters\n        ];\n        newFilters[index] = {\n            ...newFilters[index],\n            ...updates\n        };\n        setFilters(newFilters);\n    };\n    // 删除筛选条件\n    const removeFilter = (index)=>{\n        const newFilters = filters.filter((_, i)=>i !== index);\n        setFilters(newFilters);\n    };\n    const renderTable = (tableName, data)=>{\n        if (data.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Table '\",\n                    tableName,\n                    \"' has no rows.\"\n                ]\n            }, tableName, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 441,\n                columnNumber: 14\n            }, this);\n        }\n        const headers = Object.keys(data[0]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    tableName,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 font-normal\",\n                                        children: [\n                                            \"(\",\n                                            data.length,\n                                            \" rows)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>exportToCSV(tableName, data),\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Export CSV\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto max-h-[600px] border rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHeader, {\n                                        className: \"sticky top-0 bg-white z-10 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                            children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2\",\n                                                    style: {\n                                                        minWidth: '120px'\n                                                    },\n                                                    children: header\n                                                }, header, false, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableBody, {\n                                        children: data.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                                className: \"hover:bg-gray-50\",\n                                                children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"whitespace-nowrap px-4 py-2 text-sm border-b\",\n                                                        style: {\n                                                            minWidth: '120px'\n                                                        },\n                                                        title: String(row[header]),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-w-[200px] truncate\",\n                                                            children: String(row[header])\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, \"\".concat(rowIndex, \"-\").concat(header), false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, rowIndex, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t\",\n                            children: [\n                                \"Total: \",\n                                data.length,\n                                \" records\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, tableName, true, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n            lineNumber: 445,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"Database Query\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 513,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"数据库查询\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"快速表格选择 (gina_db)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                onValueChange: handleTableSelect,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                        className: \"w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                            placeholder: isLoadingTables ? \"正在加载表格...\" : \"选择要查询的表格\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                        children: tables.map((tableName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                value: tableName,\n                                                                children: tableName\n                                                            }, tableName, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: fetchTables,\n                                                disabled: isLoadingTables,\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(isLoadingTables ? 'animate-spin' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"刷新\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this),\n                                    tables.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"在 gina_db 数据库中找到 \",\n                                            tables.length,\n                                            \" 个表格\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedTable,\n                                                    \" 的筛选条件\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: addFilter,\n                                                disabled: isLoadingColumns || columns.length === 0,\n                                                children: \"添加筛选\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this),\n                                    isLoadingColumns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 mb-2\",\n                                        children: \"正在加载列信息...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 17\n                                    }, this),\n                                    filters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-4\",\n                                        children: filters.map((filter, index)=>{\n                                            var _columns_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                        checked: filter.enabled,\n                                                        onCheckedChange: (enabled)=>updateFilter(index, {\n                                                                enabled\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.column,\n                                                        onValueChange: (column)=>updateFilter(index, {\n                                                                column\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-40\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: columns.map((col, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: col.name,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                col.dataType === 'string' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 598,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 599,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'date' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 600,\n                                                                                    columnNumber: 61\n                                                                                }, this),\n                                                                                col.name,\n                                                                                \" (\",\n                                                                                col.type,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, \"\".concat(col.name, \"-\").concat(colIndex), false, {\n                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.operator,\n                                                        onValueChange: (operator)=>updateFilter(index, {\n                                                                operator\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: (()=>{\n                                                                    const selectedColumn = columns.find((col)=>col.name === filter.column);\n                                                                    if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'string') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"等于\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"contains\",\n                                                                                children: \"包含\"\n                                                                            }, \"contains\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"starts_with\",\n                                                                                children: \"开头是\"\n                                                                            }, \"starts_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 622,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"ends_with\",\n                                                                                children: \"结尾是\"\n                                                                            }, \"ends_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 623,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"regex\",\n                                                                                children: \"正则表达式\"\n                                                                            }, \"regex\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'number') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"等于\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 628,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"greater_than\",\n                                                                                children: \"大于\"\n                                                                            }, \"greater_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"less_than\",\n                                                                                children: \"小于\"\n                                                                            }, \"less_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 630,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"between\",\n                                                                                children: \"范围\"\n                                                                            }, \"between\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 631,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'date') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"等于\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"date_range\",\n                                                                                children: \"日期范围\"\n                                                                            }, \"date_range\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 636,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    }\n                                                                    return [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: \"equals\",\n                                                                            children: \"等于\"\n                                                                        }, \"equals\", false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ];\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    filter.operator === 'between' || filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 654,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[0] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[0]), \"yyyy-MM-dd\") : \"开始日期\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 650,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[0] ? new Date(filter.value[0]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            dateStr,\n                                                                                            currentValue[1]\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 662,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"至\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 681,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[1] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[1]), \"yyyy-MM-dd\") : \"结束日期\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 676,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[1] ? new Date(filter.value[1]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            currentValue[0],\n                                                                                            dateStr\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 689,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"最小值\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[0] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                e.target.value,\n                                                                                currentValue[1]\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"至\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"最大值\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[1] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                currentValue[0],\n                                                                                e.target.value\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        type: ((_columns_find = columns.find((col)=>col.name === filter.column)) === null || _columns_find === void 0 ? void 0 : _columns_find.dataType) === 'number' ? 'number' : 'text',\n                                                        placeholder: \"值\",\n                                                        className: \"flex-1\",\n                                                        value: Array.isArray(filter.value) ? '' : filter.value,\n                                                        onChange: (e)=>updateFilter(index, {\n                                                                value: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeFilter(index),\n                                                        children: \"Remove\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: handleStructuredQuery,\n                                                disabled: isLoading,\n                                                className: \"flex-1\",\n                                                children: isLoading ? \"Querying...\" : \"Query with Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    setQuery(\"SELECT * FROM \".concat(selectedTable, \" LIMIT 100;\"));\n                                                    handleQuery();\n                                                },\n                                                disabled: isLoading,\n                                                children: \"Show All (100 rows)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        className: \"text-xs font-medium text-gray-600 mb-1 block\",\n                                        children: \"Generated Query:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"text-xs text-gray-800 font-mono\",\n                                        children: buildStructuredQuery() || \"SELECT * FROM \".concat(selectedTable, \" LIMIT 100;\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 772,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 514,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mt-4 bg-destructive/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-destructive\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 787,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 789,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 785,\n                columnNumber: 9\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-2\",\n                        children: \"Results\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 11\n                    }, this),\n                    Object.keys(result).length > 0 ? Object.entries(result).map((param)=>{\n                        let [tableName, data] = param;\n                        return renderTable(tableName, data);\n                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"The query executed successfully and returned no tables.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 801,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n        lineNumber: 512,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseQueryPage, \"37jn/e4rdN0iVNZCMb7xeH+UoSk=\");\n_c = DatabaseQueryPage;\nvar _c;\n$RefreshReg$(_c, \"DatabaseQueryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/database-query/page.tsx\n"));

/***/ })

});