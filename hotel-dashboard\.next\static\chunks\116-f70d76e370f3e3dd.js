"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[116],{1243:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(19946).A)("<PERSON><PERSON>lert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6262:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(19946).A)("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]])},15452:(e,t,n)=>{n.d(t,{UC:()=>et,VY:()=>eo,ZL:()=>$,bL:()=>Q,bm:()=>er,hE:()=>en,hJ:()=>ee,l9:()=>J});var o=n(12115),r=n(85185),i=n(6101),a=n(46081),l=n(61285),s=n(5845),c=n(19178),u=n(25519),d=n(34378),p=n(28905),f=n(63655),m=n(92293),h=n(93795),v=n(38168),g=n(99708),w=n(95155),y="Dialog",[b,x]=(0,a.A)(y),[C,S]=b(y),T=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:a,modal:c=!0}=e,u=o.useRef(null),d=o.useRef(null),[p=!1,f]=(0,s.i)({prop:r,defaultProp:i,onChange:a});return(0,w.jsx)(C,{scope:t,triggerRef:u,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:c,children:n})};T.displayName=y;var P="DialogTrigger",E=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,a=S(P,n),l=(0,i.s)(t,a.triggerRef);return(0,w.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":K(a.open),...o,ref:l,onClick:(0,r.m)(e.onClick,a.onOpenToggle)})});E.displayName=P;var k="DialogPortal",[R,D]=b(k,{forceMount:void 0}),Y=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,a=S(k,t);return(0,w.jsx)(R,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,w.jsx)(p.C,{present:n||a.open,children:(0,w.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};Y.displayName=k;var N="DialogOverlay",L=o.forwardRef((e,t)=>{let n=D(N,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,i=S(N,e.__scopeDialog);return i.modal?(0,w.jsx)(p.C,{present:o||i.open,children:(0,w.jsx)(A,{...r,ref:t})}):null});L.displayName=N;var A=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,r=S(N,n);return(0,w.jsx)(h.A,{as:g.DX,allowPinchZoom:!0,shards:[r.contentRef],children:(0,w.jsx)(f.sG.div,{"data-state":K(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),X="DialogContent",j=o.forwardRef((e,t)=>{let n=D(X,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,i=S(X,e.__scopeDialog);return(0,w.jsx)(p.C,{present:o||i.open,children:i.modal?(0,w.jsx)(O,{...r,ref:t}):(0,w.jsx)(I,{...r,ref:t})})});j.displayName=X;var O=o.forwardRef((e,t)=>{let n=S(X,e.__scopeDialog),a=o.useRef(null),l=(0,i.s)(t,n.contentRef,a);return o.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,w.jsx)(z,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),I=o.forwardRef((e,t)=>{let n=S(X,e.__scopeDialog),r=o.useRef(!1),i=o.useRef(!1);return(0,w.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,a;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(a=n.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,a;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let l=t.target;(null===(a=n.triggerRef.current)||void 0===a?void 0:a.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),z=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:l,...s}=e,d=S(X,n),p=o.useRef(null),f=(0,i.s)(t,p);return(0,m.Oh)(),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(u.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,w.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":K(d.open),...s,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(G,{titleId:d.titleId}),(0,w.jsx)(V,{contentRef:p,descriptionId:d.descriptionId})]})]})}),_="DialogTitle",W=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,r=S(_,n);return(0,w.jsx)(f.sG.h2,{id:r.titleId,...o,ref:t})});W.displayName=_;var M="DialogDescription",B=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,r=S(M,n);return(0,w.jsx)(f.sG.p,{id:r.descriptionId,...o,ref:t})});B.displayName=M;var H="DialogClose",F=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,i=S(H,n);return(0,w.jsx)(f.sG.button,{type:"button",...o,ref:t,onClick:(0,r.m)(e.onClick,()=>i.onOpenChange(!1))})});function K(e){return e?"open":"closed"}F.displayName=H;var Z="DialogTitleWarning",[q,U]=(0,a.q)(Z,{contentName:X,titleName:_,docsSlug:"dialog"}),G=e=>{let{titleId:t}=e,n=U(Z),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},V=e=>{let{contentRef:t,descriptionId:n}=e,r=U("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let o=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&o&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},Q=T,J=E,$=Y,ee=L,et=j,en=W,eo=B,er=F},27472:(e,t,n)=>{n.d(t,{GT:()=>ey,WZ:()=>eC});var o=n(12115),r=function(e,t){return Number(e.toFixed(t))},i=function(e,t,n){n&&"function"==typeof n&&n(e,t)},a={easeOut:function(e){return-Math.cos(e*Math.PI)/2+.5},linear:function(e){return e},easeInQuad:function(e){return e*e},easeOutQuad:function(e){return e*(2-e)},easeInOutQuad:function(e){return e<.5?2*e*e:-1+(4-2*e)*e},easeInCubic:function(e){return e*e*e},easeOutCubic:function(e){return--e*e*e+1},easeInOutCubic:function(e){return e<.5?4*e*e*e:(e-1)*(2*e-2)*(2*e-2)+1},easeInQuart:function(e){return e*e*e*e},easeOutQuart:function(e){return 1- --e*e*e*e},easeInOutQuart:function(e){return e<.5?8*e*e*e*e:1-8*--e*e*e*e},easeInQuint:function(e){return e*e*e*e*e},easeOutQuint:function(e){return 1+--e*e*e*e*e},easeInOutQuint:function(e){return e<.5?16*e*e*e*e*e:1+16*--e*e*e*e*e}},l=function(e){"number"==typeof e&&cancelAnimationFrame(e)},s=function(e){e.mounted&&(l(e.animation),e.animate=!1,e.animation=null,e.velocity=null)};function c(e,t,n,o){if(e.mounted){var r=new Date().getTime();s(e),e.animation=function(){if(!e.mounted)return l(e.animation);var i=new Date().getTime()-r,s=(0,a[t])(i/n);i>=n?(o(1),e.animation=null):e.animation&&(o(s),requestAnimationFrame(e.animation))},requestAnimationFrame(e.animation)}}function u(e,t,n,o){var r,i,a,l=(r=t.scale,i=t.positionX,a=t.positionY,!(Number.isNaN(r)||Number.isNaN(i)||Number.isNaN(a)));if(e.mounted&&l){var s=e.setTransformState,u=e.transformState,d=u.scale,p=u.positionX,f=u.positionY,m=t.scale-d,h=t.positionX-p,v=t.positionY-f;0===n?s(t.scale,t.positionX,t.positionY):c(e,o,n,function(e){s(d+m*e,p+h*e,f+v*e)})}}var d=function(e,t,n,o,r,i,a){var l=e>t?n*(a?1:.5):0,s=o>r?i*(a?1:.5):0;return{minPositionX:e-t-l,maxPositionX:l,minPositionY:o-r-s,maxPositionY:s}},p=function(e,t){var n,o,r,i,a,l,s=e.wrapperComponent,c=e.contentComponent,u=e.setup.centerZoomedOut;if(!s||!c)throw Error("Components are not mounted");var p=(n=s.offsetWidth,o=s.offsetHeight,r=c.offsetWidth,i=c.offsetHeight,{wrapperWidth:n,wrapperHeight:o,newContentWidth:a=r*t,newDiffWidth:n-a,newContentHeight:l=i*t,newDiffHeight:o-l}),f=p.wrapperWidth,m=p.wrapperHeight;return d(f,p.newContentWidth,p.newDiffWidth,m,p.newContentHeight,p.newDiffHeight,!!u)},f=function(e,t,n,o){return o?e<t?r(t,2):e>n?r(n,2):r(e,2):r(e,2)},m=function(e,t){var n=p(e,t);return e.bounds=n,n};function h(e,t,n,o,r,i,a){var l=n.minPositionX,s=n.minPositionY,c=n.maxPositionX,u=n.maxPositionY,d=0,p=0;return a&&(d=r,p=i),{x:f(e,l-d,c+d,o),y:f(t,s-p,u+p,o)}}function v(e,t,n,o,r,i){var a=e.transformState,l=a.scale,s=a.positionX,c=a.positionY,u=o-l;return"number"!=typeof t||"number"!=typeof n?(console.error("Mouse X and Y position were not provided!"),{x:s,y:c}):h(s-t*u,c-n*u,r,i,0,0,null)}function g(e,t,n,o,r){var i=t-(r?o:0);return!Number.isNaN(n)&&e>=n?n:!Number.isNaN(t)&&e<=i?i:e}var w=function(e,t){var n=e.setup.panning.excluded,o=e.isInitialized,r=e.wrapperComponent,i=t.target,a="shadowRoot"in i&&"composedPath"in t?t.composedPath().some(function(e){return e instanceof Element&&(null==r?void 0:r.contains(e))}):null==r?void 0:r.contains(i);return!(!(o&&i&&a)||G(i,n))},y=function(e){var t=e.isInitialized,n=e.isPanning,o=e.setup.panning.disabled;return!!t&&!!n&&!o},b=function(e,t){var n=e.transformState,o=n.positionX,r=n.positionY;e.isPanning=!0,e.startCoords={x:t.clientX-o,y:t.clientY-r}},x=function(e,t){var n=t.touches,o=e.transformState,r=o.positionX,i=o.positionY;e.isPanning=!0,1===n.length&&(e.startCoords={x:n[0].clientX-r,y:n[0].clientY-i})};function C(e,t,n,o,r){var i=e.setup.limitToBounds,a=e.wrapperComponent,l=e.bounds,s=e.transformState,c=s.scale,u=s.positionX,d=s.positionY;if(null!==a&&null!==l&&(t!==u||n!==d)){var p=h(t,n,l,i,o,r,a),f=p.x,m=p.y;e.setTransformState(c,f,m)}}var S=function(e,t,n){var o=e.startCoords,r=e.transformState,i=e.setup.panning,a=i.lockAxisX,l=i.lockAxisY,s=r.positionX,c=r.positionY;if(!o)return{x:s,y:c};var u=t-o.x,d=n-o.y;return{x:a?s:u,y:l?c:d}},T=function(e,t){var n=e.setup,o=e.transformState.scale,r=n.minScale,i=n.disablePadding;return t>0&&o>=r&&!i?t:0},P=function(e){var t=e.mounted,n=e.setup,o=n.disabled,r=n.velocityAnimation,i=e.transformState.scale;return!r.disabled||!!(i>1)||!o||!!t},E=function(e){var t=e.mounted,n=e.velocity,o=e.bounds,r=e.setup,i=r.disabled,a=r.velocityAnimation,l=e.transformState.scale;return(!a.disabled||!!(l>1)||!i||!!t)&&!!n&&!!o};function k(e,t,n,o,r,i,a,l,s,c){if(r){if(t>a&&n>a){var u=a+(e-a)*c;return u>s?s:u<a?a:u}if(t<i&&n<i){var u=i+(e-i)*c;return u<l?l:u>i?i:u}}return o?t:f(e,i,a,r)}function R(e,t){var n=e.transformState.scale;s(e),m(e,n),void 0!==window.TouchEvent&&t instanceof TouchEvent?x(e,t):b(e,t)}function D(e,t){var n=e.transformState.scale,o=e.setup,r=o.minScale,i=o.alignmentAnimation,a=i.disabled,l=i.sizeX,s=i.sizeY,c=i.animationTime,d=i.animationType;if(!(a||n<r||!l&&!s)){var p=function(e){var t=e.transformState,n=t.positionX,o=t.positionY,r=t.scale,i=e.setup,a=i.disabled,l=i.limitToBounds,s=i.centerZoomedOut,c=e.wrapperComponent;if(!a&&c&&e.bounds){var u=e.bounds,d=u.maxPositionX,p=u.minPositionX,f=u.maxPositionY,m=u.minPositionY,h=n>d?c.offsetWidth:e.setup.minPositionX||0,g=o>f?c.offsetHeight:e.setup.minPositionY||0,w=v(e,h,g,r,e.bounds,l||s),y=w.x,b=w.y;return{scale:r,positionX:n>d||n<p?y:n,positionY:o>f||o<m?b:o}}}(e);p&&u(e,p,null!=t?t:c,d)}}function Y(e,t,n){var o=e.startCoords,r=e.setup.alignmentAnimation,i=r.sizeX,a=r.sizeY;if(o){var l=S(e,t,n),s=l.x,c=l.y,u=T(e,i),d=T(e,a);!function(e,t){if(P(e)){var n=e.lastMousePosition,o=e.velocityTime,r=e.setup,i=e.wrapperComponent,a=r.velocityAnimation.equalToMove,l=Date.now();if(n&&o&&i){var s=a?Math.min(1,i.offsetWidth/window.innerWidth):1,c=t.x-n.x,u=t.y-n.y,d=Math.sqrt(c*c+u*u)/(l-o);e.velocity={velocityX:c/s,velocityY:u/s,total:d}}e.lastMousePosition=t,e.velocityTime=l}}(e,{x:s,y:c}),C(e,s,c,u,d)}}function N(e,t,n,o){var i=e.setup,a=i.minScale,l=i.maxScale,s=i.limitToBounds,c=g(r(t,2),a,l,0,!1),u=m(e,c),d=v(e,n,o,c,u,s);return{scale:c,positionX:d.x,positionY:d.y}}function L(e,t,n){var o=e.transformState.scale,r=e.wrapperComponent,i=e.setup,a=i.minScale,l=i.limitToBounds,s=i.zoomAnimation,c=s.disabled,d=s.animationTime,p=s.animationType,f=c||o>=a;if((o>=1||l)&&D(e),!f&&r&&e.mounted){var m=N(e,a,t||r.offsetWidth/2,n||r.offsetHeight/2);m&&u(e,m,d,p)}}var A=function(){return(A=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};function X(e,t,n){if(n||2==arguments.length)for(var o,r=0,i=t.length;r<i;r++)!o&&r in t||(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var j={scale:1,positionX:0,positionY:0},O={disabled:!1,minPositionX:null,maxPositionX:null,minPositionY:null,maxPositionY:null,minScale:1,maxScale:8,limitToBounds:!0,centerZoomedOut:!1,centerOnInit:!1,disablePadding:!1,smooth:!0,wheel:{step:.2,disabled:!1,smoothStep:.001,wheelDisabled:!1,touchPadDisabled:!1,activationKeys:[],excluded:[]},panning:{disabled:!1,velocityDisabled:!1,lockAxisX:!1,lockAxisY:!1,allowLeftClickPan:!0,allowMiddleClickPan:!0,allowRightClickPan:!0,wheelPanning:!1,activationKeys:[],excluded:[]},pinch:{step:5,disabled:!1,excluded:[]},doubleClick:{disabled:!1,step:.7,mode:"zoomIn",animationType:"easeOut",animationTime:200,excluded:[]},zoomAnimation:{disabled:!1,size:.4,animationTime:200,animationType:"easeOut"},alignmentAnimation:{disabled:!1,sizeX:100,sizeY:100,animationTime:200,velocityAlignmentTime:400,animationType:"easeOut"},velocityAnimation:{disabled:!1,sensitivity:1,animationTime:400,animationType:"easeOut",equalToMove:!0}},I={wrapperClass:"react-transform-wrapper",contentClass:"react-transform-component"},z=function(e){var t,n,o,r;return{previousScale:null!==(t=e.initialScale)&&void 0!==t?t:j.scale,scale:null!==(n=e.initialScale)&&void 0!==n?n:j.scale,positionX:null!==(o=e.initialPositionX)&&void 0!==o?o:j.positionX,positionY:null!==(r=e.initialPositionY)&&void 0!==r?r:j.positionY}},_=function(e){var t=A({},O);return Object.keys(e).forEach(function(n){var o=void 0!==e[n];if(void 0!==O[n]&&o){var r=Object.prototype.toString.call(O[n]);"[object Object]"===r?t[n]=A(A({},O[n]),e[n]):"[object Array]"===r?t[n]=X(X([],O[n],!0),e[n],!0):t[n]=e[n]}}),t},W=function(e,t,n){var o=e.transformState.scale,i=e.wrapperComponent,a=e.setup,l=a.maxScale,s=a.minScale,c=a.zoomAnimation,u=a.smooth,d=c.size;if(!i)throw Error("Wrapper is not mounted");return g(r(u?o*Math.exp(t*n):o+t*n,3),s,l,d,!1)};function M(e,t,n,o,r){var i=e.wrapperComponent,a=e.transformState,l=a.scale,s=a.positionX,c=a.positionY;if(!i)return console.error("No WrapperComponent found");var d=i.offsetWidth,p=i.offsetHeight,f=W(e,t,n),m=N(e,f,(d/2-s)/l,(p/2-c)/l);if(!m)return console.error("Error during zoom event. New transformation state was not calculated.");u(e,m,o,r)}function B(e,t,n,o){var r=e.setup,i=e.wrapperComponent,a=r.limitToBounds,l=z(e.props),s=e.transformState,c=s.scale,d=s.positionX,f=s.positionY;if(i){var m=p(e,l.scale),v=h(l.positionX,l.positionY,m,a,0,0,i),g={scale:l.scale,positionX:v.x,positionY:v.y};if(c===l.scale&&d===l.positionX&&f===l.positionY)return;null==o||o(),u(e,g,t,n)}}var H=function(e){return{instance:e,zoomIn:function(t,n,o){void 0===t&&(t=.5),void 0===n&&(n=300),void 0===o&&(o="easeOut"),M(e,1,t,n,o)},zoomOut:function(t,n,o){void 0===t&&(t=.5),void 0===n&&(n=300),void 0===o&&(o="easeOut"),M(e,-1,t,n,o)},setTransform:function(t,n,o,r,i){void 0===r&&(r=300),void 0===i&&(i="easeOut");var a=e.transformState,l=a.positionX,s=a.positionY,c=a.scale,d=e.wrapperComponent,p=e.contentComponent;!e.setup.disabled&&d&&p&&u(e,{positionX:Number.isNaN(t)?l:t,positionY:Number.isNaN(n)?s:n,scale:Number.isNaN(o)?c:o},r,i)},resetTransform:function(t,n){void 0===t&&(t=200),void 0===n&&(n="easeOut"),B(e,t,n)},centerView:function(t,n,o){void 0===n&&(n=200),void 0===o&&(o="easeOut");var r=e.transformState,i=e.wrapperComponent,a=e.contentComponent;i&&a&&u(e,Q(t||r.scale,i,a),n,o)},zoomToElement:function(t,n,o,r){void 0===o&&(o=600),void 0===r&&(r="easeOut"),s(e);var i=e.wrapperComponent,a="string"==typeof t?document.getElementById(t):t;if(i&&a&&i.contains(a)){var l=function(e,t,n){var o,r,i,a,l,s=e.wrapperComponent,c=e.contentComponent,u=e.transformState,d=e.setup,f=d.limitToBounds,m=d.minScale,v=d.maxScale;if(!s||!c)return u;var w=s.getBoundingClientRect(),y=t.getBoundingClientRect(),b=(o=t.getBoundingClientRect(),r=s.getBoundingClientRect(),i=c.getBoundingClientRect(),a=r.x*u.scale,l=r.y*u.scale,{x:(o.x-i.x+a)/u.scale,y:(o.y-i.y+l)/u.scale}),x=b.x,C=b.y,S=y.width/u.scale,T=y.height/u.scale,P=s.offsetWidth/S,E=s.offsetHeight/T,k=g(n||Math.min(P,E),m,v,0,!1),R=(w.width-S*k)/2,D=(w.height-T*k)/2,Y=h((w.left-x)*k+R,(w.top-C)*k+D,p(e,k),f,0,0,s);return{positionX:Y.x,positionY:Y.y,scale:k}}(e,a,n);u(e,l,o,r)}}}},F=function(e){return{instance:e,state:e.transformState}},K=function(e){var t={};return Object.assign(t,F(e)),Object.assign(t,H(e)),t},Z=!1;function q(){try{return{get passive(){return!1}}}catch(e){return!1}}var U=".".concat(I.wrapperClass),G=function(e,t){return t.some(function(t){return e.matches("".concat(U," ").concat(t,", ").concat(U," .").concat(t,", ").concat(U," ").concat(t," *, ").concat(U," .").concat(t," *"))})},V=function(e){e&&clearTimeout(e)},Q=function(e,t,n){var o=n.offsetWidth*e,r=n.offsetHeight*e;return{scale:e,positionX:(t.offsetWidth-o)/2,positionY:(t.offsetHeight-r)/2}},J=function(e,t){var n=e.setup.wheel,o=n.disabled,r=n.wheelDisabled,i=n.touchPadDisabled,a=n.excluded,l=e.isInitialized,s=e.isPanning,c=t.target;return!(!(l&&!s&&!o&&c)||r&&!t.ctrlKey||i&&t.ctrlKey||G(c,a))};function $(e,t,n){var o=t.getBoundingClientRect(),r=0,i=0;if("clientX"in e)r=(e.clientX-o.left)/n,i=(e.clientY-o.top)/n;else{var a=e.touches[0];r=(a.clientX-o.left)/n,i=(a.clientY-o.top)/n}return(Number.isNaN(r)||Number.isNaN(i))&&console.error("No mouse or touch offset found"),{x:r,y:i}}var ee=function(e,t,n,o,i){var a=e.transformState.scale,l=e.wrapperComponent,s=e.setup,c=s.maxScale,u=s.minScale,d=s.zoomAnimation,p=s.disablePadding,f=d.size,m=d.disabled;if(!l)throw Error("Wrapper is not mounted");var h=a+t*n;return i?h:g(r(h,3),u,c,f,!o&&!m&&!p)},et=function(e,t){var n=e.previousWheelEvent,o=e.transformState.scale,r=e.setup,i=r.maxScale,a=r.minScale;return!!n&&(!!(o<i)||!!(o>a)||Math.sign(n.deltaY)!==Math.sign(t.deltaY)||!!(n.deltaY>0)&&!!(n.deltaY<t.deltaY)||!!(n.deltaY<0)&&!!(n.deltaY>t.deltaY)||Math.sign(n.deltaY)!==Math.sign(t.deltaY))},en=function(e,t){var n=e.setup.pinch,o=n.disabled,r=n.excluded,i=e.isInitialized,a=t.target;return!(!(i&&!o&&a)||G(a,r))},eo=function(e){var t=e.setup.pinch.disabled,n=e.isInitialized,o=e.pinchStartDistance;return!!n&&!t&&!!o},er=function(e,t,n){var o=n.getBoundingClientRect(),i=e.touches,a=r(i[0].clientX-o.left,5),l=r(i[0].clientY-o.top,5);return{x:(a+r(i[1].clientX-o.left,5))/2/t,y:(l+r(i[1].clientY-o.top,5))/2/t}},ei=function(e){return Math.sqrt(Math.pow(e.touches[0].pageX-e.touches[1].pageX,2)+Math.pow(e.touches[0].pageY-e.touches[1].pageY,2))},ea=function(e,t){var n=e.pinchStartScale,o=e.pinchStartDistance,i=e.setup,a=i.maxScale,l=i.minScale,s=i.zoomAnimation,c=i.disablePadding,u=s.size,d=s.disabled;if(!n||null===o||!t)throw Error("Pinch touches distance was not provided");return t<0?e.transformState.scale:g(r(t/o*n,2),l,a,u,!d&&!c)},el=function(e,t){var n=e.props,o=n.onWheelStart,r=n.onZoomStart;e.wheelStopEventTimer||(s(e),i(K(e),t,o),i(K(e),t,r))},es=function(e,t){var n,o=e.props,r=o.onWheel,a=o.onZoom,l=e.contentComponent,s=e.setup,c=e.transformState.scale,u=s.limitToBounds,d=s.centerZoomedOut,p=s.zoomAnimation,f=s.wheel,h=s.disablePadding,g=s.smooth,w=p.size,y=p.disabled,b=f.step,x=f.smoothStep;if(!l)throw Error("Component not mounted");t.preventDefault(),t.stopPropagation();var C=ee(e,(n=t?t.deltaY<0?1:-1:0,n),g?x*Math.abs(t.deltaY):b,!t.ctrlKey);if(c!==C){var S=m(e,C),T=$(t,l,c),P=v(e,T.x,T.y,C,S,u&&(y||0===w||d||h)),E=P.x,k=P.y;e.previousWheelEvent=t,e.setTransformState(C,E,k),i(K(e),t,r),i(K(e),t,a)}},ec=function(e,t){var n=e.props,o=n.onWheelStop,r=n.onZoomStop;V(e.wheelAnimationTimer),e.wheelAnimationTimer=setTimeout(function(){e.mounted&&(L(e,t.x,t.y),e.wheelAnimationTimer=null)},100),et(e,t)&&(V(e.wheelStopEventTimer),e.wheelStopEventTimer=setTimeout(function(){e.mounted&&(e.wheelStopEventTimer=null,i(K(e),t,o),i(K(e),t,r))},160))},eu=function(e){for(var t=0,n=0,o=0;o<2;o+=1)t+=e.touches[o].clientX,n+=e.touches[o].clientY;return{x:t/2,y:n/2}},ed=function(e,t){var n=ei(t);e.pinchStartDistance=n,e.lastDistance=n,e.pinchStartScale=e.transformState.scale,e.isPanning=!1;var o=eu(t);e.pinchLastCenterX=o.x,e.pinchLastCenterY=o.y,s(e)},ep=function(e,t){var n=e.contentComponent,o=e.pinchStartDistance,r=e.wrapperComponent,i=e.transformState.scale,a=e.setup,l=a.limitToBounds,s=a.centerZoomedOut,c=a.zoomAnimation,u=a.alignmentAnimation,d=c.disabled,p=c.size;if(null!==o&&n){var f=er(t,i,n);if(Number.isFinite(f.x)&&Number.isFinite(f.y)){var g=ei(t),w=ea(e,g),y=eu(t),b=y.x-(e.pinchLastCenterX||0),x=y.y-(e.pinchLastCenterY||0);if(w!==i||0!==b||0!==x){e.pinchLastCenterX=y.x,e.pinchLastCenterY=y.y;var C=m(e,w),S=v(e,f.x,f.y,w,C,l&&(d||0===p||s)),P=S.x,E=S.y;e.pinchMidpoint=f,e.lastDistance=g;var k=u.sizeX,R=u.sizeY,D=h(P+b,E+x,C,l,T(e,k),T(e,R),r),Y=D.x,N=D.y;e.setTransformState(w,Y,N)}}}},ef=function(e){var t=e.pinchMidpoint;e.velocity=null,e.lastDistance=null,e.pinchMidpoint=null,e.pinchStartScale=null,e.pinchStartDistance=null,L(e,null==t?void 0:t.x,null==t?void 0:t.y)},em=function(e,t){var n=e.props.onZoomStop,o=e.setup.doubleClick.animationTime;V(e.doubleClickStopEventTimer),e.doubleClickStopEventTimer=setTimeout(function(){e.doubleClickStopEventTimer=null,i(K(e),t,n)},o)},eh=function(e,t){var n=e.props,o=n.onZoomStart,r=n.onZoom,a=e.setup.doubleClick,l=a.animationTime,s=a.animationType;i(K(e),t,o),B(e,l,s,function(){return i(K(e),t,r)}),em(e,t)},ev=function(e,t){var n=e.isInitialized,o=e.setup,r=e.wrapperComponent,i=o.doubleClick,a=i.disabled,l=i.excluded,s=t.target,c=null==r?void 0:r.contains(s);return!(!(n&&s&&c&&!a)||G(s,l))},eg=function(e){var t=this;this.mounted=!0,this.pinchLastCenterX=null,this.pinchLastCenterY=null,this.onChangeCallbacks=new Set,this.onInitCallbacks=new Set,this.wrapperComponent=null,this.contentComponent=null,this.isInitialized=!1,this.bounds=null,this.previousWheelEvent=null,this.wheelStopEventTimer=null,this.wheelAnimationTimer=null,this.isPanning=!1,this.isWheelPanning=!1,this.startCoords=null,this.lastTouch=null,this.distance=null,this.lastDistance=null,this.pinchStartDistance=null,this.pinchStartScale=null,this.pinchMidpoint=null,this.doubleClickStopEventTimer=null,this.velocity=null,this.velocityTime=null,this.lastMousePosition=null,this.animate=!1,this.animation=null,this.maxBounds=null,this.pressedKeys={},this.mount=function(){t.initializeWindowEvents()},this.unmount=function(){t.cleanupWindowEvents()},this.update=function(e){t.props=e,m(t,t.transformState.scale),t.setup=_(e)},this.initializeWindowEvents=function(){var e,n,o=q(),r=null===(e=t.wrapperComponent)||void 0===e?void 0:e.ownerDocument,i=null==r?void 0:r.defaultView;null===(n=t.wrapperComponent)||void 0===n||n.addEventListener("wheel",t.onWheelPanning,o),null==i||i.addEventListener("mousedown",t.onPanningStart,o),null==i||i.addEventListener("mousemove",t.onPanning,o),null==i||i.addEventListener("mouseup",t.onPanningStop,o),null==r||r.addEventListener("mouseleave",t.clearPanning,o),null==i||i.addEventListener("keyup",t.setKeyUnPressed,o),null==i||i.addEventListener("keydown",t.setKeyPressed,o)},this.cleanupWindowEvents=function(){var e,n,o=q(),r=null===(e=t.wrapperComponent)||void 0===e?void 0:e.ownerDocument,i=null==r?void 0:r.defaultView;null==i||i.removeEventListener("mousedown",t.onPanningStart,o),null==i||i.removeEventListener("mousemove",t.onPanning,o),null==i||i.removeEventListener("mouseup",t.onPanningStop,o),null==r||r.removeEventListener("mouseleave",t.clearPanning,o),null==i||i.removeEventListener("keyup",t.setKeyUnPressed,o),null==i||i.removeEventListener("keydown",t.setKeyPressed,o),document.removeEventListener("mouseleave",t.clearPanning,o),s(t),null===(n=t.observer)||void 0===n||n.disconnect()},this.handleInitializeWrapperEvents=function(e){var n=q();e.addEventListener("wheel",t.onWheelZoom,n),e.addEventListener("dblclick",t.onDoubleClick,n),e.addEventListener("touchstart",t.onTouchPanningStart,n),e.addEventListener("touchmove",t.onTouchPanning,n),e.addEventListener("touchend",t.onTouchPanningStop,n)},this.handleInitialize=function(e,n){var o=!1,r=t.setup.centerOnInit,i=function(e,t){for(var n=0;n<e.length;n++)if(e[n].target===t)return!0;return!1};t.applyTransformation(),t.onInitCallbacks.forEach(function(e){e(K(t))}),t.observer=new ResizeObserver(function(a){if(i(a,e)||i(a,n)){if(r&&!o){var l=n.offsetWidth,c=n.offsetHeight;(l>0||c>0)&&(o=!0,t.setCenter())}else s(t),m(t,t.transformState.scale),D(t,0)}}),t.observer.observe(e),t.observer.observe(n)},this.onWheelZoom=function(e){!t.setup.disabled&&J(t,e)&&t.isPressingKeys(t.setup.wheel.activationKeys)&&(el(t,e),es(t,e),ec(t,e))},this.onWheelPanning=function(e){var n=t.setup,o=n.disabled,r=n.wheel,i=n.panning;if(t.wrapperComponent&&t.contentComponent&&!o&&r.wheelDisabled&&!i.disabled&&i.wheelPanning&&!e.ctrlKey){e.preventDefault(),e.stopPropagation();var a=t.transformState,l=a.positionX,s=a.positionY,c=l-e.deltaX,u=s-e.deltaY,d=i.lockAxisX?l:c,p=i.lockAxisY?s:u,f=t.setup.alignmentAnimation,m=f.sizeX,h=f.sizeY,v=T(t,m),g=T(t,h);(d!==l||p!==s)&&C(t,d,p,v,g)}},this.onPanningStart=function(e){var n=t.setup.disabled,o=t.props.onPanningStart;!n&&w(t,e)&&t.isPressingKeys(t.setup.panning.activationKeys)&&(0!==e.button||t.setup.panning.allowLeftClickPan)&&(1!==e.button||t.setup.panning.allowMiddleClickPan)&&(2!==e.button||t.setup.panning.allowRightClickPan)&&(e.preventDefault(),e.stopPropagation(),s(t),R(t,e),i(K(t),e,o))},this.onPanning=function(e){var n=t.setup.disabled,o=t.props.onPanning;!n&&y(t)&&t.isPressingKeys(t.setup.panning.activationKeys)&&(e.preventDefault(),e.stopPropagation(),Y(t,e.clientX,e.clientY),i(K(t),e,o))},this.onPanningStop=function(e){var n=t.props.onPanningStop;t.isPanning&&(function(e){if(e.isPanning){var t=e.setup.panning.velocityDisabled,n=e.velocity,o=e.wrapperComponent,r=e.contentComponent;e.isPanning=!1,e.animate=!1,e.animation=null;var i=null==o?void 0:o.getBoundingClientRect(),l=null==r?void 0:r.getBoundingClientRect(),s=(null==i?void 0:i.width)||0,u=(null==i?void 0:i.height)||0,d=(null==l?void 0:l.width)||0,p=(null==l?void 0:l.height)||0;!t&&n&&(null==n?void 0:n.total)>.1&&(s<d||u<p)?function(e){var t,n,o,r,i=e.velocity,l=e.bounds,s=e.setup,u=e.wrapperComponent;if(E(e)&&i&&l&&u){var d=i.velocityX,p=i.velocityY,f=i.total,m=l.maxPositionX,h=l.minPositionX,v=l.maxPositionY,g=l.minPositionY,w=s.limitToBounds,y=s.alignmentAnimation,b=s.zoomAnimation,x=s.panning,C=x.lockAxisY,S=x.lockAxisX,P=b.animationType,R=y.sizeX,D=y.sizeY,Y=y.velocityAlignmentTime,N=Math.max((n=(t=e.setup.velocityAnimation).equalToMove,o=t.animationTime,r=t.sensitivity,n?o*f*r:o),Y),L=T(e,R),A=T(e,D),X=L*u.offsetWidth/100,j=A*u.offsetHeight/100,O=m+X,I=h-X,z=v+j,_=g-j,W=e.transformState,M=new Date().getTime();c(e,P,N,function(t){var n=e.transformState,o=n.scale,r=n.positionX,i=n.positionY,l=new Date().getTime()-M,s=1-(0,a[y.animationType])(Math.min(1,l/Y)),c=1-t,u=r+d*c,f=i+p*c,b=k(u,W.positionX,r,S,w,h,m,I,O,s),x=k(f,W.positionY,i,C,w,g,v,_,z,s);(r!==u||i!==f)&&e.setTransformState(o,b,x)})}}(e):D(e)}}(t),i(K(t),e,n))},this.onPinchStart=function(e){var n=t.setup.disabled,o=t.props,r=o.onPinchingStart,a=o.onZoomStart;!n&&en(t,e)&&(ed(t,e),s(t),i(K(t),e,r),i(K(t),e,a))},this.onPinch=function(e){var n=t.setup.disabled,o=t.props,r=o.onPinching,a=o.onZoom;!n&&eo(t)&&(e.preventDefault(),e.stopPropagation(),ep(t,e),i(K(t),e,r),i(K(t),e,a))},this.onPinchStop=function(e){var n=t.props,o=n.onPinchingStop,r=n.onZoomStop;t.pinchStartScale&&(ef(t),i(K(t),e,o),i(K(t),e,r))},this.onTouchPanningStart=function(e){var n=t.setup.disabled,o=t.props.onPanningStart;if(!n&&w(t,e)&&!(t.lastTouch&&+new Date-t.lastTouch<200&&1===e.touches.length)){t.lastTouch=+new Date,s(t);var r=e.touches,a=1===r.length,l=2===r.length;a&&(s(t),R(t,e),i(K(t),e,o)),l&&t.onPinchStart(e)}},this.onTouchPanning=function(e){var n=t.setup.disabled,o=t.props.onPanning;if(t.isPanning&&1===e.touches.length){if(n||!y(t))return;e.preventDefault(),e.stopPropagation();var r=e.touches[0];Y(t,r.clientX,r.clientY),i(K(t),e,o)}else e.touches.length>1&&t.onPinch(e)},this.onTouchPanningStop=function(e){t.onPanningStop(e),t.onPinchStop(e)},this.onDoubleClick=function(e){!t.setup.disabled&&ev(t,e)&&function(e,t){var n,o=e.setup,r=e.doubleClickStopEventTimer,a=e.transformState,l=e.contentComponent,s=a.scale,c=e.props,d=c.onZoomStart,p=c.onZoom,f=o.doubleClick,m=f.disabled,h=f.mode,v=f.step,g=f.animationTime,w=f.animationType;if(!m&&!r){if("reset"===h)return eh(e,t);if(!l)return console.error("No ContentComponent found");var y=(n=e.transformState.scale,"toggle"===h?1===n?1:-1:"zoomOut"===h?-1:1),b=W(e,y,v);if(s!==b){i(K(e),t,d);var x=$(t,l,s),C=N(e,b,x.x,x.y);if(!C)return console.error("Error during zoom event. New transformation state was not calculated.");i(K(e),t,p),u(e,C,g,w),em(e,t)}}}(t,e)},this.clearPanning=function(e){t.isPanning&&t.onPanningStop(e)},this.setKeyPressed=function(e){t.pressedKeys[e.key]=!0},this.setKeyUnPressed=function(e){t.pressedKeys[e.key]=!1},this.isPressingKeys=function(e){return!e.length||!!e.find(function(e){return t.pressedKeys[e]})},this.setTransformState=function(e,n,o){var r=t.props.onTransformed;if(Number.isNaN(e)||Number.isNaN(n)||Number.isNaN(o))console.error("Detected NaN set state values");else{e!==t.transformState.scale&&(t.transformState.previousScale=t.transformState.scale,t.transformState.scale=e),t.transformState.positionX=n,t.transformState.positionY=o,t.applyTransformation();var a=K(t);t.onChangeCallbacks.forEach(function(e){return e(a)}),i(a,{scale:e,positionX:n,positionY:o},r)}},this.setCenter=function(){if(t.wrapperComponent&&t.contentComponent){var e=Q(t.transformState.scale,t.wrapperComponent,t.contentComponent);t.setTransformState(e.scale,e.positionX,e.positionY)}},this.handleTransformStyles=function(e,n,o){return t.props.customTransform?t.props.customTransform(e,n,o):"translate(".concat(e,"px, ").concat(n,"px) scale(").concat(o,")")},this.applyTransformation=function(){if(t.mounted&&t.contentComponent){var e=t.transformState,n=e.scale,o=e.positionX,r=e.positionY,i=t.handleTransformStyles(o,r,n);t.contentComponent.style.transform=i}},this.getContext=function(){return K(t)},this.onChange=function(e){return t.onChangeCallbacks.has(e)||t.onChangeCallbacks.add(e),function(){t.onChangeCallbacks.delete(e)}},this.onInit=function(e){return t.onInitCallbacks.has(e)||t.onInitCallbacks.add(e),function(){t.onInitCallbacks.delete(e)}},this.init=function(e,n){t.cleanupWindowEvents(),t.wrapperComponent=e,t.contentComponent=n,m(t,t.transformState.scale),t.handleInitializeWrapperEvents(e),t.handleInitialize(e,n),t.initializeWindowEvents(),t.isInitialized=!0,i(K(t),void 0,t.props.onInit)},this.props=e,this.setup=_(this.props),this.transformState=z(this.props)},ew=o.createContext(null),ey=o.forwardRef(function(e,t){var n,r,i=(0,o.useRef)(new eg(e)).current,a=(n=e.children,r=H(i),"function"==typeof n?n(r):n);return(0,o.useImperativeHandle)(t,function(){return H(i)},[i]),(0,o.useEffect)(function(){i.update(e)},[i,e]),o.createElement(ew.Provider,{value:i},a)});o.forwardRef(function(e,t){var n,r=(0,o.useRef)(null),i=(0,o.useContext)(ew);return(0,o.useEffect)(function(){return i.onChange(function(e){r.current&&(r.current.style.transform=i.handleTransformStyles(0,0,1/e.instance.transformState.scale))})},[i]),o.createElement("div",A({},e,{ref:(n=[r,t],function(e){n.forEach(function(t){"function"==typeof t?t(e):null!=t&&(t.current=e)})})}))});var eb={width:0,height:0,y:0,x:0,top:0,bottom:0,left:0,right:0},ex={wrapper:"transform-component-module_wrapper__SPB86",content:"transform-component-module_content__FBWxo"};!function(e,t){void 0===t&&(t={});var n=t.insertAt;if(e&&"undefined"!=typeof document){var o=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css","top"===n&&o.firstChild?o.insertBefore(r,o.firstChild):o.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}}(".transform-component-module_wrapper__SPB86 {\n  position: relative;\n  width: -moz-fit-content;\n  width: fit-content;\n  height: -moz-fit-content;\n  height: fit-content;\n  overflow: hidden;\n  -webkit-touch-callout: none; /* iOS Safari */\n  -webkit-user-select: none; /* Safari */\n  -khtml-user-select: none; /* Konqueror HTML */\n  -moz-user-select: none; /* Firefox */\n  -ms-user-select: none; /* Internet Explorer/Edge */\n  user-select: none;\n  margin: 0;\n  padding: 0;\n  transform: translate3d(0, 0, 0);\n}\n.transform-component-module_content__FBWxo {\n  display: flex;\n  flex-wrap: wrap;\n  width: -moz-fit-content;\n  width: fit-content;\n  height: -moz-fit-content;\n  height: fit-content;\n  margin: 0;\n  padding: 0;\n  transform-origin: 0% 0%;\n}\n.transform-component-module_content__FBWxo img {\n  pointer-events: none;\n}\n");var eC=function(e){var t=e.children,n=e.wrapperClass,r=e.contentClass,i=e.wrapperStyle,a=e.contentStyle,l=e.wrapperProps,s=e.contentProps,c=(0,o.useContext)(ew),u=c.init,d=c.cleanupWindowEvents,p=(0,o.useRef)(null),f=(0,o.useRef)(null);return(0,o.useEffect)(function(){var e=p.current,t=f.current;return null!==e&&null!==t&&u&&(null==u||u(e,t)),function(){null==d||d()}},[]),o.createElement("div",A({},void 0===l?{}:l,{ref:p,className:"".concat(I.wrapperClass," ").concat(ex.wrapper," ").concat(void 0===n?"":n),style:i}),o.createElement("div",A({},void 0===s?{}:s,{ref:f,className:"".concat(I.contentClass," ").concat(ex.content," ").concat(void 0===r?"":r),style:a}),t))},eS=function(){var e=useContext(ew);if(!e)throw Error("Transform context must be placed inside TransformWrapper");return e}},40133:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(19946).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},47655:(e,t,n)=>{n.d(t,{LM:()=>U,OK:()=>G,VM:()=>S,bL:()=>q,lr:()=>X});var o=n(12115),r=n(63655),i=n(28905),a=n(46081),l=n(6101),s=n(39033),c=n(94315),u=n(52712),d=n(89367),p=n(85185),f=n(95155),m="ScrollArea",[h,v]=(0,a.A)(m),[g,w]=h(m),y=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:i="hover",dir:a,scrollHideDelay:s=600,...u}=e,[d,p]=o.useState(null),[m,h]=o.useState(null),[v,w]=o.useState(null),[y,b]=o.useState(null),[x,C]=o.useState(null),[S,T]=o.useState(0),[P,E]=o.useState(0),[k,R]=o.useState(!1),[D,Y]=o.useState(!1),N=(0,l.s)(t,e=>p(e)),L=(0,c.jH)(a);return(0,f.jsx)(g,{scope:n,type:i,dir:L,scrollHideDelay:s,scrollArea:d,viewport:m,onViewportChange:h,content:v,onContentChange:w,scrollbarX:y,onScrollbarXChange:b,scrollbarXEnabled:k,onScrollbarXEnabledChange:R,scrollbarY:x,onScrollbarYChange:C,scrollbarYEnabled:D,onScrollbarYEnabledChange:Y,onCornerWidthChange:T,onCornerHeightChange:E,children:(0,f.jsx)(r.sG.div,{dir:L,...u,ref:N,style:{position:"relative","--radix-scroll-area-corner-width":S+"px","--radix-scroll-area-corner-height":P+"px",...e.style}})})});y.displayName=m;var b="ScrollAreaViewport",x=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:i,nonce:a,...s}=e,c=w(b,n),u=o.useRef(null),d=(0,l.s)(t,u,c.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,f.jsx)(r.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,f.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var C="ScrollAreaScrollbar",S=o.forwardRef((e,t)=>{let{forceMount:n,...r}=e,i=w(C,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:l}=i,s="horizontal"===e.orientation;return o.useEffect(()=>(s?a(!0):l(!0),()=>{s?a(!1):l(!1)}),[s,a,l]),"hover"===i.type?(0,f.jsx)(T,{...r,ref:t,forceMount:n}):"scroll"===i.type?(0,f.jsx)(P,{...r,ref:t,forceMount:n}):"auto"===i.type?(0,f.jsx)(E,{...r,ref:t,forceMount:n}):"always"===i.type?(0,f.jsx)(k,{...r,ref:t}):null});S.displayName=C;var T=o.forwardRef((e,t)=>{let{forceMount:n,...r}=e,a=w(C,e.__scopeScrollArea),[l,s]=o.useState(!1);return o.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),s(!0)},o=()=>{t=window.setTimeout(()=>s(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",o),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",o)}}},[a.scrollArea,a.scrollHideDelay]),(0,f.jsx)(i.C,{present:n||l,children:(0,f.jsx)(E,{"data-state":l?"visible":"hidden",...r,ref:t})})}),P=o.forwardRef((e,t)=>{var n;let{forceMount:r,...a}=e,l=w(C,e.__scopeScrollArea),s="horizontal"===e.orientation,c=K(()=>d("SCROLL_END"),100),[u,d]=(n={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,t)=>{let o=n[e][t];return null!=o?o:e},"hidden"));return o.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>d("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,l.scrollHideDelay,d]),o.useEffect(()=>{let e=l.viewport,t=s?"scrollLeft":"scrollTop";if(e){let n=e[t],o=()=>{let o=e[t];n!==o&&(d("SCROLL"),c()),n=o};return e.addEventListener("scroll",o),()=>e.removeEventListener("scroll",o)}},[l.viewport,s,d,c]),(0,f.jsx)(i.C,{present:r||"hidden"!==u,children:(0,f.jsx)(k,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:t,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),E=o.forwardRef((e,t)=>{let n=w(C,e.__scopeScrollArea),{forceMount:r,...a}=e,[l,s]=o.useState(!1),c="horizontal"===e.orientation,u=K(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;s(c?e:t)}},10);return Z(n.viewport,u),Z(n.content,u),(0,f.jsx)(i.C,{present:r||l,children:(0,f.jsx)(k,{"data-state":l?"visible":"hidden",...a,ref:t})})}),k=o.forwardRef((e,t)=>{let{orientation:n="vertical",...r}=e,i=w(C,e.__scopeScrollArea),a=o.useRef(null),l=o.useRef(0),[s,c]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=W(s.viewport,s.content),d={...r,sizes:s,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function p(e,t){return function(e,t,n){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",r=M(n),i=t||r/2,a=n.scrollbar.paddingStart+i,l=n.scrollbar.size-n.scrollbar.paddingEnd-(r-i),s=n.content-n.viewport;return H([a,l],"ltr"===o?[0,s]:[-1*s,0])(e)}(e,l.current,s,t)}return"horizontal"===n?(0,f.jsx)(R,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=B(i.viewport.scrollLeft,s,i.dir);a.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=p(e,i.dir))}}):"vertical"===n?(0,f.jsx)(D,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=B(i.viewport.scrollTop,s);a.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=p(e))}}):null}),R=o.forwardRef((e,t)=>{let{sizes:n,onSizesChange:r,...i}=e,a=w(C,e.__scopeScrollArea),[s,c]=o.useState(),u=o.useRef(null),d=(0,l.s)(t,u,a.onScrollbarXChange);return o.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,f.jsx)(L,{"data-orientation":"horizontal",...i,ref:d,sizes:n,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":M(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(a.viewport){let o=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(o),function(e,t){return e>0&&e<t}(o,n)&&t.preventDefault()}},onResize:()=>{u.current&&a.viewport&&s&&r({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:_(s.paddingLeft),paddingEnd:_(s.paddingRight)}})}})}),D=o.forwardRef((e,t)=>{let{sizes:n,onSizesChange:r,...i}=e,a=w(C,e.__scopeScrollArea),[s,c]=o.useState(),u=o.useRef(null),d=(0,l.s)(t,u,a.onScrollbarYChange);return o.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,f.jsx)(L,{"data-orientation":"vertical",...i,ref:d,sizes:n,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":M(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(a.viewport){let o=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(o),function(e,t){return e>0&&e<t}(o,n)&&t.preventDefault()}},onResize:()=>{u.current&&a.viewport&&s&&r({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:_(s.paddingTop),paddingEnd:_(s.paddingBottom)}})}})}),[Y,N]=h(C),L=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:i,hasThumb:a,onThumbChange:c,onThumbPointerUp:u,onThumbPointerDown:d,onThumbPositionChange:m,onDragScroll:h,onWheelScroll:v,onResize:g,...y}=e,b=w(C,n),[x,S]=o.useState(null),T=(0,l.s)(t,e=>S(e)),P=o.useRef(null),E=o.useRef(""),k=b.viewport,R=i.content-i.viewport,D=(0,s.c)(v),N=(0,s.c)(m),L=K(g,10);function A(e){P.current&&h({x:e.clientX-P.current.left,y:e.clientY-P.current.top})}return o.useEffect(()=>{let e=e=>{let t=e.target;(null==x?void 0:x.contains(t))&&D(e,R)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[k,x,R,D]),o.useEffect(N,[i,N]),Z(x,L),Z(b.content,L),(0,f.jsx)(Y,{scope:n,scrollbar:x,hasThumb:a,onThumbChange:(0,s.c)(c),onThumbPointerUp:(0,s.c)(u),onThumbPositionChange:N,onThumbPointerDown:(0,s.c)(d),children:(0,f.jsx)(r.sG.div,{...y,ref:T,style:{position:"absolute",...y.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),P.current=x.getBoundingClientRect(),E.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),A(e))}),onPointerMove:(0,p.m)(e.onPointerMove,A),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=E.current,b.viewport&&(b.viewport.style.scrollBehavior=""),P.current=null})})})}),A="ScrollAreaThumb",X=o.forwardRef((e,t)=>{let{forceMount:n,...o}=e,r=N(A,e.__scopeScrollArea);return(0,f.jsx)(i.C,{present:n||r.hasThumb,children:(0,f.jsx)(j,{ref:t,...o})})}),j=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:i,...a}=e,s=w(A,n),c=N(A,n),{onThumbPositionChange:u}=c,d=(0,l.s)(t,e=>c.onThumbChange(e)),m=o.useRef(void 0),h=K(()=>{m.current&&(m.current(),m.current=void 0)},100);return o.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{h(),m.current||(m.current=F(e,u),u())};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,h,u]),(0,f.jsx)(r.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,o=e.clientY-t.top;c.onThumbPointerDown({x:n,y:o})}),onPointerUp:(0,p.m)(e.onPointerUp,c.onThumbPointerUp)})});X.displayName=A;var O="ScrollAreaCorner",I=o.forwardRef((e,t)=>{let n=w(O,e.__scopeScrollArea),o=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&o?(0,f.jsx)(z,{...e,ref:t}):null});I.displayName=O;var z=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,...i}=e,a=w(O,n),[l,s]=o.useState(0),[c,u]=o.useState(0),d=!!(l&&c);return Z(a.scrollbarX,()=>{var e;let t=(null===(e=a.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;a.onCornerHeightChange(t),u(t)}),Z(a.scrollbarY,()=>{var e;let t=(null===(e=a.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;a.onCornerWidthChange(t),s(t)}),d?(0,f.jsx)(r.sG.div,{...i,ref:t,style:{width:l,height:c,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function _(e){return e?parseInt(e,10):0}function W(e,t){let n=e/t;return isNaN(n)?0:n}function M(e){let t=W(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function B(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",o=M(t),r=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-r,a=t.content-t.viewport,l=(0,d.q)(e,"ltr"===n?[0,a]:[-1*a,0]);return H([0,a],[0,i-o])(l)}function H(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let o=(t[1]-t[0])/(e[1]-e[0]);return t[0]+o*(n-e[0])}}var F=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},n={left:e.scrollLeft,top:e.scrollTop},o=0;return!function r(){let i={left:e.scrollLeft,top:e.scrollTop},a=n.left!==i.left,l=n.top!==i.top;(a||l)&&t(),n=i,o=window.requestAnimationFrame(r)}(),()=>window.cancelAnimationFrame(o)};function K(e,t){let n=(0,s.c)(e),r=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(r.current),[]),o.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(n,t)},[n,t])}function Z(e,t){let n=(0,s.c)(t);(0,u.N)(()=>{let t=0;if(e){let o=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return o.observe(e),()=>{window.cancelAnimationFrame(t),o.unobserve(e)}}},[e,n])}var q=y,U=x,G=I},51154:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54416:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},54481:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(19946).A)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]])},76981:(e,t,n)=>{n.d(t,{C1:()=>P,bL:()=>T});var o=n(12115),r=n(6101),i=n(46081),a=n(85185),l=n(5845),s=n(45503),c=n(11275),u=n(28905),d=n(63655),p=n(95155),f="Checkbox",[m,h]=(0,i.A)(f),[v,g]=m(f),w=o.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:i,checked:s,defaultChecked:c,required:u,disabled:f,value:m="on",onCheckedChange:h,form:g,...w}=e,[y,b]=o.useState(null),T=(0,r.s)(t,e=>b(e)),P=o.useRef(!1),E=!y||g||!!y.closest("form"),[k=!1,R]=(0,l.i)({prop:s,defaultProp:c,onChange:h}),D=o.useRef(k);return o.useEffect(()=>{let e=null==y?void 0:y.form;if(e){let t=()=>R(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[y,R]),(0,p.jsxs)(v,{scope:n,state:k,disabled:f,children:[(0,p.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":C(k)?"mixed":k,"aria-required":u,"data-state":S(k),"data-disabled":f?"":void 0,disabled:f,value:m,...w,ref:T,onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.m)(e.onClick,e=>{R(e=>!!C(e)||!e),E&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),E&&(0,p.jsx)(x,{control:y,bubbles:!P.current,name:i,value:m,checked:k,required:u,disabled:f,form:g,style:{transform:"translateX(-100%)"},defaultChecked:!C(c)&&c})]})});w.displayName=f;var y="CheckboxIndicator",b=o.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:o,...r}=e,i=g(y,n);return(0,p.jsx)(u.C,{present:o||C(i.state)||!0===i.state,children:(0,p.jsx)(d.sG.span,{"data-state":S(i.state),"data-disabled":i.disabled?"":void 0,...r,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=y;var x=e=>{let{control:t,checked:n,bubbles:r=!0,defaultChecked:i,...a}=e,l=o.useRef(null),u=(0,s.Z)(n),d=(0,c.X)(t);o.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==n&&t){let o=new Event("click",{bubbles:r});e.indeterminate=C(n),t.call(e,!C(n)&&n),e.dispatchEvent(o)}},[u,n,r]);let f=o.useRef(!C(n)&&n);return(0,p.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=i?i:f.current,...a,tabIndex:-1,ref:l,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return"indeterminate"===e}function S(e){return C(e)?"indeterminate":e?"checked":"unchecked"}var T=w,P=b},89613:(e,t,n)=>{n.d(t,{Kq:()=>_,UC:()=>B,bL:()=>W,l9:()=>M});var o=n(12115),r=n(85185),i=n(6101),a=n(46081),l=n(19178),s=n(61285),c=n(35152),u=(n(34378),n(28905)),d=n(63655),p=n(99708),f=n(5845),m=n(2564),h=n(95155),[v,g]=(0,a.A)("Tooltip",[c.Bk]),w=(0,c.Bk)(),y="TooltipProvider",b="tooltip.open",[x,C]=v(y),S=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:r=300,disableHoverableContent:i=!1,children:a}=e,[l,s]=o.useState(!0),c=o.useRef(!1),u=o.useRef(0);return o.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(x,{scope:t,isOpenDelayed:l,delayDuration:n,onOpen:o.useCallback(()=>{window.clearTimeout(u.current),s(!1)},[]),onClose:o.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>s(!0),r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:o.useCallback(e=>{c.current=e},[]),disableHoverableContent:i,children:a})};S.displayName=y;var T="Tooltip",[P,E]=v(T),k=e=>{let{__scopeTooltip:t,children:n,open:r,defaultOpen:i=!1,onOpenChange:a,disableHoverableContent:l,delayDuration:u}=e,d=C(T,e.__scopeTooltip),p=w(t),[m,v]=o.useState(null),g=(0,s.B)(),y=o.useRef(0),x=null!=l?l:d.disableHoverableContent,S=null!=u?u:d.delayDuration,E=o.useRef(!1),[k=!1,R]=(0,f.i)({prop:r,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(b))):d.onClose(),null==a||a(e)}}),D=o.useMemo(()=>k?E.current?"delayed-open":"instant-open":"closed",[k]),Y=o.useCallback(()=>{window.clearTimeout(y.current),y.current=0,E.current=!1,R(!0)},[R]),N=o.useCallback(()=>{window.clearTimeout(y.current),y.current=0,R(!1)},[R]),L=o.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{E.current=!0,R(!0),y.current=0},S)},[S,R]);return o.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,h.jsx)(c.bL,{...p,children:(0,h.jsx)(P,{scope:t,contentId:g,open:k,stateAttribute:D,trigger:m,onTriggerChange:v,onTriggerEnter:o.useCallback(()=>{d.isOpenDelayed?L():Y()},[d.isOpenDelayed,L,Y]),onTriggerLeave:o.useCallback(()=>{x?N():(window.clearTimeout(y.current),y.current=0)},[N,x]),onOpen:Y,onClose:N,disableHoverableContent:x,children:n})})};k.displayName=T;var R="TooltipTrigger",D=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,l=E(R,n),s=C(R,n),u=w(n),p=o.useRef(null),f=(0,i.s)(t,p,l.onTriggerChange),m=o.useRef(!1),v=o.useRef(!1),g=o.useCallback(()=>m.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,h.jsx)(c.Mz,{asChild:!0,...u,children:(0,h.jsx)(d.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:f,onPointerMove:(0,r.m)(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),v.current=!0)}),onPointerLeave:(0,r.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),v.current=!1}),onPointerDown:(0,r.m)(e.onPointerDown,()=>{m.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,r.m)(e.onFocus,()=>{m.current||l.onOpen()}),onBlur:(0,r.m)(e.onBlur,l.onClose),onClick:(0,r.m)(e.onClick,l.onClose)})})});D.displayName=R;var[Y,N]=v("TooltipPortal",{forceMount:void 0}),L="TooltipContent",A=o.forwardRef((e,t)=>{let n=N(L,e.__scopeTooltip),{forceMount:o=n.forceMount,side:r="top",...i}=e,a=E(L,e.__scopeTooltip);return(0,h.jsx)(u.C,{present:o||a.open,children:a.disableHoverableContent?(0,h.jsx)(I,{side:r,...i,ref:t}):(0,h.jsx)(X,{side:r,...i,ref:t})})}),X=o.forwardRef((e,t)=>{let n=E(L,e.__scopeTooltip),r=C(L,e.__scopeTooltip),a=o.useRef(null),l=(0,i.s)(t,a),[s,c]=o.useState(null),{trigger:u,onClose:d}=n,p=a.current,{onPointerInTransitChange:f}=r,m=o.useCallback(()=>{c(null),f(!1)},[f]),v=o.useCallback((e,t)=>{let n=e.currentTarget,o={x:e.clientX,y:e.clientY},r=function(e,t){let n=Math.abs(t.top-e.y),o=Math.abs(t.bottom-e.y),r=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,o,r,i)){case i:return"left";case r:return"right";case n:return"top";case o:return"bottom";default:throw Error("unreachable")}}(o,n.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let o=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(o.y-n.y)>=(e.y-n.y)*(o.x-n.x))t.pop();else break}t.push(o)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let o=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(o.y-t.y)>=(e.y-t.y)*(o.x-t.x))n.pop();else break}n.push(o)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,o=[];switch(t){case"top":o.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":o.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":o.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":o.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return o}(o,r),...function(e){let{top:t,right:n,bottom:o,left:r}=e;return[{x:r,y:t},{x:n,y:t},{x:n,y:o},{x:r,y:o}]}(t.getBoundingClientRect())])),f(!0)},[f]);return o.useEffect(()=>()=>m(),[m]),o.useEffect(()=>{if(u&&p){let e=e=>v(e,p),t=e=>v(e,u);return u.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{u.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[u,p,v,m]),o.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},o=(null==u?void 0:u.contains(t))||(null==p?void 0:p.contains(t)),r=!function(e,t){let{x:n,y:o}=e,r=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,l=t[e].y,s=t[i].x,c=t[i].y;l>o!=c>o&&n<(s-a)*(o-l)/(c-l)+a&&(r=!r)}return r}(n,s);o?m():r&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[u,p,s,d,m]),(0,h.jsx)(I,{...e,ref:l})}),[j,O]=v(T,{isInside:!1}),I=o.forwardRef((e,t)=>{let{__scopeTooltip:n,children:r,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:s,...u}=e,d=E(L,n),f=w(n),{onClose:v}=d;return o.useEffect(()=>(document.addEventListener(b,v),()=>document.removeEventListener(b,v)),[v]),o.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&v()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,v]),(0,h.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:v,children:(0,h.jsxs)(c.UC,{"data-state":d.stateAttribute,...f,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(p.xV,{children:r}),(0,h.jsx)(j,{scope:n,isInside:!0,children:(0,h.jsx)(m.b,{id:d.contentId,role:"tooltip",children:i||r})})]})})});A.displayName=L;var z="TooltipArrow";o.forwardRef((e,t)=>{let{__scopeTooltip:n,...o}=e,r=w(n);return O(z,n).isInside?null:(0,h.jsx)(c.i3,{...r,...o,ref:t})}).displayName=z;var _=S,W=k,M=D,B=A}}]);