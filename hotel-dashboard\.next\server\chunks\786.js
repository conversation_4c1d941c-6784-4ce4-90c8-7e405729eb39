"use strict";exports.id=786,exports.ids=[786],exports.modules={46786:(e,t,l)=>{l.d(t,{default:()=>f});var a=l(60687),r=l(43210),o=l.n(r),n=l(55192),s=l(39390),i=l(68988),d=l(24934),c=l(63974);let h=({canvasSize:e,setCanvasSize:t,setGrid:l,grid:r,selectedColor:h,setSelectedColor:u,selectedShapeType:f,setSelectedShapeType:m,diameter:g,setDiameter:p,replaceColor:x,resetCanvas:w,exportCanvas:M})=>{let[v,j]=o().useState("#000000"),[y,b]=o().useState("#ff0000"),[S,C]=o().useState({width:0,height:0});return o().useEffect(()=>{r.cols>0&&r.rows>0&&C({width:e.width/r.cols,height:e.height/r.rows})},[e,r]),(0,a.jsxs)(n.Zp,{className:"w-full md:w-80",children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"工具栏"})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(s.J,{children:"画布尺寸 (px)"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.p,{type:"number",defaultValue:1920,onChange:l=>t({width:parseInt(l.target.value),height:e.height}),placeholder:"宽度"}),(0,a.jsx)(i.p,{type:"number",defaultValue:1080,onChange:l=>t({width:e.width,height:parseInt(l.target.value)}),placeholder:"高度"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(s.J,{children:"网格 (列x行)"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.p,{type:"number",value:r.cols,onChange:e=>l({...r,cols:parseInt(e.target.value)}),placeholder:"列"}),(0,a.jsx)(i.p,{type:"number",value:r.rows,onChange:e=>l({...r,rows:parseInt(e.target.value)}),placeholder:"行"})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(s.J,{children:"单元格尺寸"}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground p-2 border rounded-md",children:[(0,a.jsxs)("div",{children:["宽: ",S.width.toFixed(2)," px"]}),(0,a.jsxs)("div",{children:["高: ",S.height.toFixed(2)," px"]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(s.J,{children:"图形类型"}),(0,a.jsxs)(c.l6,{value:f,onValueChange:e=>m(e),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"选择图形"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"circle",children:"圆形"}),(0,a.jsx)(c.eb,{value:"square",children:"MTF"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(s.J,{children:"直径"}),(0,a.jsx)(i.p,{type:"number",value:g,onChange:e=>p(parseInt(e.target.value,10)||0)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(s.J,{children:"图形颜色"}),(0,a.jsx)(i.p,{type:"color",value:h,onChange:e=>u(e.target.value),className:"w-full"})]}),(0,a.jsxs)("div",{className:"space-y-2 border-t pt-4",children:[(0,a.jsx)(s.J,{children:"替换颜色"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.p,{placeholder:"旧颜色",type:"color",value:v,onChange:e=>j(e.target.value)}),(0,a.jsx)("span",{children:">"}),(0,a.jsx)(i.p,{placeholder:"新颜色",type:"color",value:y,onChange:e=>b(e.target.value)})]}),(0,a.jsx)(d.$,{onClick:()=>x(v,y),className:"w-full",children:"替换"})]}),(0,a.jsxs)("div",{className:"space-y-2 border-t pt-4",children:[(0,a.jsx)(s.J,{children:"画布操作"}),(0,a.jsx)(d.$,{onClick:w,className:"w-full",variant:"destructive",children:"重置画布"}),(0,a.jsx)(d.$,{onClick:()=>M("png"),className:"w-full",children:"导出为 PNG"}),(0,a.jsx)(d.$,{onClick:()=>M("jpeg"),className:"w-full",children:"导出为 JPEG"})]})]})]})},u=({width:e,height:t,grid:l,shapes:o,addShape:n,deleteShape:s,backgroundColor:i})=>{let d=(0,r.useRef)(null);(0,r.useEffect)(()=>{let a=d.current;if(!a)return;let r=a.getContext("2d");if(!r)return;let n=window.devicePixelRatio||1;a.width=e*n,a.height=t*n,a.style.width=`${e}px`,a.style.height=`${t}px`,r.scale(n,n),r.fillStyle=i,r.fillRect(0,0,e,t),c(r);let s=a=>{let r,o;let n=e/l.cols,s=t/l.rows,i=a.diameter/2,d=a.cell.col*n,c=a.cell.row*s;switch(a.alignment){case"center":r=d+n/2,o=c+s/2;break;case"topLeft":r=d+i,o=c+i;break;case"coordinates":a.coordinates?(r=d+a.coordinates.x,o=c+a.coordinates.y):(r=d+n/2,o=c+s/2)}return{cx:r,cy:o}},h=(e,t,l,a,r,o)=>{let n=document.createElement("canvas");n.width=a*o,n.height=a*o;let s=n.getContext("2d");if(!s)return;s.scale(o,o),s.fillStyle="#FFFFFF",s.fillRect(0,0,a,a),s.fillStyle=r,s.imageSmoothingEnabled=!1;let i=a/2,d=a/2;s.fillRect(Math.round(0),Math.round(d-1),Math.round(a),Math.round(2)),s.fillRect(Math.round(i-1),Math.round(0),Math.round(2),Math.round(a));for(let e=i-1-2;e>0;e-=4)s.fillRect(Math.round(e-2),Math.round(0),Math.round(2),Math.round(a/2-1));for(let e=d-1-2;e>0;e-=4)s.fillRect(Math.round(i+1),Math.round(e-2),Math.round(a/2-1),Math.round(2));for(let e=d+1+2;e<a;e+=4)s.fillRect(Math.round(0),Math.round(e),Math.round(a/2-1),Math.round(2));for(let e=i+1+2;e<a;e+=4)s.fillRect(Math.round(e),Math.round(d+1),Math.round(2),Math.round(a/2-1));e.imageSmoothingEnabled=!1,e.drawImage(n,t,l,a,a)},u=(e,t,l)=>{let{cx:a,cy:r}=s(t),o=t.diameter/2;if("circle"===t.type){e.save(),e.imageSmoothingEnabled=!1;let n=document.createElement("canvas"),s=Math.ceil(t.diameter*l)+2;n.width=s,n.height=s;let i=n.getContext("2d");if(i){i.imageSmoothingEnabled=!1,i.fillStyle=t.color;let o=t.diameter*l/2,d=s/2;for(let e=0;e<s;e++)for(let t=0;t<s;t++)Math.sqrt((e-d)**2+(t-d)**2)<=o&&i.fillRect(e,t,1,1);e.drawImage(n,a-t.diameter/2,r-t.diameter/2,t.diameter,t.diameter)}else e.fillStyle=t.color,e.beginPath(),e.arc(a,r,o,0,2*Math.PI),e.fill();e.restore()}else"square"===t.type&&h(e,a-o,r-o,t.diameter,t.color,l)};o.forEach(e=>u(r,e,n))},[e,t,l,o,i]);let c=a=>{a.strokeStyle="rgba(0, 0, 0, 0.5)",a.setLineDash([5,5]),a.lineWidth=1;let r=e/l.cols,o=t/l.rows;for(let e=1;e<l.cols;e++)a.beginPath(),a.moveTo(e*r,0),a.lineTo(e*r,t),a.stroke();for(let t=1;t<l.rows;t++)a.beginPath(),a.moveTo(0,t*o),a.lineTo(e,t*o),a.stroke();a.setLineDash([])},h=(a,r)=>{let n=e/l.cols,s=t/l.rows;for(let e=o.length-1;e>=0;e--){let t,l;let i=o[e],d=i.diameter/2,c=i.cell.col*n,h=i.cell.row*s;switch(i.alignment){case"center":t=c+n/2,l=h+s/2;break;case"topLeft":t=c+d,l=h+d;break;case"coordinates":i.coordinates?(t=c+i.coordinates.x,l=h+i.coordinates.y):(t=c+n/2,l=h+s/2)}if("circle"===i.type){if(Math.sqrt((a-t)**2+(r-l)**2)<=d)return i}else if("square"===i.type&&a>=t-d&&a<=t+d&&r>=l-d&&r<=l+d)return i}};return(0,a.jsx)("canvas",{ref:d,width:e,height:t,onMouseDown:a=>{let r=d.current;if(!r)return;let o=r.getBoundingClientRect(),i=a.clientX-o.left,c=a.clientY-o.top;if(2===a.button){a.preventDefault();let e=h(i,c);e&&s(e.id);return}let u=e/l.cols;n({row:Math.floor(c/(t/l.rows)),col:Math.floor(i/u)},"center")},onContextMenu:e=>e.preventDefault(),className:"border border-gray-400"})},f=()=>{let[e,t]=(0,r.useState)({width:1920,height:1080}),[l,o]=(0,r.useState)({rows:9,cols:17}),[n,s]=(0,r.useState)([]),[i,d]=(0,r.useState)("#000000"),[c,f]=(0,r.useState)("#FFFFFF"),[m,g]=(0,r.useState)("circle"),[p,x]=(0,r.useState)(50),w=async t=>{if("undefined"==typeof document)return;let a=document.createElement("canvas");a.width=e.width,a.height=e.height;let r=a.getContext("2d");if(!r){console.error("Could not get temporary canvas context for exporting.");return}r.fillStyle=c,r.fillRect(0,0,a.width,a.height);let o=e=>{let t,r;let o=a.width/l.cols,n=a.height/l.rows,s=e.diameter/2,i=e.cell.col*o,d=e.cell.row*n;switch(e.alignment){case"center":t=i+o/2,r=d+n/2;break;case"topLeft":t=i+s,r=d+s;break;case"coordinates":e.coordinates?(t=i+e.coordinates.x,r=d+e.coordinates.y):(t=i+o/2,r=d+n/2)}return{cx:t,cy:r}},s=(e,t,l,a,r)=>{let o=document.createElement("canvas");o.width=a,o.height=a;let n=o.getContext("2d");if(!n)return;n.fillStyle="#FFFFFF",n.fillRect(0,0,a,a),n.fillStyle=r,n.imageSmoothingEnabled=!1;let s=a/2,i=a/2;n.fillRect(Math.round(0),Math.round(i-1),Math.round(a),Math.round(2)),n.fillRect(Math.round(s-1),Math.round(0),Math.round(2),Math.round(a));for(let e=s-1-2;e>0;e-=4)n.fillRect(Math.round(e-2),Math.round(0),Math.round(2),Math.round(a/2-1));for(let e=i-1-2;e>0;e-=4)n.fillRect(Math.round(s+1),Math.round(e-2),Math.round(a/2-1),Math.round(2));for(let e=i+1+2;e<a;e+=4)n.fillRect(Math.round(0),Math.round(e),Math.round(a/2-1),Math.round(2));for(let e=s+1+2;e<a;e+=4)n.fillRect(Math.round(e),Math.round(i+1),Math.round(2),Math.round(a/2-1));e.imageSmoothingEnabled=!1,e.drawImage(o,t,l)};for(let e of n){let{cx:t,cy:l}=o(e),a=e.diameter/2;if("circle"===e.type){let o=document.createElement("canvas"),n=Math.ceil(e.diameter)+2;o.width=n,o.height=n;let s=o.getContext("2d");if(s){s.imageSmoothingEnabled=!1,s.fillStyle=e.color;let a=e.diameter/2,i=n/2;for(let e=0;e<n;e++)for(let t=0;t<n;t++)Math.sqrt((e-i)**2+(t-i)**2)<=a&&s.fillRect(e,t,1,1);r.imageSmoothingEnabled=!1,r.drawImage(o,t-e.diameter/2,l-e.diameter/2,e.diameter,e.diameter)}else r.fillStyle=e.color,r.beginPath(),r.arc(t,l,a,0,2*Math.PI),r.fill()}else"square"===e.type&&s(r,t-a,l-a,e.diameter,e.color)}let i=a.toDataURL(`image/${t}`),d=document.createElement("a");d.href=i,d.download=`drawing-board-${Date.now()}.${t}`,document.body.appendChild(d),d.click(),document.body.removeChild(d)};return(0,a.jsxs)("div",{className:"flex flex-col md:flex-row h-full gap-4",children:[(0,a.jsx)(h,{canvasSize:e,setCanvasSize:t,setGrid:o,grid:l,selectedColor:i,setSelectedColor:d,selectedShapeType:m,setSelectedShapeType:g,diameter:p,setDiameter:x,replaceColor:(e,t)=>{let l=e.toLowerCase(),a=t.toLowerCase();c.toLowerCase()===l&&f(a),s(e=>e.map(e=>e.color.toLowerCase()===l?{...e,color:a}:e))},resetCanvas:()=>{s([]),f("#FFFFFF")},exportCanvas:w}),(0,a.jsx)("div",{className:"flex-grow overflow-auto",children:(0,a.jsx)(u,{width:e.width,height:e.height,grid:l,shapes:n,addShape:(e,t,l)=>{s([...n,{id:Date.now(),type:m,cell:e,color:i,diameter:p,alignment:t,coordinates:l}])},deleteShape:e=>{s(n.filter(t=>t.id!==e))},backgroundColor:c})})]})}}};