"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/database-query/page.tsx":
/*!*************************************************!*\
  !*** ./app/(dashboard)/database-query/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseQueryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DatabaseQueryPage() {\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SELECT TOP 10 * FROM dbo.LOG_DATA;\");\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tables, setTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingTables, setIsLoadingTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 新增状态：结构化查询相关\n    const [selectedTable, setSelectedTable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [columns, setColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingColumns, setIsLoadingColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // CSV导出函数\n    const exportToCSV = (tableName, data)=>{\n        if (data.length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"No Data\",\n                description: \"No data to export.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const headers = Object.keys(data[0]);\n        // 创建CSV内容\n        const csvContent = [\n            // CSV头部\n            headers.join(','),\n            // CSV数据行\n            ...data.map((row)=>headers.map((header)=>{\n                    const value = String(row[header] || '');\n                    // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号\n                    if (value.includes(',') || value.includes('\"') || value.includes('\\n')) {\n                        return '\"'.concat(value.replace(/\"/g, '\"\"'), '\"');\n                    }\n                    return value;\n                }).join(','))\n        ].join('\\n');\n        // 创建Blob并下载\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat(tableName, \"_\").concat(new Date().toISOString().slice(0, 19).replace(/:/g, '-'), \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n            title: \"Export Successful\",\n            description: \"\".concat(tableName, \" exported to CSV successfully.\")\n        });\n    };\n    // 获取数据库表列表\n    const fetchTables = async ()=>{\n        setIsLoadingTables(true);\n        try {\n            // 尝试多种不同的查询方式\n            const queries = [\n                \"USE gina_db; SHOW TABLES;\",\n                \"SHOW TABLES FROM gina_db;\",\n                \"SELECT name FROM gina_db.sys.tables ORDER BY name;\",\n                \"SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;\"\n            ];\n            for(let i = 0; i < queries.length; i++){\n                try {\n                    console.log(\"Trying query \".concat(i + 1, \": \").concat(queries[i]));\n                    const response = await fetch('/api/database-query', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            query: queries[i]\n                        })\n                    });\n                    const response_data = await response.json();\n                    if (response.ok) {\n                        console.log('Query succeeded:', response_data);\n                        // 提取表名列表\n                        const tableData = response_data.data;\n                        if (tableData && Object.keys(tableData).length > 0) {\n                            const firstKey = Object.keys(tableData)[0];\n                            const tableRows = tableData[firstKey];\n                            if (Array.isArray(tableRows) && tableRows.length > 0) {\n                                // 尝试不同的列名\n                                const possibleColumns = [\n                                    'TABLE_NAME',\n                                    'name',\n                                    'Tables_in_gina_db'\n                                ];\n                                let tableNames = [];\n                                // 首先尝试已知的列名\n                                for (const colName of possibleColumns){\n                                    if (tableRows[0].hasOwnProperty(colName)) {\n                                        tableNames = tableRows.map((row)=>String(row[colName])).filter(Boolean);\n                                        break;\n                                    }\n                                }\n                                // 如果没有找到已知列名，使用第一列\n                                if (tableNames.length === 0) {\n                                    tableNames = tableRows.map((row)=>{\n                                        const values = Object.values(row);\n                                        return values.length > 0 ? String(values[0]) : null;\n                                    }).filter((name)=>Boolean(name));\n                                }\n                                if (tableNames.length > 0) {\n                                    setTables(tableNames);\n                                    console.log('Found tables:', tableNames);\n                                    return; // 成功获取表列表，退出函数\n                                }\n                            }\n                        }\n                    } else {\n                        console.log(\"Query \".concat(i + 1, \" failed:\"), response_data.error);\n                    }\n                } catch (err) {\n                    console.log(\"Query \".concat(i + 1, \" error:\"), err);\n                    continue; // 尝试下一个查询\n                }\n            }\n            // 所有查询都失败了\n            throw new Error('All table listing queries failed. Please check database connection and permissions.');\n        } catch (err) {\n            console.error('Error fetching tables:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"Error\",\n                description: \"Failed to fetch table list: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingTables(false);\n        }\n    };\n    // 获取表的列信息\n    const fetchColumns = async (tableName)=>{\n        if (!tableName) return;\n        setIsLoadingColumns(true);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: \"SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '\".concat(tableName, \"' ORDER BY ORDINAL_POSITION;\")\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                const tableData = response_data.data;\n                if (tableData && Object.keys(tableData).length > 0) {\n                    const firstKey = Object.keys(tableData)[0];\n                    const columnRows = tableData[firstKey];\n                    if (Array.isArray(columnRows)) {\n                        const columnInfo = columnRows.map((row)=>{\n                            const columnName = row.COLUMN_NAME || row.column_name || '';\n                            const dataType = (row.DATA_TYPE || row.data_type || '').toLowerCase();\n                            // 根据数据类型判断字段类型\n                            let fieldType = 'string';\n                            if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('numeric') || dataType.includes('double') || dataType.includes('real') || dataType.includes('money') || dataType.includes('smallmoney')) {\n                                fieldType = 'number';\n                            } else if (dataType.includes('date') || dataType.includes('time') || dataType.includes('timestamp')) {\n                                fieldType = 'date';\n                            } else if (dataType.includes('bit') || dataType.includes('boolean')) {\n                                fieldType = 'boolean';\n                            }\n                            return {\n                                name: columnName,\n                                type: dataType,\n                                dataType: fieldType\n                            };\n                        });\n                        // 去重处理，以防有重复的列名\n                        const uniqueColumns = columnInfo.filter((col, index, self)=>index === self.findIndex((c)=>c.name === col.name));\n                        setColumns(uniqueColumns);\n                        console.log('Found columns:', uniqueColumns);\n                    }\n                }\n            } else {\n                throw new Error(response_data.error || 'Failed to fetch columns.');\n            }\n        } catch (err) {\n            console.error('Error fetching columns:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"Error\",\n                description: \"Failed to fetch column information: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingColumns(false);\n        }\n    };\n    // 页面加载时获取表列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DatabaseQueryPage.useEffect\": ()=>{\n            fetchTables();\n        }\n    }[\"DatabaseQueryPage.useEffect\"], []);\n    // 处理表选择\n    const handleTableSelect = (tableName)=>{\n        setSelectedTable(tableName);\n        setQuery(\"SELECT * FROM \".concat(tableName, \" LIMIT 100;\"));\n        setFilters([]); // 清空之前的筛选条件\n        fetchColumns(tableName); // 获取列信息\n    };\n    // 构建结构化查询\n    const buildStructuredQuery = ()=>{\n        if (!selectedTable) return \"\";\n        let whereClause = \"\";\n        const activeFilters = filters.filter((f)=>f.enabled && f.value !== \"\" && f.value !== null);\n        if (activeFilters.length > 0) {\n            const conditions = activeFilters.map((filter)=>{\n                const column = filter.column;\n                const value = filter.value;\n                switch(filter.operator){\n                    case 'equals':\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    case 'contains':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"%'\");\n                    case 'starts_with':\n                        return \"\".concat(column, \" LIKE '\").concat(value, \"%'\");\n                    case 'ends_with':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"'\");\n                    case 'regex':\n                        return \"\".concat(column, \" REGEXP '\").concat(value, \"'\");\n                    case 'greater_than':\n                        return \"\".concat(column, \" > \").concat(value);\n                    case 'less_than':\n                        return \"\".concat(column, \" < \").concat(value);\n                    case 'between':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN \").concat(value[0], \" AND \").concat(value[1]);\n                        }\n                        return \"\".concat(column, \" = \").concat(value);\n                    case 'date_range':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN '\").concat(value[0], \"' AND '\").concat(value[1], \"'\");\n                        }\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    default:\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                }\n            });\n            whereClause = \" WHERE \" + conditions.join(\" AND \");\n        }\n        return \"SELECT * FROM \".concat(selectedTable).concat(whereClause, \" LIMIT 100;\");\n    };\n    // 执行结构化查询\n    const handleStructuredQuery = async ()=>{\n        const structuredQuery = buildStructuredQuery();\n        setQuery(structuredQuery);\n        // 直接执行查询，不依赖状态更新\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: structuredQuery\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                setResult(response_data.data);\n            } else {\n                throw new Error(response_data.error || 'Query failed.');\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"Error\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleQuery = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query\n                })\n            });\n            const response_data = await response.json();\n            if (!response.ok) {\n                throw new Error(response_data.error || 'An unknown error occurred.');\n            }\n            // Extract the actual data from the new API response format\n            const actualData = response_data.data || {};\n            // Check if the result is an empty object, which is a valid success case\n            if (Object.keys(actualData).length === 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"Query Successful\",\n                    description: \"The query ran successfully but returned no data.\"\n                });\n                setResult({});\n            } else {\n                setResult(actualData);\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"Error\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 添加筛选条件\n    const addFilter = ()=>{\n        if (columns.length === 0) return;\n        const newFilter = {\n            column: columns[0].name,\n            operator: 'equals',\n            value: '',\n            enabled: true\n        };\n        setFilters([\n            ...filters,\n            newFilter\n        ]);\n    };\n    // 更新筛选条件\n    const updateFilter = (index, updates)=>{\n        const newFilters = [\n            ...filters\n        ];\n        newFilters[index] = {\n            ...newFilters[index],\n            ...updates\n        };\n        setFilters(newFilters);\n    };\n    // 删除筛选条件\n    const removeFilter = (index)=>{\n        const newFilters = filters.filter((_, i)=>i !== index);\n        setFilters(newFilters);\n    };\n    const renderTable = (tableName, data)=>{\n        if (data.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Table '\",\n                    tableName,\n                    \"' has no rows.\"\n                ]\n            }, tableName, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 441,\n                columnNumber: 14\n            }, this);\n        }\n        const headers = Object.keys(data[0]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    tableName,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 font-normal\",\n                                        children: [\n                                            \"(\",\n                                            data.length,\n                                            \" rows)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>exportToCSV(tableName, data),\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Export CSV\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto max-h-[600px] border rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHeader, {\n                                        className: \"sticky top-0 bg-white z-10 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                            children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2\",\n                                                    style: {\n                                                        minWidth: '120px'\n                                                    },\n                                                    children: header\n                                                }, header, false, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableBody, {\n                                        children: data.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                                className: \"hover:bg-gray-50\",\n                                                children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"whitespace-nowrap px-4 py-2 text-sm border-b\",\n                                                        style: {\n                                                            minWidth: '120px'\n                                                        },\n                                                        title: String(row[header]),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-w-[200px] truncate\",\n                                                            children: String(row[header])\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, \"\".concat(rowIndex, \"-\").concat(header), false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, rowIndex, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t\",\n                            children: [\n                                \"Total: \",\n                                data.length,\n                                \" records\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, tableName, true, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n            lineNumber: 445,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"Database Query\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 513,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"数据库查询\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"快速表格选择 (gina_db)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                onValueChange: handleTableSelect,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                        className: \"w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                            placeholder: isLoadingTables ? \"正在加载表格...\" : \"选择要查询的表格\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                        children: tables.map((tableName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                value: tableName,\n                                                                children: tableName\n                                                            }, tableName, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: fetchTables,\n                                                disabled: isLoadingTables,\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(isLoadingTables ? 'animate-spin' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"刷新\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this),\n                                    tables.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"在 gina_db 数据库中找到 \",\n                                            tables.length,\n                                            \" 个表格\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Filter Conditions for \",\n                                                    selectedTable\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: addFilter,\n                                                disabled: isLoadingColumns || columns.length === 0,\n                                                children: \"Add Filter\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this),\n                                    isLoadingColumns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 mb-2\",\n                                        children: \"Loading columns...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 17\n                                    }, this),\n                                    filters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-4\",\n                                        children: filters.map((filter, index)=>{\n                                            var _columns_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                        checked: filter.enabled,\n                                                        onCheckedChange: (enabled)=>updateFilter(index, {\n                                                                enabled\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.column,\n                                                        onValueChange: (column)=>updateFilter(index, {\n                                                                column\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-40\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: columns.map((col, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: col.name,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                col.dataType === 'string' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 598,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 599,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'date' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 600,\n                                                                                    columnNumber: 61\n                                                                                }, this),\n                                                                                col.name,\n                                                                                \" (\",\n                                                                                col.type,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, \"\".concat(col.name, \"-\").concat(colIndex), false, {\n                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.operator,\n                                                        onValueChange: (operator)=>updateFilter(index, {\n                                                                operator\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: (()=>{\n                                                                    const selectedColumn = columns.find((col)=>col.name === filter.column);\n                                                                    if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'string') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"Equals\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"contains\",\n                                                                                children: \"Contains\"\n                                                                            }, \"contains\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"starts_with\",\n                                                                                children: \"Starts with\"\n                                                                            }, \"starts_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 622,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"ends_with\",\n                                                                                children: \"Ends with\"\n                                                                            }, \"ends_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 623,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"regex\",\n                                                                                children: \"Regex\"\n                                                                            }, \"regex\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'number') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"Equals\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 628,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"greater_than\",\n                                                                                children: \"Greater than\"\n                                                                            }, \"greater_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"less_than\",\n                                                                                children: \"Less than\"\n                                                                            }, \"less_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 630,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"between\",\n                                                                                children: \"Between\"\n                                                                            }, \"between\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 631,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'date') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"Equals\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"date_range\",\n                                                                                children: \"Date range\"\n                                                                            }, \"date_range\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 636,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    }\n                                                                    return [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: \"equals\",\n                                                                            children: \"Equals\"\n                                                                        }, \"equals\", false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ];\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    filter.operator === 'between' || filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 654,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[0] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[0]), \"yyyy-MM-dd\") : \"From date\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 650,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[0] ? new Date(filter.value[0]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            dateStr,\n                                                                                            currentValue[1]\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 662,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"to\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 681,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[1] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[1]), \"yyyy-MM-dd\") : \"To date\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 676,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[1] ? new Date(filter.value[1]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            currentValue[0],\n                                                                                            dateStr\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 689,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"From\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[0] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                e.target.value,\n                                                                                currentValue[1]\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"to\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"To\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[1] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                currentValue[0],\n                                                                                e.target.value\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        type: ((_columns_find = columns.find((col)=>col.name === filter.column)) === null || _columns_find === void 0 ? void 0 : _columns_find.dataType) === 'number' ? 'number' : 'text',\n                                                        placeholder: \"Value\",\n                                                        className: \"flex-1\",\n                                                        value: Array.isArray(filter.value) ? '' : filter.value,\n                                                        onChange: (e)=>updateFilter(index, {\n                                                                value: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeFilter(index),\n                                                        children: \"Remove\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: handleStructuredQuery,\n                                                disabled: isLoading,\n                                                className: \"flex-1\",\n                                                children: isLoading ? \"Querying...\" : \"Query with Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    setQuery(\"SELECT * FROM \".concat(selectedTable, \" LIMIT 100;\"));\n                                                    handleQuery();\n                                                },\n                                                disabled: isLoading,\n                                                children: \"Show All (100 rows)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        className: \"text-xs font-medium text-gray-600 mb-1 block\",\n                                        children: \"Generated Query:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"text-xs text-gray-800 font-mono\",\n                                        children: buildStructuredQuery() || \"SELECT * FROM \".concat(selectedTable, \" LIMIT 100;\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 772,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 514,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mt-4 bg-destructive/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-destructive\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 787,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 789,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 785,\n                columnNumber: 9\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-2\",\n                        children: \"Results\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 11\n                    }, this),\n                    Object.keys(result).length > 0 ? Object.entries(result).map((param)=>{\n                        let [tableName, data] = param;\n                        return renderTable(tableName, data);\n                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"The query executed successfully and returned no tables.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 801,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n        lineNumber: 512,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseQueryPage, \"37jn/e4rdN0iVNZCMb7xeH+UoSk=\");\n_c = DatabaseQueryPage;\nvar _c;\n$RefreshReg$(_c, \"DatabaseQueryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/database-query/page.tsx\n"));

/***/ })

});