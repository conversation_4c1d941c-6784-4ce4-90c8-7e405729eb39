"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/database-query/page.tsx":
/*!*************************************************!*\
  !*** ./app/(dashboard)/database-query/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseQueryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DatabaseQueryPage() {\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tables, setTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingTables, setIsLoadingTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 新增状态：结构化查询相关\n    const [selectedTable, setSelectedTable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [columns, setColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingColumns, setIsLoadingColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 分页相关状态\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalRows, setTotalRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [showPagination, setShowPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 导出进度相关状态\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exportProgress, setExportProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [exportTotal, setExportTotal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // CSV导出函数\n    const exportToCSV = (tableName, data)=>{\n        if (data.length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"无数据\",\n                description: \"没有数据可导出。\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const headers = Object.keys(data[0]);\n        // 创建CSV内容\n        const csvContent = [\n            // CSV头部\n            headers.join(','),\n            // CSV数据行\n            ...data.map((row)=>headers.map((header)=>{\n                    const value = String(row[header] || '');\n                    // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号\n                    if (value.includes(',') || value.includes('\"') || value.includes('\\n')) {\n                        return '\"'.concat(value.replace(/\"/g, '\"\"'), '\"');\n                    }\n                    return value;\n                }).join(','))\n        ].join('\\n');\n        // 创建Blob并下载\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat(tableName, \"_\").concat(new Date().toISOString().slice(0, 19).replace(/:/g, '-'), \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n            title: \"导出成功\",\n            description: \"\".concat(tableName, \" 已成功导出到CSV文件。\")\n        });\n    };\n    // 分批导出所有数据的函数\n    const exportAllDataToCSV = async (tableName)=>{\n        console.log('开始导出数据，表名:', tableName);\n        setIsExporting(true);\n        setExportProgress(0);\n        try {\n            // 1. 先获取总行数\n            const countQuery = buildStructuredQuery({\n                withCount: true\n            }) || \"SELECT COUNT(*) as total FROM \".concat(tableName, \";\");\n            console.log('获取导出总行数:', countQuery);\n            const countResponse = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: countQuery\n                })\n            });\n            const countData = await countResponse.json();\n            console.log('导出COUNT查询响应:', countData);\n            let totalCount = 0;\n            if (countResponse.ok && countData.data) {\n                console.log('导出COUNT查询数据结构:', countData.data);\n                // 使用与显示查询相同的解析逻辑\n                if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {\n                    // 处理 {tableName: [{total: 1500}]} 格式\n                    const tableNames = Object.keys(countData.data);\n                    if (tableNames.length > 0) {\n                        const firstTableData = countData.data[tableNames[0]];\n                        if (firstTableData && firstTableData.length > 0) {\n                            totalCount = parseInt(firstTableData[0].total) || 0;\n                        }\n                    }\n                } else if (Array.isArray(countData.data) && countData.data.length > 0) {\n                    // 处理直接数组格式\n                    totalCount = parseInt(countData.data[0].total) || 0;\n                }\n                console.log('导出解析的总行数:', totalCount);\n            } else {\n                console.error('导出COUNT查询失败:', countData);\n            }\n            if (totalCount === 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"无数据\",\n                    description: \"没有数据可导出。\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setExportTotal(totalCount);\n            // 2. 分批查询数据\n            const batchSize = 100;\n            const totalBatches = Math.ceil(totalCount / batchSize);\n            let allData = [];\n            let headers = [];\n            for(let batch = 0; batch < totalBatches; batch++){\n                const offset = batch * batchSize;\n                const batchQuery = buildStructuredQuery({\n                    page: batch + 1\n                }) || \"SELECT * FROM \".concat(tableName, \" LIMIT \").concat(batchSize, \" OFFSET \").concat(offset, \";\");\n                console.log(\"导出批次 \".concat(batch + 1, \"/\").concat(totalBatches, \":\"), batchQuery);\n                const batchResponse = await fetch('/api/database-query', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        query: batchQuery\n                    })\n                });\n                const batchData = await batchResponse.json();\n                if (batchResponse.ok && batchData.data) {\n                    // 假设数据结构是 { tableName: [...] }\n                    const tableData = Object.values(batchData.data)[0];\n                    if (tableData && Array.isArray(tableData)) {\n                        if (batch === 0) {\n                            headers = Object.keys(tableData[0] || {});\n                        }\n                        allData = allData.concat(tableData);\n                    }\n                }\n                // 更新进度\n                setExportProgress(batch + 1);\n                // 给UI一点时间更新\n                await new Promise((resolve)=>setTimeout(resolve, 10));\n            }\n            // 3. 生成CSV并下载\n            if (allData.length > 0) {\n                exportToCSV(tableName, allData);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"导出完成\",\n                    description: \"成功导出 \".concat(allData.length, \" 条记录\")\n                });\n            } else {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"导出失败\",\n                    description: \"未能获取到数据\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (err) {\n            console.error('导出错误:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"导出失败\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsExporting(false);\n            setExportProgress(0);\n            setExportTotal(0);\n        }\n    };\n    // 获取数据库表列表\n    const fetchTables = async ()=>{\n        setIsLoadingTables(true);\n        try {\n            // 尝试多种不同的查询方式\n            const queries = [\n                \"USE gina_db; SHOW TABLES;\",\n                \"SHOW TABLES FROM gina_db;\",\n                \"SELECT name FROM gina_db.sys.tables ORDER BY name;\",\n                \"SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;\"\n            ];\n            for(let i = 0; i < queries.length; i++){\n                try {\n                    console.log(\"Trying query \".concat(i + 1, \": \").concat(queries[i]));\n                    const response = await fetch('/api/database-query', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            query: queries[i]\n                        })\n                    });\n                    const response_data = await response.json();\n                    if (response.ok) {\n                        console.log('Query succeeded:', response_data);\n                        // 提取表名列表\n                        const tableData = response_data.data;\n                        if (tableData && Object.keys(tableData).length > 0) {\n                            const firstKey = Object.keys(tableData)[0];\n                            const tableRows = tableData[firstKey];\n                            if (Array.isArray(tableRows) && tableRows.length > 0) {\n                                // 尝试不同的列名\n                                const possibleColumns = [\n                                    'TABLE_NAME',\n                                    'name',\n                                    'Tables_in_gina_db'\n                                ];\n                                let tableNames = [];\n                                // 首先尝试已知的列名\n                                for (const colName of possibleColumns){\n                                    if (tableRows[0].hasOwnProperty(colName)) {\n                                        tableNames = tableRows.map((row)=>String(row[colName])).filter(Boolean);\n                                        break;\n                                    }\n                                }\n                                // 如果没有找到已知列名，使用第一列\n                                if (tableNames.length === 0) {\n                                    tableNames = tableRows.map((row)=>{\n                                        const values = Object.values(row);\n                                        return values.length > 0 ? String(values[0]) : null;\n                                    }).filter((name)=>Boolean(name));\n                                }\n                                if (tableNames.length > 0) {\n                                    setTables(tableNames);\n                                    console.log('Found tables:', tableNames);\n                                    return; // 成功获取表列表，退出函数\n                                }\n                            }\n                        }\n                    } else {\n                        console.log(\"Query \".concat(i + 1, \" failed:\"), response_data.error);\n                    }\n                } catch (err) {\n                    console.log(\"Query \".concat(i + 1, \" error:\"), err);\n                    continue; // 尝试下一个查询\n                }\n            }\n            // 所有查询都失败了\n            throw new Error('所有表列表查询都失败了。请检查数据库连接和权限。');\n        } catch (err) {\n            console.error('Error fetching tables:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: \"获取表列表失败: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingTables(false);\n        }\n    };\n    // 获取表的列信息\n    const fetchColumns = async (tableName)=>{\n        if (!tableName) return;\n        setIsLoadingColumns(true);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: \"SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '\".concat(tableName, \"' ORDER BY ORDINAL_POSITION;\")\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                const tableData = response_data.data;\n                if (tableData && Object.keys(tableData).length > 0) {\n                    const firstKey = Object.keys(tableData)[0];\n                    const columnRows = tableData[firstKey];\n                    if (Array.isArray(columnRows)) {\n                        const columnInfo = columnRows.map((row)=>{\n                            const columnName = row.COLUMN_NAME || row.column_name || '';\n                            const dataType = (row.DATA_TYPE || row.data_type || '').toLowerCase();\n                            // 根据数据类型判断字段类型\n                            let fieldType = 'string';\n                            if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('numeric') || dataType.includes('double') || dataType.includes('real') || dataType.includes('money') || dataType.includes('smallmoney')) {\n                                fieldType = 'number';\n                            } else if (dataType.includes('date') || dataType.includes('time') || dataType.includes('timestamp')) {\n                                fieldType = 'date';\n                            } else if (dataType.includes('bit') || dataType.includes('boolean')) {\n                                fieldType = 'boolean';\n                            }\n                            return {\n                                name: columnName,\n                                type: dataType,\n                                dataType: fieldType\n                            };\n                        });\n                        // 去重处理，以防有重复的列名\n                        const uniqueColumns = columnInfo.filter((col, index, self)=>index === self.findIndex((c)=>c.name === col.name));\n                        setColumns(uniqueColumns);\n                        console.log('Found columns:', uniqueColumns);\n                    }\n                }\n            } else {\n                throw new Error(response_data.error || '获取列信息失败。');\n            }\n        } catch (err) {\n            console.error('Error fetching columns:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: \"获取列信息失败: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingColumns(false);\n        }\n    };\n    // 页面加载时获取表列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DatabaseQueryPage.useEffect\": ()=>{\n            fetchTables();\n        }\n    }[\"DatabaseQueryPage.useEffect\"], []);\n    // 处理表选择\n    const handleTableSelect = (tableName)=>{\n        setSelectedTable(tableName);\n        setFilters([]); // 清空之前的筛选条件\n        // 重置分页状态\n        setCurrentPage(1);\n        setTotalRows(0);\n        setShowPagination(false);\n        setResult(null);\n        setError(null);\n        fetchColumns(tableName); // 获取列信息\n    };\n    // 构建结构化查询\n    const buildStructuredQuery = function() {\n        let { withCount = false, page = 1 } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        if (!selectedTable) return \"\";\n        let whereClause = \"\";\n        const activeFilters = filters.filter((f)=>f.enabled && f.value !== \"\" && f.value !== null);\n        if (activeFilters.length > 0) {\n            const conditions = activeFilters.map((filter)=>{\n                const column = filter.column;\n                const value = filter.value;\n                switch(filter.operator){\n                    case 'equals':\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    case 'contains':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"%'\");\n                    case 'starts_with':\n                        return \"\".concat(column, \" LIKE '\").concat(value, \"%'\");\n                    case 'ends_with':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"'\");\n                    case 'regex':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"%'\"); // 使用LIKE代替REGEXP以提高兼容性\n                    case 'greater_than':\n                        return \"\".concat(column, \" > \").concat(value);\n                    case 'less_than':\n                        return \"\".concat(column, \" < \").concat(value);\n                    case 'between':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN \").concat(value[0], \" AND \").concat(value[1]);\n                        }\n                        return \"\".concat(column, \" = \").concat(value);\n                    case 'date_range':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN '\").concat(value[0], \"' AND '\").concat(value[1], \"'\");\n                        }\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    default:\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                }\n            });\n            whereClause = \" WHERE \" + conditions.join(\" AND \");\n        }\n        if (withCount) {\n            return \"SELECT COUNT(*) as total FROM \".concat(selectedTable).concat(whereClause, \";\");\n        }\n        const offset = (page - 1) * pageSize;\n        return \"SELECT * FROM \".concat(selectedTable).concat(whereClause, \" LIMIT \").concat(pageSize, \" OFFSET \").concat(offset, \";\");\n    };\n    // 执行结构化查询\n    const handleStructuredQuery = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        const structuredQuery = buildStructuredQuery({\n            page\n        });\n        setQuery(structuredQuery);\n        // 直接执行查询，不依赖状态更新\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            // 首先执行COUNT查询获取总行数\n            const countQuery = buildStructuredQuery({\n                withCount: true\n            });\n            console.log('执行COUNT查询:', countQuery);\n            const countResponse = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: countQuery\n                })\n            });\n            const countData = await countResponse.json();\n            console.log('COUNT查询响应:', countData);\n            let totalCount = 0;\n            if (countResponse.ok && countData.data) {\n                console.log('COUNT查询数据结构:', countData.data);\n                // 检查数据结构，可能是 { tableName: [...] } 格式\n                if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {\n                    // 如果是对象格式，取第一个表的数据\n                    const firstTableData = Object.values(countData.data)[0];\n                    if (firstTableData && firstTableData.length > 0) {\n                        totalCount = parseInt(firstTableData[0].total) || 0;\n                    }\n                } else if (Array.isArray(countData.data) && countData.data.length > 0) {\n                    // 如果是数组格式\n                    totalCount = parseInt(countData.data[0].total) || 0;\n                }\n                console.log('解析的总行数:', totalCount);\n            }\n            setTotalRows(totalCount);\n            setShowPagination(true); // 始终显示分页信息，包括总数\n            // 然后执行数据查询\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: structuredQuery\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                setResult(response_data.data);\n                setCurrentPage(page);\n            } else {\n                throw new Error(response_data.error || '查询失败。');\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 添加筛选条件\n    const addFilter = ()=>{\n        if (columns.length === 0) return;\n        const newFilter = {\n            column: columns[0].name,\n            operator: 'equals',\n            value: '',\n            enabled: true\n        };\n        setFilters([\n            ...filters,\n            newFilter\n        ]);\n    };\n    // 更新筛选条件\n    const updateFilter = (index, updates)=>{\n        const newFilters = [\n            ...filters\n        ];\n        newFilters[index] = {\n            ...newFilters[index],\n            ...updates\n        };\n        setFilters(newFilters);\n    };\n    // 删除筛选条件\n    const removeFilter = (index)=>{\n        const newFilters = filters.filter((_, i)=>i !== index);\n        setFilters(newFilters);\n    };\n    const renderTable = (tableName, data)=>{\n        if (data.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"表 '\",\n                    tableName,\n                    \"' 没有数据行。\"\n                ]\n            }, tableName, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 581,\n                columnNumber: 14\n            }, this);\n        }\n        const headers = Object.keys(data[0]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    tableName,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 font-normal\",\n                                        children: [\n                                            \"(\",\n                                            data.length,\n                                            \" rows)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>exportAllDataToCSV(tableName),\n                                disabled: isExporting,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 15\n                                    }, this),\n                                    isExporting ? \"导出中...\" : \"导出全部数据\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0\",\n                    children: [\n                        isExporting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"导出进度\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                exportProgress,\n                                                \" / \",\n                                                Math.ceil(exportTotal / 100),\n                                                \" 批次\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: \"\".concat(exportTotal > 0 ? exportProgress / Math.ceil(exportTotal / 100) * 100 : 0, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: [\n                                        \"正在导出 \",\n                                        exportTotal,\n                                        \" 条记录...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto max-h-[600px] border rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHeader, {\n                                        className: \"sticky top-0 bg-white z-10 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                            children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2\",\n                                                    style: {\n                                                        minWidth: '120px'\n                                                    },\n                                                    children: header\n                                                }, header, false, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableBody, {\n                                        children: data.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                                className: \"hover:bg-gray-50\",\n                                                children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"whitespace-nowrap px-4 py-2 text-sm border-b\",\n                                                        style: {\n                                                            minWidth: '120px'\n                                                        },\n                                                        title: String(row[header]),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-w-[200px] truncate\",\n                                                            children: String(row[header])\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, \"\".concat(rowIndex, \"-\").concat(header), false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, rowIndex, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t\",\n                            children: showPagination ? \"显示第 \".concat((currentPage - 1) * pageSize + 1, \"-\").concat(Math.min(currentPage * pageSize, totalRows), \" 条，总计 \").concat(totalRows, \" 条记录\") : \"总计: \".concat(data.length, \" 条记录\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 606,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, tableName, true, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n            lineNumber: 585,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"数据库查询\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"快速表选择 (gina_db)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                onValueChange: handleTableSelect,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                        className: \"w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                            placeholder: isLoadingTables ? \"加载表中...\" : \"选择要查询的表\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                        children: tables.map((tableName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                value: tableName,\n                                                                children: tableName\n                                                            }, tableName, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: fetchTables,\n                                                disabled: isLoadingTables,\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(isLoadingTables ? 'animate-spin' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"刷新\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 13\n                                    }, this),\n                                    tables.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"在 gina_db 数据库中找到 \",\n                                            tables.length,\n                                            \" 个表\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 686,\n                                columnNumber: 11\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedTable,\n                                                    \" 的筛选条件\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: addFilter,\n                                                disabled: isLoadingColumns || columns.length === 0,\n                                                children: \"添加筛选\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, this),\n                                    isLoadingColumns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 mb-2\",\n                                        children: \"加载列中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 17\n                                    }, this),\n                                    filters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-4\",\n                                        children: filters.map((filter, index)=>{\n                                            var _columns_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                        checked: filter.enabled,\n                                                        onCheckedChange: (enabled)=>updateFilter(index, {\n                                                                enabled\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.column,\n                                                        onValueChange: (column)=>updateFilter(index, {\n                                                                column\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-40\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: columns.map((col, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: col.name,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                col.dataType === 'string' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 764,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 765,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'date' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 766,\n                                                                                    columnNumber: 61\n                                                                                }, this),\n                                                                                col.name,\n                                                                                \" (\",\n                                                                                col.type,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 763,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, \"\".concat(col.name, \"-\").concat(colIndex), false, {\n                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.operator,\n                                                        onValueChange: (operator)=>updateFilter(index, {\n                                                                operator\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: (()=>{\n                                                                    const selectedColumn = columns.find((col)=>col.name === filter.column);\n                                                                    if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'string') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"等于\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 786,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"contains\",\n                                                                                children: \"包含\"\n                                                                            }, \"contains\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 787,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"starts_with\",\n                                                                                children: \"开头是\"\n                                                                            }, \"starts_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 788,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"ends_with\",\n                                                                                children: \"结尾是\"\n                                                                            }, \"ends_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 789,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"regex\",\n                                                                                children: \"正则表达式\"\n                                                                            }, \"regex\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 790,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'number') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"等于\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 794,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"greater_than\",\n                                                                                children: \"大于\"\n                                                                            }, \"greater_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 795,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"less_than\",\n                                                                                children: \"小于\"\n                                                                            }, \"less_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"between\",\n                                                                                children: \"范围\"\n                                                                            }, \"between\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 797,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'date') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"等于\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 801,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"date_range\",\n                                                                                children: \"日期范围\"\n                                                                            }, \"date_range\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    }\n                                                                    return [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: \"equals\",\n                                                                            children: \"等于\"\n                                                                        }, \"equals\", false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 805,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ];\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    filter.operator === 'between' || filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 820,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[0] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[0]), \"yyyy-MM-dd\") : \"开始日期\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 816,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 815,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[0] ? new Date(filter.value[0]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            dateStr,\n                                                                                            currentValue[1]\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 828,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"to\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 847,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[1] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[1]), \"yyyy-MM-dd\") : \"结束日期\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 843,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 842,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[1] ? new Date(filter.value[1]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            currentValue[0],\n                                                                                            dateStr\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 855,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 854,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"最小值\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[0] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                e.target.value,\n                                                                                currentValue[1]\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"到\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 880,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"最大值\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[1] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                currentValue[0],\n                                                                                e.target.value\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        type: ((_columns_find = columns.find((col)=>col.name === filter.column)) === null || _columns_find === void 0 ? void 0 : _columns_find.dataType) === 'number' ? 'number' : 'text',\n                                                        placeholder: \"值\",\n                                                        className: \"flex-1\",\n                                                        value: Array.isArray(filter.value) ? '' : filter.value,\n                                                        onChange: (e)=>updateFilter(index, {\n                                                                value: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeFilter(index),\n                                                        children: \"删除\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: ()=>{\n                                                    // 重置到第一页并执行查询\n                                                    setCurrentPage(1);\n                                                    handleStructuredQuery(1);\n                                                },\n                                                disabled: isLoading,\n                                                className: \"flex-1\",\n                                                children: isLoading ? \"查询中...\" : \"使用筛选查询\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                onClick: async ()=>{\n                                                    // 显示前100行数据（重置筛选条件），但先获取总数并启用分页\n                                                    setFilters([]);\n                                                    setCurrentPage(1);\n                                                    setIsLoading(true);\n                                                    setError(null);\n                                                    setResult(null);\n                                                    try {\n                                                        // 先获取总行数\n                                                        const countQuery = \"SELECT COUNT(*) as total FROM \".concat(selectedTable, \";\");\n                                                        console.log('获取总行数:', countQuery);\n                                                        const countResponse = await fetch('/api/database-query', {\n                                                            method: 'POST',\n                                                            headers: {\n                                                                'Content-Type': 'application/json'\n                                                            },\n                                                            body: JSON.stringify({\n                                                                query: countQuery\n                                                            })\n                                                        });\n                                                        const countData = await countResponse.json();\n                                                        console.log('COUNT查询响应:', countData);\n                                                        console.log('COUNT查询响应状态:', countResponse.ok);\n                                                        let totalCount = 0;\n                                                        if (countResponse.ok && countData.data) {\n                                                            console.log('COUNT查询数据结构:', countData.data);\n                                                            console.log('数据类型:', typeof countData.data);\n                                                            console.log('是否为数组:', Array.isArray(countData.data));\n                                                            // 尝试多种数据结构解析\n                                                            try {\n                                                                // 方法1: 检查是否是 { tableName: [...] } 格式\n                                                                if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {\n                                                                    console.log('尝试对象格式解析...');\n                                                                    const tableNames = Object.keys(countData.data);\n                                                                    console.log('表名:', tableNames);\n                                                                    if (tableNames.length > 0) {\n                                                                        const firstTableData = countData.data[tableNames[0]];\n                                                                        console.log('第一个表的数据:', firstTableData);\n                                                                        if (firstTableData && firstTableData.length > 0) {\n                                                                            console.log('第一行数据:', firstTableData[0]);\n                                                                            totalCount = parseInt(firstTableData[0].total) || 0;\n                                                                        }\n                                                                    }\n                                                                } else if (Array.isArray(countData.data) && countData.data.length > 0) {\n                                                                    console.log('尝试数组格式解析...');\n                                                                    console.log('第一行数据:', countData.data[0]);\n                                                                    totalCount = parseInt(countData.data[0].total) || 0;\n                                                                }\n                                                                console.log('解析的总行数:', totalCount);\n                                                            } catch (parseError) {\n                                                                console.error('解析COUNT数据时出错:', parseError);\n                                                            }\n                                                        } else {\n                                                            console.error('COUNT查询失败:', countData);\n                                                        }\n                                                        setTotalRows(totalCount);\n                                                        setShowPagination(true); // 始终显示分页信息，包括总数\n                                                        // 然后查询前100行\n                                                        const dataQuery = \"SELECT * FROM \".concat(selectedTable, \" LIMIT 100 OFFSET 0;\");\n                                                        setQuery(dataQuery);\n                                                        const dataResponse = await fetch('/api/database-query', {\n                                                            method: 'POST',\n                                                            headers: {\n                                                                'Content-Type': 'application/json'\n                                                            },\n                                                            body: JSON.stringify({\n                                                                query: dataQuery\n                                                            })\n                                                        });\n                                                        const dataResult = await dataResponse.json();\n                                                        if (dataResponse.ok) {\n                                                            setResult(dataResult.data);\n                                                            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                                                                title: \"显示全部数据\",\n                                                                description: \"显示前100行，总计 \".concat(totalCount, \" 条记录\")\n                                                            });\n                                                        } else {\n                                                            throw new Error(dataResult.error || '查询失败。');\n                                                        }\n                                                    } catch (err) {\n                                                        setError(err.message);\n                                                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                                                            title: \"错误\",\n                                                            description: err.message,\n                                                            variant: \"destructive\"\n                                                        });\n                                                    } finally{\n                                                        setIsLoading(false);\n                                                    }\n                                                },\n                                                disabled: isLoading || !selectedTable,\n                                                size: \"sm\",\n                                                children: \"显示全部 (100行)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 924,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 13\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        className: \"text-xs font-medium text-gray-600 mb-1 block\",\n                                        children: \"生成的查询语句:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 1037,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"text-xs text-gray-800 font-mono\",\n                                        children: buildStructuredQuery({\n                                            page: currentPage\n                                        }) || \"SELECT * FROM \".concat(selectedTable, \" LIMIT \").concat(pageSize, \" OFFSET 0;\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 1040,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 684,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 680,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mt-4 bg-destructive/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-destructive\",\n                            children: \"错误\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 1051,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1050,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 1054,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1053,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 1049,\n                columnNumber: 9\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold\",\n                                children: \"查询结果\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1062,\n                                columnNumber: 13\n                            }, this),\n                            showPagination && totalRows > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"总计 \",\n                                    totalRows,\n                                    \" 条记录，第 \",\n                                    currentPage,\n                                    \" 页，共 \",\n                                    Math.ceil(totalRows / pageSize),\n                                    \" 页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1064,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1061,\n                        columnNumber: 11\n                    }, this),\n                    Object.keys(result).length > 0 ? Object.entries(result).map((param)=>{\n                        let [tableName, data] = param;\n                        return renderTable(tableName, data);\n                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"查询执行成功，但没有返回数据。\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1073,\n                        columnNumber: 13\n                    }, this),\n                    showPagination && totalRows > pageSize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>handleStructuredQuery(currentPage - 1),\n                                disabled: currentPage <= 1 || isLoading,\n                                children: \"上一页\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1079,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: Array.from({\n                                    length: Math.min(5, Math.ceil(totalRows / pageSize))\n                                }, (_, i)=>{\n                                    const pageNum = Math.max(1, currentPage - 2) + i;\n                                    if (pageNum > Math.ceil(totalRows / pageSize)) return null;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: pageNum === currentPage ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleStructuredQuery(pageNum),\n                                        disabled: isLoading,\n                                        children: pageNum\n                                    }, pageNum, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 1094,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1088,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>handleStructuredQuery(currentPage + 1),\n                                disabled: currentPage >= Math.ceil(totalRows / pageSize) || isLoading,\n                                children: \"下一页\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1107,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1078,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 1060,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n        lineNumber: 679,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseQueryPage, \"3NZ+6PaaI8y19+6KNXEsKo1lpxk=\");\n_c = DatabaseQueryPage;\nvar _c;\n$RefreshReg$(_c, \"DatabaseQueryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/database-query/page.tsx\n"));

/***/ })

});