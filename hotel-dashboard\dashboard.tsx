"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import {
  Bar<PERSON>hart,
  ChevronLeft,
  ChevronRight,
  Search,
  Plus,
  Edit,
  Trash,
  ChevronDown,
  Bell,
  Home,
  CalendarIcon,
  MessageSquare,
  Star,
  Award,
  CreditCard,
  Utensils,
  ShoppingBag,
  Truck,
  Clock,
  DollarSign,
  Filter,
  Download,
  Printer,
  MoreHorizontal,
  Menu,
  FileText, // Added for Log Analysis
Database, // Added for Surface Data Query
Brush, // Added for Drawing Board
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardFooter, CardDescription } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import {
  Bar,
  BarChart as RechartsBarChart,
  Line,
  LineChart as RechartsLineChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
Area as RechartsArea,
} from "recharts"
import SurfaceDataQueryPage from "@/app/(dashboard)/surface-data-query/page"; // Import the new page
import LogAnalysisPage from "@/app/(dashboard)/log-analysis/page"; // Import the new page
import DrawingBoardPage from "@/app/(dashboard)/drawing-board/page"; // Import the new page

// 数据库查询相关的接口定义
interface ColumnInfo {
  name: string;
  dataType: string;
}

interface FilterCondition {
  column: string;
  operator: string;
  value: string | string[];
  enabled: boolean;
}

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("stays")
  const [activeSection, setActiveSection] = useState("dashboard")
  const [isMobile, setIsMobile] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { toast } = useToast()

  // 数据库查询相关状态
  const [tables, setTables] = useState<string[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [columns, setColumns] = useState<ColumnInfo[]>([]);
  const [filters, setFilters] = useState<FilterCondition[]>([]);
  const [query, setQuery] = useState('');
  const [result, setResult] = useState<any>({});
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingTables, setIsLoadingTables] = useState(false);
  const [isLoadingColumns, setIsLoadingColumns] = useState(false);

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100);
  const [totalRows, setTotalRows] = useState(0);
  const [showPagination, setShowPagination] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  // 初始化时获取表列表
  useEffect(() => {
    fetchTables();
  }, [])

  // 数据库查询相关函数
  // 获取表列表
  const fetchTables = async () => {
    setIsLoadingTables(true);
    setError(null);

    try {
      // 尝试多种不同的查询方式
      const queries = [
        "USE gina_db; SHOW TABLES;",
        "SHOW TABLES FROM gina_db;",
        "SELECT name FROM gina_db.sys.tables ORDER BY name;",
        "SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;",
      ];

      for (let i = 0; i < queries.length; i++) {
        try {
          console.log(`Trying query ${i + 1}: ${queries[i]}`);

          const response = await fetch('/api/database-query', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: queries[i]
            }),
          });

          const response_data = await response.json();

          if (response.ok) {
            console.log('Query succeeded:', response_data);

            // Extract the actual data from the new API response format
            const actualData = response_data.data || {};

            if (Object.keys(actualData).length > 0) {
              // Get the first (and likely only) table result
              const firstTableName = Object.keys(actualData)[0];
              const tableData = actualData[firstTableName];

              if (Array.isArray(tableData) && tableData.length > 0) {
                // Extract table names from the result
                const tableNames = tableData.map((row: any) => {
                  // Try different possible column names for table names
                  return row.Tables_in_gina_db || row.name || row.TABLE_NAME || Object.values(row)[0];
                }).filter(Boolean);

                if (tableNames.length > 0) {
                  setTables(tableNames);
                  console.log('Tables loaded successfully:', tableNames);
                  break; // Exit the loop if successful
                }
              }
            }
          } else {
            console.log(`Query ${i + 1} failed:`, response_data.error);
          }
        } catch (queryError) {
          console.log(`Query ${i + 1} error:`, queryError);
        }
      }
    } catch (err: any) {
      setError(err.message);
      toast({
        title: "Error",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoadingTables(false);
    }
  };

  // 获取表的列信息
  const fetchColumns = async (tableName: string) => {
    setIsLoadingColumns(true);
    try {
      const response = await fetch('/api/database-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `SELECT COLUMN_NAME, DATA_TYPE FROM gina_db.INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '${tableName}' ORDER BY ORDINAL_POSITION;`
        }),
      });

      const response_data = await response.json();

      if (response.ok) {
        const actualData = response_data.data || {};
        if (Object.keys(actualData).length > 0) {
          const firstTableName = Object.keys(actualData)[0];
          const columnData = actualData[firstTableName];

          if (Array.isArray(columnData)) {
            const columnInfo: ColumnInfo[] = columnData.map((row: any) => ({
              name: row.COLUMN_NAME || row.column_name,
              dataType: getSimplifiedDataType(row.DATA_TYPE || row.data_type)
            }));
            setColumns(columnInfo);
          }
        }
      }
    } catch (err: any) {
      console.error('Error fetching columns:', err);
    } finally {
      setIsLoadingColumns(false);
    }
  };

  // 简化数据类型判断
  const getSimplifiedDataType = (sqlType: string): string => {
    if (!sqlType) return 'text';
    const type = sqlType.toLowerCase();

    // 数字类型
    if (type.includes('int') || type.includes('decimal') || type.includes('float') ||
        type.includes('double') || type.includes('real') || type.includes('money') ||
        type.includes('numeric') || type.includes('number')) {
      return 'number';
    }

    // 日期类型
    if (type.includes('date') || type.includes('time')) {
      return 'date';
    }

    // 默认为文本类型
    return 'text';
  };

  // 处理表选择
  const handleTableSelect = (tableName: string) => {
    setSelectedTable(tableName);
    setQuery(`SELECT * FROM ${tableName} LIMIT 100;`);
    setFilters([]); // 清空之前的筛选条件
    fetchColumns(tableName); // 获取列信息
  };

  // 构建WHERE条件的辅助函数
  const buildWhereConditions = (activeFilters: FilterCondition[]) => {
    return activeFilters.map(filter => {
      const column = filter.column;
      const value = filter.value;

      switch (filter.operator) {
        case 'equals':
          return `${column} = '${value}'`;
        case 'contains':
          return `${column} LIKE '%${value}%'`;
        case 'starts_with':
          return `${column} LIKE '${value}%'`;
        case 'ends_with':
          return `${column} LIKE '%${value}'`;
        case 'regex':
          return `${column} LIKE '${value}'`; // SQL Server doesn't have native regex, using LIKE
        case 'greater_than':
          return `${column} > ${value}`;
        case 'less_than':
          return `${column} < ${value}`;
        case 'between':
          if (Array.isArray(value) && value.length === 2) {
            return `${column} BETWEEN ${value[0]} AND ${value[1]}`;
          }
          return `${column} = '${value}'`;
        case 'date_range':
          if (Array.isArray(value) && value.length === 2) {
            return `${column} BETWEEN '${value[0]}' AND '${value[1]}'`;
          }
          return `${column} = '${value}'`;
        default:
          return `${column} = '${value}'`;
      }
    });
  };

  // 构建结构化查询（支持分页和总数查询）
  const buildStructuredQuery = (options: {
    withCount?: boolean;
    page?: number;
    size?: number;
  } = {}) => {
    if (!selectedTable) return "";

    const { withCount = false, page = 1, size = pageSize } = options;
    const activeFilters = filters.filter(f => f.enabled && f.value !== "" && f.value !== null);

    let whereClause = "";
    if (activeFilters.length > 0) {
      const conditions = buildWhereConditions(activeFilters);
      whereClause = " WHERE " + conditions.join(" AND ");
    }

    // 如果是获取总数的查询
    if (withCount) {
      return `SELECT COUNT(*) as total FROM ${selectedTable}${whereClause};`;
    }

    // 构建分页数据查询
    const offset = (page - 1) * size;
    return `SELECT * FROM ${selectedTable}${whereClause} LIMIT ${size} OFFSET ${offset};`;
  };

  // 执行结构化查询（支持分页）
  const handleStructuredQuery = async (page = 1, size = pageSize) => {
    console.log('=== MAIN PAGE handleStructuredQuery called ===', { page, size, selectedTable });
    if (!selectedTable) return;

    setIsLoading(true);
    setError(null);
    setCurrentPage(page);

    try {
      // 总是先获取总数（除非已经有了且不是第一页）
      if (page === 1 || totalRows === 0) {
        const countQuery = buildStructuredQuery({ withCount: true });
        console.log('Count query:', countQuery);

        const countResponse = await fetch('/api/database-query', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ query: countQuery }),
        });

        if (countResponse.ok) {
          const countData = await countResponse.json();
          console.log('Count query response:', countData);

          // 尝试不同的路径来获取总数
          let total = 0;
          if (countData.data) {
            console.log('Count data structure:', countData.data);

            // 如果data是对象，获取第一个表的数据
            const firstTableName = Object.keys(countData.data)[0];
            console.log('First table name:', firstTableName);

            if (firstTableName && Array.isArray(countData.data[firstTableName])) {
              const firstRow = countData.data[firstTableName][0];
              console.log('First row:', firstRow);
              console.log('First row keys:', Object.keys(firstRow || {}));

              // 尝试多种可能的字段名
              total = firstRow?.total || firstRow?.count || firstRow?.Total || firstRow?.COUNT || 0;

              // 如果还是0，尝试获取第一个数值字段
              if (total === 0 && firstRow) {
                const values = Object.values(firstRow);
                console.log('All values in first row:', values);
                const numericValue = values.find(v => typeof v === 'number' && v > 0);
                if (typeof numericValue === 'number') {
                  total = numericValue;
                }
              }
            }
          }

          console.log('Extracted total:', total);
          setTotalRows(total);
          setShowPagination(total > size);

          // 显示toast通知用户总数
          toast({
            title: "查询统计",
            description: `表 ${selectedTable} 共有 ${total} 条记录`,
          });
        } else {
          console.error('Count query failed:', countResponse.status);
        }
      }

      // 获取分页数据
      const dataQuery = buildStructuredQuery({ page, size });
      setQuery(dataQuery);

      const response = await fetch('/api/database-query', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: dataQuery }),
      });

      const response_data = await response.json();

      if (!response.ok) {
        throw new Error(response_data.error || 'An unknown error occurred.');
      }

      const actualData = response_data.data || {};
      setResult(actualData);

    } catch (err: any) {
      setError(err.message);
      toast({
        title: "查询错误",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 执行普通查询
  const handleQuery = async () => {
    if (!query.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/database-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      const response_data = await response.json();

      if (!response.ok) {
        throw new Error(response_data.error || 'An unknown error occurred.');
      }

      // Extract the actual data from the new API response format
      const actualData = response_data.data || {};

      // Check if the result is an empty object, which is a valid success case
      if (Object.keys(actualData).length === 0) {
        toast({
          title: "查询成功",
          description: "查询执行成功但没有返回数据。",
        });
        setResult({});
      } else {
        setResult(actualData);
      }

    } catch (err: any) {
      setError(err.message);
      toast({
        title: "错误",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 添加筛选条件
  const addFilter = () => {
    if (columns.length === 0) return;

    const newFilter: FilterCondition = {
      column: columns[0].name,
      operator: 'equals',
      value: '',
      enabled: true
    };

    setFilters([...filters, newFilter]);
  };

  // 更新筛选条件
  const updateFilter = (index: number, updates: Partial<FilterCondition>) => {
    const newFilters = [...filters];
    newFilters[index] = { ...newFilters[index], ...updates };
    setFilters(newFilters);
  };

  // 删除筛选条件
  const removeFilter = (index: number) => {
    const newFilters = filters.filter((_, i) => i !== index);
    setFilters(newFilters);
  };

  // CSV导出功能
  const exportToCSV = (tableName: string, data: any[]) => {
    if (!data || data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers.map(header => {
          const value = row[header];
          // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号
          if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${tableName}_export.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 渲染表格
  const renderTable = (tableName: string, data: any[]) => {
    if (!data || data.length === 0) return null;

    const headers = Object.keys(data[0]);
    return (
      <Card key={tableName} className="mt-4">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {tableName}
              <span className="text-sm text-gray-500 font-normal">
                ({data.length} rows)
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => exportToCSV(tableName, data)}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export CSV
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {/* 水平和垂直滚动容器 */}
          <div className="overflow-auto max-h-[600px] border rounded-md">
            <Table>
              <TableHeader className="sticky top-0 bg-white z-10 shadow-sm">
                <TableRow>
                  {headers.map((header) => (
                    <TableHead
                      key={header}
                      className="whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2"
                      style={{ minWidth: '120px' }}
                    >
                      {header}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((row, rowIndex) => (
                  <TableRow key={rowIndex} className="hover:bg-gray-50">
                    {headers.map((header) => (
                      <TableCell
                        key={`${rowIndex}-${header}`}
                        className="whitespace-nowrap px-4 py-2 text-sm border-b"
                        style={{ minWidth: '120px' }}
                        title={String(row[header])} // 鼠标悬停显示完整内容
                      >
                        <div className="max-w-[200px] truncate">
                          {String(row[header])}
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          {/* 显示总行数 */}
          <div className="px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t">
            Total: {data.length} records
          </div>
        </CardContent>
      </Card>
    );
  };

  // Sample data for charts
  const revenueData = [
    { name: "Sun", value: 8 },
    { name: "Mon", value: 10 },
    { name: "Tue", value: 12 },
    { name: "Wed", value: 11 },
    { name: "Thu", value: 9 },
    { name: "Fri", value: 11 },
    { name: "Sat", value: 12 },
  ]

  const guestsData = [
    { name: "Sun", value: 8000 },
    { name: "Mon", value: 10000 },
    { name: "Tue", value: 12000 },
    { name: "Wed", value: 9000 },
    { name: "Thu", value: 6000 },
    { name: "Fri", value: 8000 },
  ]

  const roomsData = [
    { name: "Sun", occupied: 15, booked: 10, available: 25 },
    { name: "Mon", occupied: 20, booked: 12, available: 18 },
    { name: "Tue", occupied: 18, booked: 15, available: 17 },
    { name: "Wed", occupied: 22, booked: 10, available: 18 },
    { name: "Thu", occupied: 20, booked: 15, available: 15 },
    { name: "Fri", occupied: 18, booked: 12, available: 20 },
    { name: "Sat", occupied: 15, booked: 10, available: 25 },
  ]

  const foodOrdersData = [
    { name: "Breakfast", value: 35 },
    { name: "Lunch", value: 45 },
    { name: "Dinner", value: 55 },
    { name: "Room Service", value: 25 },
  ]

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"]

  const bookingData = [
    {
      id: 1,
      name: "Ram Kailash",
      phone: "9905598912",
      bookingId: "SDK89635",
      nights: 2,
      roomType: "1 King Room",
      guests: 2,
      paid: "rsp.150",
      cost: "rsp.1500",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    {
      id: 2,
      name: "Samira Karki",
      phone: "9815394203",
      bookingId: "SDK89635",
      nights: 4,
      roomType: ["1 Queen", "1 King Room"],
      guests: 5,
      paid: "paid",
      cost: "rsp.5500",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    {
      id: 3,
      name: "Jeevan Rai",
      phone: "9865328452",
      bookingId: "SDK89635",
      nights: 1,
      roomType: ["1 Deluxe", "1 King Room"],
      guests: 3,
      paid: "rsp.150",
      cost: "rsp.2500",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    {
      id: 4,
      name: "Bindu Sharma",
      phone: "9845653124",
      bookingId: "SDK89635",
      nights: 3,
      roomType: ["1 Deluxe", "1 King Room"],
      guests: 2,
      paid: "rsp.150",
      cost: "rsp.3000",
      avatar: "/placeholder.svg?height=32&width=32",
    },
  ]

  const foodOrders = [
    {
      id: "FO-1234",
      guest: "Ram Kailash",
      room: "101",
      items: ["Chicken Curry", "Naan Bread", "Rice"],
      total: "rsp.850",
      status: "Delivered",
      time: "12:30 PM",
    },
    {
      id: "FO-1235",
      guest: "Samira Karki",
      room: "205",
      items: ["Vegetable Pasta", "Garlic Bread", "Tiramisu"],
      total: "rsp.1200",
      status: "Preparing",
      time: "1:15 PM",
    },
    {
      id: "FO-1236",
      guest: "Jeevan Rai",
      room: "310",
      items: ["Club Sandwich", "French Fries", "Coke"],
      total: "rsp.650",
      status: "On the way",
      time: "1:45 PM",
    },
  ]

  const invoices = [
    {
      id: "INV-2023-001",
      guest: "Ram Kailash",
      date: "26 Jul 2023",
      amount: "rsp.1500",
      status: "Paid",
      items: [
        { description: "Room Charges (2 nights)", amount: "rsp.1200" },
        { description: "Food & Beverages", amount: "rsp.300" },
      ],
    },
    {
      id: "INV-2023-002",
      guest: "Samira Karki",
      date: "25 Jul 2023",
      amount: "rsp.5500",
      status: "Paid",
      items: [
        { description: "Room Charges (4 nights)", amount: "rsp.4800" },
        { description: "Food & Beverages", amount: "rsp.700" },
      ],
    },
    {
      id: "INV-2023-003",
      guest: "Jeevan Rai",
      date: "24 Jul 2023",
      amount: "rsp.2500",
      status: "Pending",
      items: [
        { description: "Room Charges (1 night)", amount: "rsp.2000" },
        { description: "Food & Beverages", amount: "rsp.500" },
      ],
    },
  ]

  const calendarEvents = [
    { date: 2, guest: "Carl Larson II", nights: 2, guests: 2 },
    { date: 9, guest: "Mrs. Emmett Morar", nights: 2, guests: 2 },
    { date: 24, guest: "Marjorie Klocko", nights: 2, guests: 2 },
  ]

  const renderDashboard = () => (
    <>
      <div className="flex justify-end mb-4">
        <p className="text-sm text-gray-600">Wed // July 26th, 2023</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
        <Card>
          <CardContent className="p-4 flex items-center">
            <div className="bg-blue-50 p-3 rounded-full mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-500"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M5 12h14"></path>
                <path d="M12 5l7 7-7 7"></path>
              </svg>
            </div>
            <div>
              <p className="text-sm text-gray-500">
                Arrival <span className="text-xs">(This week)</span>
              </p>
              <div className="flex items-center">
                <h3 className="text-2xl font-bold mr-2">73</h3>
                <span className="text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded">+24%</span>
              </div>
              <p className="text-xs text-gray-500">Previous week: 35</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex items-center">
            <div className="bg-amber-50 p-3 rounded-full mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-amber-500"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M19 12H5"></path>
                <path d="M12 19l-7-7 7-7"></path>
              </svg>
            </div>
            <div>
              <p className="text-sm text-gray-500">
                Departure <span className="text-xs">(This week)</span>
              </p>
              <div className="flex items-center">
                <h3 className="text-2xl font-bold mr-2">35</h3>
                <span className="text-xs px-1.5 py-0.5 bg-red-100 text-red-600 rounded">-12%</span>
              </div>
              <p className="text-xs text-gray-500">Previous week: 97</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex items-center">
            <div className="bg-cyan-50 p-3 rounded-full mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-cyan-500"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </div>
            <div>
              <p className="text-sm text-gray-500">
                Booking <span className="text-xs">(This week)</span>
              </p>
              <div className="flex items-center">
                <h3 className="text-2xl font-bold mr-2">237</h3>
                <span className="text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded">+31%</span>
              </div>
              <p className="text-xs text-gray-500">Previous week: 187</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <p className="text-sm text-gray-500 mb-2">Today Activities</p>
            <div className="flex justify-between mb-2">
              <div className="text-center">
                <div className="bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1">
                  <span>5</span>
                </div>
                <p className="text-xs">
                  Room
                  <br />
                  Available
                </p>
              </div>
              <div className="text-center">
                <div className="bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1">
                  <span>10</span>
                </div>
                <p className="text-xs">
                  Room
                  <br />
                  Blocked
                </p>
              </div>
              <div className="text-center">
                <div className="bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1">
                  <span>15</span>
                </div>
                <p className="text-xs">Guest</p>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-xs text-gray-500">Total Revenue</p>
              <p className="text-lg font-bold">Rs.35k</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between p-4 pb-2">
            <CardTitle className="text-base font-medium">Revenue</CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 text-xs">
                  this week <ChevronDown className="ml-1 h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>This Month</DropdownMenuItem>
                <DropdownMenuItem>This Year</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="h-[200px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsBarChart data={revenueData} margin={{ top: 10, right: 10, left: -20, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" axisLine={false} tickLine={false} />
                  <YAxis hide={true} />
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-white p-2 border rounded shadow-sm">
                            <p className="text-xs">{`${payload[0].value} K`}</p>
                          </div>
                        )
                      }
                      return null
                    }}
                  />
                  <Bar dataKey="value" fill="#F59E0B" radius={[4, 4, 0, 0]} />
                </RechartsBarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between p-4 pb-2">
            <CardTitle className="text-base font-medium">Guests</CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 text-xs">
                  this week <ChevronDown className="ml-1 h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>This Month</DropdownMenuItem>
                <DropdownMenuItem>This Year</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="h-[200px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsLineChart data={guestsData} margin={{ top: 10, right: 10, left: -20, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" axisLine={false} tickLine={false} />
                  <YAxis hide={true} />
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-white p-2 border rounded shadow-sm">
                            <p className="text-xs">{`${payload[0].value}`}</p>
                          </div>
                        )
                      }
                      return null
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#3B82F6"
                    strokeWidth={2}
                    dot={{ r: 4, fill: "white", stroke: "#3B82F6", strokeWidth: 2 }}
                    activeDot={{ r: 6 }}
                    fill="url(#colorUv)"
                  />
                  <defs>
                    <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.2} />
                      <stop offset="95%" stopColor="#3B82F6" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <area type="monotone" dataKey="value" stroke="none" fill="url(#colorUv)" />
                </RechartsLineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between p-4 pb-2">
            <CardTitle className="text-base font-medium">Rooms</CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 text-xs">
                  this week <ChevronDown className="ml-1 h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>This Month</DropdownMenuItem>
                <DropdownMenuItem>This Year</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="text-xs mb-2">
              <div className="flex items-center justify-between">
                <p>Total 50 Rooms</p>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                    <span>Occupied</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    <span>Booked</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="h-2 w-2 rounded-full bg-amber-500"></span>
                    <span>Available</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="h-[180px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsBarChart data={roomsData} margin={{ top: 10, right: 10, left: -20, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" axisLine={false} tickLine={false} />
                  <YAxis hide={true} />
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-white p-2 border rounded shadow-sm">
                            <p className="text-xs">{`Occupied: ${payload[0].value}`}</p>
                            <p className="text-xs">{`Booked: ${payload[1].value}`}</p>
                            <p className="text-xs">{`Available: ${payload[2].value}`}</p>
                          </div>
                        )
                      }
                      return null
                    }}
                  />
                  <Bar dataKey="occupied" fill="#3B82F6" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="booked" fill="#10B981" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="available" fill="#F59E0B" radius={[4, 4, 0, 0]} />
                </RechartsBarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Booking Table */}
      <Card className="mb-6">
        <CardHeader className="p-4 pb-0">
          <CardTitle className="text-base font-medium">
            Todays Booking <span className="text-xs font-normal text-gray-500">(8 Guest today)</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <Tabs defaultValue="stays" className="w-full">
            <TabsList className="mb-4 border-b w-full justify-start rounded-none bg-transparent p-0">
              <TabsTrigger
                value="stays"
                className="rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                onClick={() => setActiveTab("stays")}
              >
                Stays
              </TabsTrigger>
              <TabsTrigger
                value="packages"
                className="rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                onClick={() => setActiveTab("packages")}
              >
                Packages
              </TabsTrigger>
              <TabsTrigger
                value="arrivals"
                className="rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                onClick={() => setActiveTab("arrivals")}
              >
                Arrivals
              </TabsTrigger>
              <TabsTrigger
                value="departure"
                className="rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                onClick={() => setActiveTab("departure")}
              >
                Departure
              </TabsTrigger>
            </TabsList>

            <div className="flex flex-col md:flex-row justify-between mb-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search guest by name or phone number or booking ID"
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full md:w-[400px] text-sm"
                />
              </div>
              <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Add Booking
              </Button>
            </div>

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="whitespace-nowrap">
                      <div className="flex items-center">
                        NAME <ChevronDown className="h-4 w-4 ml-1" />
                      </div>
                    </TableHead>
                    <TableHead className="whitespace-nowrap">BOOKING ID</TableHead>
                    <TableHead className="whitespace-nowrap">NIGHTS</TableHead>
                    <TableHead className="whitespace-nowrap">ROOM TYPE</TableHead>
                    <TableHead className="whitespace-nowrap">GUESTS</TableHead>
                    <TableHead className="whitespace-nowrap">PAID</TableHead>
                    <TableHead className="whitespace-nowrap">COST</TableHead>
                    <TableHead className="whitespace-nowrap">ACTION</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {bookingData.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <Avatar className="h-8 w-8 mr-3">
                            <AvatarImage src={booking.avatar} alt={booking.name} />
                            <AvatarFallback>
                              {booking.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{booking.name}</p>
                            <p className="text-xs text-gray-500">{booking.phone}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{booking.bookingId}</TableCell>
                      <TableCell>{booking.nights}</TableCell>
                      <TableCell>
                        {Array.isArray(booking.roomType) ? (
                          <div>
                            {booking.roomType.map((type, index) => (
                              <p key={index}>{type}</p>
                            ))}
                          </div>
                        ) : (
                          booking.roomType
                        )}
                      </TableCell>
                      <TableCell>{booking.guests} Guests</TableCell>
                      <TableCell>
                        {booking.paid === "paid" ? (
                          <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-xs">paid</span>
                        ) : (
                          booking.paid
                        )}
                      </TableCell>
                      <TableCell>{booking.cost}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            <div className="flex justify-end mt-4">
              <Button variant="link" className="text-blue-500 hover:text-blue-600">
                See other Bookings
              </Button>
            </div>
          </Tabs>
        </CardContent>
      </Card>

      {/* Calendar and Rating */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="p-4 pb-0">
            <CardTitle className="text-base font-medium">Calender</CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-4">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h3 className="text-sm font-medium">August 2023</h3>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <div className="grid grid-cols-7 gap-1 text-center text-xs">
              <div className="py-1 font-medium">SU</div>
              <div className="py-1 font-medium">MO</div>
              <div className="py-1 font-medium">TU</div>
              <div className="py-1 font-medium">WE</div>
              <div className="py-1 font-medium">TH</div>
              <div className="py-1 font-medium">FR</div>
              <div className="py-1 font-medium">SA</div>

              <div className="py-1 text-gray-400">31</div>
              <div className="py-1">1</div>
              <div className="py-1 relative">
                2
                <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></span>
              </div>
              <div className="py-1">3</div>
              <div className="py-1">4</div>
              <div className="py-1">5</div>
              <div className="py-1">6</div>

              <div className="py-1">7</div>
              <div className="py-1">8</div>
              <div className="py-1 relative">
                9
                <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></span>
              </div>
              <div className="py-1">10</div>
              <div className="py-1">11</div>
              <div className="py-1">12</div>
              <div className="py-1">13</div>

              <div className="py-1">14</div>
              <div className="py-1">15</div>
              <div className="py-1">16</div>
              <div className="py-1">17</div>
              <div className="py-1">18</div>
              <div className="py-1">19</div>
              <div className="py-1">20</div>

              <div className="py-1">21</div>
              <div className="py-1">22</div>
              <div className="py-1">23</div>
              <div className="py-1 relative">
                24
                <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></span>
              </div>
              <div className="py-1">25</div>
              <div className="py-1">26</div>
              <div className="py-1">27</div>

              <div className="py-1">28</div>
              <div className="py-1">29</div>
              <div className="py-1">30</div>
              <div className="py-1">31</div>
              <div className="py-1 text-gray-400">1</div>
              <div className="py-1 text-gray-400">2</div>
              <div className="py-1 text-gray-400">3</div>
            </div>

            <div className="mt-6 border rounded-md p-3">
              <h4 className="text-sm font-medium mb-2">August 02, 2023 Booking Lists</h4>
              <p className="text-xs text-gray-500 mb-3">(3 Bookings)</p>

              <div className="space-y-3">
                {calendarEvents.map((event, index) => (
                  <div key={index} className="flex items-center">
                    <Avatar className="h-8 w-8 mr-3">
                      <AvatarImage src="/placeholder.svg?height=32&width=32" alt={event.guest} />
                      <AvatarFallback>
                        {event.guest
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{event.guest}</p>
                      <p className="text-xs text-gray-500">
                        {event.nights} Nights | {event.guests} Guests
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between p-4 pb-0">
            <CardTitle className="text-base font-medium">Overall Rating</CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 text-xs">
                  This Week <ChevronDown className="ml-1 h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>This Month</DropdownMenuItem>
                <DropdownMenuItem>This Year</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>
          <CardContent className="p-4">
            <div className="flex justify-center mb-6">
              <div className="relative w-48 h-24">
                <svg viewBox="0 0 100 50" className="w-full h-full">
                  <path d="M 0 50 A 50 50 0 0 1 100 50" fill="none" stroke="#e5e7eb" strokeWidth="10" />
                  <path d="M 0 50 A 50 50 0 0 1 90 50" fill="none" stroke="#3b82f6" strokeWidth="10" />
                </svg>
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <div className="text-center">
                    <p className="text-sm font-medium">Rating</p>
                    <p className="text-2xl font-bold">4.5/5</p>
                    <span className="text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded">+31%</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Cleanliness</span>
                <div className="flex items-center gap-2">
                  <Progress value={90} className="h-2 w-32" />
                  <span className="text-sm">4.5</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Facilities</span>
                <div className="flex items-center gap-2">
                  <Progress value={90} className="h-2 w-32" />
                  <span className="text-sm">4.5</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Location</span>
                <div className="flex items-center gap-2">
                  <Progress value={50} className="h-2 w-32" />
                  <span className="text-sm">2.5</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Room Comfort</span>
                <div className="flex items-center gap-2">
                  <Progress value={50} className="h-2 w-32" />
                  <span className="text-sm">2.5</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Service</span>
                <div className="flex items-center gap-2">
                  <Progress value={76} className="h-2 w-32" />
                  <span className="text-sm">3.8</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Value for money</span>
                <div className="flex items-center gap-2">
                  <Progress value={76} className="h-2 w-32" />
                  <span className="text-sm">3.8</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 数据库查询界面 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            数据库查询
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* 表选择 */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Label className="text-sm font-medium">选择表:</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchTables}
                disabled={isLoadingTables}
              >
                {isLoadingTables ? "加载中..." : "刷新表列表"}
              </Button>
            </div>

            {tables.length > 0 && (
              <Select value={selectedTable} onValueChange={handleTableSelect}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="请选择一个表" />
                </SelectTrigger>
                <SelectContent>
                  {tables.map((table) => (
                    <SelectItem key={table} value={table}>
                      {table}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* 结构化查询界面 */}
          {selectedTable && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-4">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  {selectedTable} 的筛选条件
                </Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addFilter}
                  disabled={isLoadingColumns || columns.length === 0}
                >
                  添加筛选
                </Button>
              </div>

              {isLoadingColumns && (
                <div className="text-sm text-gray-500 mb-2">正在加载列信息...</div>
              )}

              {filters.length > 0 && (
                <div className="space-y-3 mb-4">
                  {filters.map((filter, index) => (
                    <div key={index} className="flex items-center gap-2 p-3 border rounded-lg">
                      <Switch
                        checked={filter.enabled}
                        onCheckedChange={(enabled) => updateFilter(index, { enabled })}
                      />

                      <Select
                        value={filter.column}
                        onValueChange={(column) => updateFilter(index, { column })}
                      >
                        <SelectTrigger className="w-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {columns.map((col) => (
                            <SelectItem key={col.name} value={col.name}>
                              {col.name} ({col.dataType})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Select
                        value={filter.operator}
                        onValueChange={(operator) => updateFilter(index, { operator })}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {columns.find(col => col.name === filter.column)?.dataType === 'number' ? (
                            <>
                              <SelectItem value="equals">等于</SelectItem>
                              <SelectItem value="greater_than">大于</SelectItem>
                              <SelectItem value="less_than">小于</SelectItem>
                              <SelectItem value="between">范围</SelectItem>
                            </>
                          ) : columns.find(col => col.name === filter.column)?.dataType === 'date' ? (
                            <>
                              <SelectItem value="equals">等于</SelectItem>
                              <SelectItem value="date_range">日期范围</SelectItem>
                            </>
                          ) : (
                            <>
                              <SelectItem value="equals">精确匹配</SelectItem>
                              <SelectItem value="contains">包含</SelectItem>
                              <SelectItem value="starts_with">开头匹配</SelectItem>
                              <SelectItem value="ends_with">结尾匹配</SelectItem>
                              <SelectItem value="regex">正则表达式</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>

                      {/* 值输入区域 */}
                      {columns.find(col => col.name === filter.column)?.dataType === 'date' && filter.operator === 'date_range' ? (
                        <div className="flex items-center gap-2">
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button variant="outline" className="w-28 justify-start text-left font-normal">
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {Array.isArray(filter.value) && filter.value[0] ? format(new Date(filter.value[0]), "MM/dd") : "开始日期"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={Array.isArray(filter.value) && filter.value[0] ? new Date(filter.value[0]) : undefined}
                                onSelect={(date) => {
                                  const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                                  updateFilter(index, { value: [date ? format(date, 'yyyy-MM-dd') : '', currentValue[1]] });
                                }}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <span className="text-sm text-gray-500">至</span>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button variant="outline" className="w-28 justify-start text-left font-normal">
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {Array.isArray(filter.value) && filter.value[1] ? format(new Date(filter.value[1]), "MM/dd") : "结束日期"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={Array.isArray(filter.value) && filter.value[1] ? new Date(filter.value[1]) : undefined}
                                onSelect={(date) => {
                                  const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                                  updateFilter(index, { value: [currentValue[0], date ? format(date, 'yyyy-MM-dd') : ''] });
                                }}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                      ) : columns.find(col => col.name === filter.column)?.dataType === 'number' && filter.operator === 'between' ? (
                        <div className="flex items-center gap-2">
                          <Input
                            type="number"
                            placeholder="最小值"
                            className="w-24"
                            value={Array.isArray(filter.value) ? filter.value[0] : ''}
                            onChange={(e) => {
                              const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                              updateFilter(index, { value: [e.target.value, currentValue[1]] });
                            }}
                          />
                          <span className="text-sm text-gray-500">至</span>
                          <Input
                            type="number"
                            placeholder="最大值"
                            className="w-24"
                            value={Array.isArray(filter.value) ? filter.value[1] : ''}
                            onChange={(e) => {
                              const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                              updateFilter(index, { value: [currentValue[0], e.target.value] });
                            }}
                          />
                        </div>
                      ) : (
                        <Input
                          type={columns.find(col => col.name === filter.column)?.dataType === 'number' ? 'number' : 'text'}
                          placeholder="值"
                          className="flex-1"
                          value={Array.isArray(filter.value) ? '' : filter.value}
                          onChange={(e) => updateFilter(index, { value: e.target.value })}
                        />
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFilter(index)}
                      >
                        删除
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex gap-2">
                <Button onClick={() => handleStructuredQuery(1)} disabled={isLoading} className="flex-1">
                  {isLoading ? "查询中..." : "使用筛选查询"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    // 清空筛选条件，然后执行分页查询
                    setFilters([]);
                    handleStructuredQuery(1);
                  }}
                  disabled={isLoading}
                >
                  显示全部 (分页)
                </Button>
                <Button
                  variant="outline"
                  onClick={async () => {
                    // 测试 COUNT 查询
                    const countQuery = `SELECT COUNT(*) as total FROM ${selectedTable};`;
                    console.log('Testing COUNT query:', countQuery);

                    const response = await fetch('/api/database-query', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ query: countQuery }),
                    });

                    const data = await response.json();
                    console.log('COUNT query result:', data);
                  }}
                  disabled={isLoading || !selectedTable}
                  size="sm"
                >
                  测试COUNT
                </Button>
              </div>
            </div>
          )}

          {/* 显示当前查询 */}
          {selectedTable && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <Label className="text-xs font-medium text-gray-600 mb-1 block">
                Generated Query:
              </Label>
              <code className="text-xs text-gray-800 font-mono">
                {buildStructuredQuery() || `SELECT * FROM ${selectedTable} LIMIT 100;`}
              </code>
            </div>
          )}
        </CardContent>
      </Card>

      {error && (
        <Card className="mt-4 bg-destructive/10">
          <CardHeader>
            <CardTitle className="text-destructive">错误</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <div className="mt-4">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-xl font-bold">查询结果</h2>
            {showPagination && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>总计 {totalRows} 条记录</span>
                <span>|</span>
                <span>第 {currentPage} 页，共 {Math.ceil(totalRows / pageSize)} 页</span>
              </div>
            )}
          </div>

          {Object.keys(result).length > 0 ? (
             Object.entries(result).map(([tableName, data]) => renderTable(tableName, data as any[]))
          ) : (
            <p>查询执行成功但没有返回数据。</p>
          )}

          {/* 分页控件 */}
          {showPagination && (
            <div className="mt-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label className="text-sm">每页显示:</Label>
                <Select value={pageSize.toString()} onValueChange={(value) => {
                  setPageSize(parseInt(value));
                  handleStructuredQuery(1, parseInt(value));
                }}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                    <SelectItem value="200">200</SelectItem>
                    <SelectItem value="500">500</SelectItem>
                    <SelectItem value="1000">1000</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStructuredQuery(1)}
                  disabled={currentPage === 1 || isLoading}
                >
                  首页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStructuredQuery(currentPage - 1)}
                  disabled={currentPage === 1 || isLoading}
                >
                  上一页
                </Button>
                <span className="text-sm px-2">
                  {currentPage} / {Math.ceil(totalRows / pageSize)}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStructuredQuery(currentPage + 1)}
                  disabled={currentPage >= Math.ceil(totalRows / pageSize) || isLoading}
                >
                  下一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStructuredQuery(Math.ceil(totalRows / pageSize))}
                  disabled={currentPage >= Math.ceil(totalRows / pageSize) || isLoading}
                >
                  末页
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </>
  )

  const renderBillingSystem = () => (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Billing System</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button size="sm" className="flex items-center gap-1">
            <Plus className="h-4 w-4" />
            New Invoice
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 flex items-center">
            <div className="bg-blue-50 p-3 rounded-full mr-4">
              <CreditCard className="h-6 w-6 text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Revenue</p>
              <h3 className="text-2xl font-bold">Rs.125,000</h3>
              <p className="text-xs text-green-600">+12% from last month</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 flex items-center">
            <div className="bg-green-50 p-3 rounded-full mr-4">
              <DollarSign className="h-6 w-6 text-green-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Paid Invoices</p>
              <h3 className="text-2xl font-bold">Rs.98,500</h3>
              <p className="text-xs text-green-600">78% of total</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 flex items-center">
            <div className="bg-amber-50 p-3 rounded-full mr-4">
              <Clock className="h-6 w-6 text-amber-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Pending Payments</p>
              <h3 className="text-2xl font-bold">Rs.26,500</h3>
              <p className="text-xs text-amber-600">22% of total</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-6">
        <CardHeader className="p-4 pb-0">
          <CardTitle className="text-base font-medium">Recent Invoices</CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice ID</TableHead>
                  <TableHead>Guest</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">{invoice.id}</TableCell>
                    <TableCell>{invoice.guest}</TableCell>
                    <TableCell>{invoice.date}</TableCell>
                    <TableCell>{invoice.amount}</TableCell>
                    <TableCell>
                      <Badge variant={invoice.status === "Paid" ? "success" : "warning"}>{invoice.status}</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              toast({
                                title: "Invoice details",
                                description: `Viewing details for invoice ${invoice.id}`,
                              })
                            }}
                          >
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              toast({
                                title: "Invoice printed",
                                description: `Invoice ${invoice.id} sent to printer`,
                              })
                            }}
                          >
                            <Printer className="h-4 w-4 mr-2" />
                            Print
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              toast({
                                title: "Invoice downloaded",
                                description: `Invoice ${invoice.id} downloaded as PDF`,
                              })
                            }}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              toast({
                                title: "Payment reminder sent",
                                description: `Reminder sent to ${invoice.guest}`,
                              })
                            }}
                          >
                            Send Reminder
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Dialog>
        <DialogTrigger asChild>
          <Button className="mb-6">Create New Invoice</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create New Invoice</DialogTitle>
            <DialogDescription>Create a new invoice for a guest. Fill in all the required details.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="guest" className="text-right">
                Guest
              </Label>
              <Select>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select guest" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ram">Ram Kailash</SelectItem>
                  <SelectItem value="samira">Samira Karki</SelectItem>
                  <SelectItem value="jeevan">Jeevan Rai</SelectItem>
                  <SelectItem value="bindu">Bindu Sharma</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="room" className="text-right">
                Room
              </Label>
              <Select>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select room" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="101">101 - King Room</SelectItem>
                  <SelectItem value="102">102 - Queen Room</SelectItem>
                  <SelectItem value="201">201 - Deluxe Room</SelectItem>
                  <SelectItem value="301">301 - Suite</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="date" className="text-right">
                Date
              </Label>
              <Input id="date" type="date" className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="amount" className="text-right">
                Amount
              </Label>
              <Input id="amount" type="number" placeholder="0.00" className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea id="description" placeholder="Invoice description" className="col-span-3" />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={() => {
                toast({
                  title: "Invoice created",
                  description: "New invoice has been created successfully",
                })
              }}
            >
              Create Invoice
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )

  const renderFoodDelivery = () => (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Food Delivery System</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button size="sm" className="flex items-center gap-1">
            <Plus className="h-4 w-4" />
            New Order
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 flex items-center">
            <div className="bg-blue-50 p-3 rounded-full mr-4">
              <Utensils className="h-6 w-6 text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Orders</p>
              <h3 className="text-2xl font-bold">42</h3>
              <p className="text-xs text-green-600">+8% from yesterday</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 flex items-center">
            <div className="bg-green-50 p-3 rounded-full mr-4">
              <ShoppingBag className="h-6 w-6 text-green-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Completed</p>
              <h3 className="text-2xl font-bold">35</h3>
              <p className="text-xs text-green-600">83% of total</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 flex items-center">
            <div className="bg-amber-50 p-3 rounded-full mr-4">
              <Truck className="h-6 w-6 text-amber-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">In Progress</p>
              <h3 className="text-2xl font-bold">7</h3>
              <p className="text-xs text-amber-600">17% of total</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="p-4 pb-0">
              <CardTitle className="text-base font-medium">Active Orders</CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Guest</TableHead>
                      <TableHead>Room</TableHead>
                      <TableHead>Items</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {foodOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.id}</TableCell>
                        <TableCell>{order.guest}</TableCell>
                        <TableCell>{order.room}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            {order.items.map((item, index) => (
                              <span key={index} className="text-xs">
                                {item}
                              </span>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>{order.total}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              order.status === "Delivered"
                                ? "success"
                                : order.status === "Preparing"
                                  ? "warning"
                                  : "default"
                            }
                          >
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => {
                                  toast({
                                    title: "Order details",
                                    description: `Viewing details for order ${order.id}`,
                                  })
                                }}
                              >
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  toast({
                                    title: "Order status updated",
                                    description: `Order ${order.id} marked as delivered`,
                                  })
                                }}
                              >
                                Mark as Delivered
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => {
                                  toast({
                                    title: "Order cancelled",
                                    description: `Order ${order.id} has been cancelled`,
                                    variant: "destructive",
                                  })
                                }}
                              >
                                Cancel Order
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
        <div>
          <Card>
            <CardHeader className="p-4 pb-0">
              <CardTitle className="text-base font-medium">Order Distribution</CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <div className="h-[250px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={foodOrdersData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {foodOrdersData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="flex flex-wrap justify-center gap-4 mt-4">
                {foodOrdersData.map((entry, index) => (
                  <div key={index} className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-1"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <span className="text-xs">
                      {entry.name}: {entry.value}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Dialog>
        <DialogTrigger asChild>
          <Button className="mb-6">Place New Order</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Place New Food Order</DialogTitle>
            <DialogDescription>Create a new food order for a guest. Select items from the menu.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="guest" className="text-right">
                Guest
              </Label>
              <Select>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select guest" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ram">Ram Kailash - Room 101</SelectItem>
                  <SelectItem value="samira">Samira Karki - Room 205</SelectItem>
                  <SelectItem value="jeevan">Jeevan Rai - Room 310</SelectItem>
                  <SelectItem value="bindu">Bindu Sharma - Room 402</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Menu Items</Label>
              <div className="col-span-3 border rounded-md p-3 space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="item1" />
                  <Label htmlFor="item1" className="flex justify-between w-full">
                    <span>Chicken Curry</span>
                    <span>Rs.450</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="item2" />
                  <Label htmlFor="item2" className="flex justify-between w-full">
                    <span>Vegetable Pasta</span>
                    <span>Rs.350</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="item3" />
                  <Label htmlFor="item3" className="flex justify-between w-full">
                    <span>Club Sandwich</span>
                    <span>Rs.250</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="item4" />
                  <Label htmlFor="item4" className="flex justify-between w-full">
                    <span>Naan Bread</span>
                    <span>Rs.50</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="item5" />
                  <Label htmlFor="item5" className="flex justify-between w-full">
                    <span>Rice</span>
                    <span>Rs.100</span>
                  </Label>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="special" className="text-right">
                Special Instructions
              </Label>
              <Textarea id="special" placeholder="Any special requests" className="col-span-3" />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={() => {
                toast({
                  title: "Order placed",
                  description: "Food order has been placed successfully",
                })
              }}
            >
              Place Order
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile Sidebar Toggle */}
      {isMobile && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-4 right-4 z-50 rounded-full h-12 w-12 shadow-lg bg-white"
          onClick={() => setSidebarOpen(true)}
        >
          <Menu className="h-6 w-6" />
        </Button>
      )}

      {/* Sidebar */}
      <div
        className={`${isMobile ? "fixed inset-0 z-50 transform transition-transform duration-300 ease-in-out" : "w-64"} ${isMobile && !sidebarOpen ? "-translate-x-full" : "translate-x-0"} bg-white border-r border-gray-200 flex flex-col`}
      >
        {isMobile && (
          <div className="flex justify-end p-4">
            <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(false)}>
              <ChevronLeft className="h-6 w-6" />
            </Button>
          </div>
        )}
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-2xl font-semibold text-purple-600">XREAL TEST</h1>
        </div>
        <div className="flex-1 py-4 overflow-y-auto">
          <nav className="space-y-1 px-2">
            <button
              onClick={() => setActiveSection("dashboard")}
              className={`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${activeSection === "dashboard" ? "text-blue-600 bg-blue-50 border-l-4 border-blue-600" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`}
            >
              <BarChart className="mr-3 h-5 w-5" />
              Dashboard
            </button>
            {/* New Log Analysis Link */}
            <button
              onClick={() => setActiveSection("log-analysis")}
              className={`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${activeSection === "log-analysis" ? "text-blue-600 bg-blue-50 border-l-4 border-blue-600" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`}
            >
              <FileText className="mr-3 h-5 w-5" />
              胶合日志分析
            </button>
            {/* End New Log Analysis Link */}
            <button
              onClick={() => setActiveSection("surface-data-query")}
              className={`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${activeSection === "surface-data-query" ? "text-blue-600 bg-blue-50 border-l-4 border-blue-600" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`}
            >
              <Database className="mr-3 h-5 w-5" />
              面形数据查询
            </button>
            {/* New Drawing Board Link */}
            <button
              onClick={() => setActiveSection("drawing-board")}
              className={`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${activeSection === "drawing-board" ? "text-blue-600 bg-blue-50 border-l-4 border-blue-600" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`}
            >
              <Brush className="mr-3 h-5 w-5" />
              画图板
            </button>
            {/* End New Drawing Board Link */}

            {/* End New Database Query Link */}
            <button
              onClick={() => setActiveSection("premium")}
              className={`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${activeSection === "premium" ? "text-blue-600 bg-blue-50 border-l-4 border-blue-600" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`}
            >
              <Award className="mr-3 h-5 w-5" />
              Try Premium Version
            </button>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 flex items-center justify-between px-4 py-4 md:px-6">
          <div className="flex items-center">
            {isMobile && (
              <Button variant="ghost" size="icon" className="mr-2" onClick={() => setSidebarOpen(true)}>
                <Menu className="h-5 w-5" />
              </Button>
            )}
            <h1 className="text-xl font-semibold text-gray-800">
              {activeSection === "dashboard"
                ? "Dashboard"
                : activeSection === "check-in-out"
                  ? "Check In-Out"
                  : activeSection === "rooms"
                    ? "Rooms"
                    : activeSection === "messages"
                      ? "Messages"
                      : activeSection === "customer-review"
                        ? "Customer Review"
                        : activeSection === "billing"
                          ? "Billing System"
                          : activeSection === "food-delivery"
                            ? "Food Delivery"
                            : activeSection === "log-analysis" // Added for Log Analysis
                              ? "胶合日志分析"
                              : activeSection === "surface-data-query" // Added for Surface Data Query
                                ? "面形数据查询"
                                : activeSection === "drawing-board" // Added for Drawing Board
                                  ? "画图板"
                                  : "Premium Version"}
            </h1>
          </div>
          <div className="flex items-center space-x-4">

            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/placeholder.svg?height=32&width=32" alt="User" />
                    <AvatarFallback>U</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuItem>Logout</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gray-50">
          {activeSection === "dashboard" && renderDashboard()}
          {activeSection === "billing" && renderBillingSystem()}
          {activeSection === "food-delivery" && renderFoodDelivery()}
          {activeSection === "log-analysis" && <LogAnalysisPage />} {/* Added for Log Analysis */}
          {activeSection === "surface-data-query" && <SurfaceDataQueryPage />} {/* Added for Surface Data Query */}
          {activeSection === "drawing-board" && <DrawingBoardPage />} {/* Added for Drawing Board */}

          {activeSection !== "dashboard" && activeSection !== "billing" && activeSection !== "food-delivery" && activeSection !== "log-analysis" && activeSection !== "surface-data-query" && activeSection !== "drawing-board" && (
            <div className="flex items-center justify-center h-full">
              <Card className="w-full max-w-md">
                <CardHeader>
                  <CardTitle>Coming Soon</CardTitle>
                  <CardDescription>This section is under development and will be available soon.</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>
                    The{" "}
                    {activeSection === "check-in-out"
                      ? "Check In-Out"
                      : activeSection === "rooms"
                        ? "Rooms"
                        : activeSection === "messages"
                          ? "Messages"
                          : activeSection === "customer-review"
                            ? "Customer Review"
                            : "Premium"}{" "}
                    module is currently being built. Please check back later.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button onClick={() => setActiveSection("dashboard")}>Return to Dashboard</Button>
                </CardFooter>
              </Card>
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
