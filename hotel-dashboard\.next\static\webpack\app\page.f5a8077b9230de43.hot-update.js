"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/database-query/page.tsx":
/*!*************************************************!*\
  !*** ./app/(dashboard)/database-query/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseQueryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CalendarIcon,Database,Download,Filter,Hash,RefreshCw,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DatabaseQueryPage() {\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SELECT TOP 10 * FROM dbo.LOG_DATA;\");\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tables, setTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingTables, setIsLoadingTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 新增状态：结构化查询相关\n    const [selectedTable, setSelectedTable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [columns, setColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingColumns, setIsLoadingColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 分页相关状态\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalRows, setTotalRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [showPagination, setShowPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 导出进度相关状态\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exportProgress, setExportProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [exportTotal, setExportTotal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // CSV导出函数\n    const exportToCSV = (tableName, data)=>{\n        if (data.length === 0) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"无数据\",\n                description: \"没有数据可导出。\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const headers = Object.keys(data[0]);\n        // 创建CSV内容\n        const csvContent = [\n            // CSV头部\n            headers.join(','),\n            // CSV数据行\n            ...data.map((row)=>headers.map((header)=>{\n                    const value = String(row[header] || '');\n                    // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号\n                    if (value.includes(',') || value.includes('\"') || value.includes('\\n')) {\n                        return '\"'.concat(value.replace(/\"/g, '\"\"'), '\"');\n                    }\n                    return value;\n                }).join(','))\n        ].join('\\n');\n        // 创建Blob并下载\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat(tableName, \"_\").concat(new Date().toISOString().slice(0, 19).replace(/:/g, '-'), \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n            title: \"导出成功\",\n            description: \"\".concat(tableName, \" 已成功导出到CSV文件。\")\n        });\n    };\n    // 分批导出所有数据的函数\n    const exportAllDataToCSV = async (tableName)=>{\n        console.log('开始导出数据，表名:', tableName);\n        setIsExporting(true);\n        setExportProgress(0);\n        try {\n            // 1. 先获取总行数\n            const countQuery = buildStructuredQuery({\n                withCount: true\n            }) || \"SELECT COUNT(*) as total FROM \".concat(tableName, \";\");\n            console.log('获取导出总行数:', countQuery);\n            const countResponse = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: countQuery\n                })\n            });\n            const countData = await countResponse.json();\n            console.log('导出COUNT查询响应:', countData);\n            let totalCount = 0;\n            if (countResponse.ok && countData.data) {\n                console.log('导出COUNT查询数据结构:', countData.data);\n                // 使用与显示查询相同的解析逻辑\n                if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {\n                    // 处理 {tableName: [{total: 1500}]} 格式\n                    const tableNames = Object.keys(countData.data);\n                    if (tableNames.length > 0) {\n                        const firstTableData = countData.data[tableNames[0]];\n                        if (firstTableData && firstTableData.length > 0) {\n                            totalCount = parseInt(firstTableData[0].total) || 0;\n                        }\n                    }\n                } else if (Array.isArray(countData.data) && countData.data.length > 0) {\n                    // 处理直接数组格式\n                    totalCount = parseInt(countData.data[0].total) || 0;\n                }\n                console.log('导出解析的总行数:', totalCount);\n            } else {\n                console.error('导出COUNT查询失败:', countData);\n            }\n            if (totalCount === 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"无数据\",\n                    description: \"没有数据可导出。\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setExportTotal(totalCount);\n            // 2. 分批查询数据\n            const batchSize = 100;\n            const totalBatches = Math.ceil(totalCount / batchSize);\n            let allData = [];\n            let headers = [];\n            for(let batch = 0; batch < totalBatches; batch++){\n                const offset = batch * batchSize;\n                const batchQuery = buildStructuredQuery({\n                    page: batch + 1\n                }) || \"SELECT * FROM \".concat(tableName, \" LIMIT \").concat(batchSize, \" OFFSET \").concat(offset, \";\");\n                console.log(\"导出批次 \".concat(batch + 1, \"/\").concat(totalBatches, \":\"), batchQuery);\n                const batchResponse = await fetch('/api/database-query', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        query: batchQuery\n                    })\n                });\n                const batchData = await batchResponse.json();\n                if (batchResponse.ok && batchData.data) {\n                    // 假设数据结构是 { tableName: [...] }\n                    const tableData = Object.values(batchData.data)[0];\n                    if (tableData && Array.isArray(tableData)) {\n                        if (batch === 0) {\n                            headers = Object.keys(tableData[0] || {});\n                        }\n                        allData = allData.concat(tableData);\n                    }\n                }\n                // 更新进度\n                setExportProgress(batch + 1);\n                // 给UI一点时间更新\n                await new Promise((resolve)=>setTimeout(resolve, 10));\n            }\n            // 3. 生成CSV并下载\n            if (allData.length > 0) {\n                exportToCSV(tableName, allData);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"导出完成\",\n                    description: \"成功导出 \".concat(allData.length, \" 条记录\")\n                });\n            } else {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: \"导出失败\",\n                    description: \"未能获取到数据\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (err) {\n            console.error('导出错误:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"导出失败\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsExporting(false);\n            setExportProgress(0);\n            setExportTotal(0);\n        }\n    };\n    // 获取数据库表列表\n    const fetchTables = async ()=>{\n        setIsLoadingTables(true);\n        try {\n            // 尝试多种不同的查询方式\n            const queries = [\n                \"USE gina_db; SHOW TABLES;\",\n                \"SHOW TABLES FROM gina_db;\",\n                \"SELECT name FROM gina_db.sys.tables ORDER BY name;\",\n                \"SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;\"\n            ];\n            for(let i = 0; i < queries.length; i++){\n                try {\n                    console.log(\"Trying query \".concat(i + 1, \": \").concat(queries[i]));\n                    const response = await fetch('/api/database-query', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            query: queries[i]\n                        })\n                    });\n                    const response_data = await response.json();\n                    if (response.ok) {\n                        console.log('Query succeeded:', response_data);\n                        // 提取表名列表\n                        const tableData = response_data.data;\n                        if (tableData && Object.keys(tableData).length > 0) {\n                            const firstKey = Object.keys(tableData)[0];\n                            const tableRows = tableData[firstKey];\n                            if (Array.isArray(tableRows) && tableRows.length > 0) {\n                                // 尝试不同的列名\n                                const possibleColumns = [\n                                    'TABLE_NAME',\n                                    'name',\n                                    'Tables_in_gina_db'\n                                ];\n                                let tableNames = [];\n                                // 首先尝试已知的列名\n                                for (const colName of possibleColumns){\n                                    if (tableRows[0].hasOwnProperty(colName)) {\n                                        tableNames = tableRows.map((row)=>String(row[colName])).filter(Boolean);\n                                        break;\n                                    }\n                                }\n                                // 如果没有找到已知列名，使用第一列\n                                if (tableNames.length === 0) {\n                                    tableNames = tableRows.map((row)=>{\n                                        const values = Object.values(row);\n                                        return values.length > 0 ? String(values[0]) : null;\n                                    }).filter((name)=>Boolean(name));\n                                }\n                                if (tableNames.length > 0) {\n                                    setTables(tableNames);\n                                    console.log('Found tables:', tableNames);\n                                    return; // 成功获取表列表，退出函数\n                                }\n                            }\n                        }\n                    } else {\n                        console.log(\"Query \".concat(i + 1, \" failed:\"), response_data.error);\n                    }\n                } catch (err) {\n                    console.log(\"Query \".concat(i + 1, \" error:\"), err);\n                    continue; // 尝试下一个查询\n                }\n            }\n            // 所有查询都失败了\n            throw new Error('所有表列表查询都失败了。请检查数据库连接和权限。');\n        } catch (err) {\n            console.error('Error fetching tables:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: \"获取表列表失败: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingTables(false);\n        }\n    };\n    // 获取表的列信息\n    const fetchColumns = async (tableName)=>{\n        if (!tableName) return;\n        setIsLoadingColumns(true);\n        try {\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: \"SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '\".concat(tableName, \"' ORDER BY ORDINAL_POSITION;\")\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                const tableData = response_data.data;\n                if (tableData && Object.keys(tableData).length > 0) {\n                    const firstKey = Object.keys(tableData)[0];\n                    const columnRows = tableData[firstKey];\n                    if (Array.isArray(columnRows)) {\n                        const columnInfo = columnRows.map((row)=>{\n                            const columnName = row.COLUMN_NAME || row.column_name || '';\n                            const dataType = (row.DATA_TYPE || row.data_type || '').toLowerCase();\n                            // 根据数据类型判断字段类型\n                            let fieldType = 'string';\n                            if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('numeric') || dataType.includes('double') || dataType.includes('real') || dataType.includes('money') || dataType.includes('smallmoney')) {\n                                fieldType = 'number';\n                            } else if (dataType.includes('date') || dataType.includes('time') || dataType.includes('timestamp')) {\n                                fieldType = 'date';\n                            } else if (dataType.includes('bit') || dataType.includes('boolean')) {\n                                fieldType = 'boolean';\n                            }\n                            return {\n                                name: columnName,\n                                type: dataType,\n                                dataType: fieldType\n                            };\n                        });\n                        // 去重处理，以防有重复的列名\n                        const uniqueColumns = columnInfo.filter((col, index, self)=>index === self.findIndex((c)=>c.name === col.name));\n                        setColumns(uniqueColumns);\n                        console.log('Found columns:', uniqueColumns);\n                    }\n                }\n            } else {\n                throw new Error(response_data.error || '获取列信息失败。');\n            }\n        } catch (err) {\n            console.error('Error fetching columns:', err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: \"获取列信息失败: \" + err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoadingColumns(false);\n        }\n    };\n    // 页面加载时获取表列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DatabaseQueryPage.useEffect\": ()=>{\n            fetchTables();\n        }\n    }[\"DatabaseQueryPage.useEffect\"], []);\n    // 处理表选择\n    const handleTableSelect = (tableName)=>{\n        setSelectedTable(tableName);\n        setQuery(\"SELECT * FROM \".concat(tableName, \" LIMIT 100;\"));\n        setFilters([]); // 清空之前的筛选条件\n        // 重置分页状态\n        setCurrentPage(1);\n        setTotalRows(0);\n        setShowPagination(false);\n        setResult(null);\n        setError(null);\n        fetchColumns(tableName); // 获取列信息\n    };\n    // 构建结构化查询\n    const buildStructuredQuery = function() {\n        let { withCount = false, page = 1 } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        if (!selectedTable) return \"\";\n        let whereClause = \"\";\n        const activeFilters = filters.filter((f)=>f.enabled && f.value !== \"\" && f.value !== null);\n        if (activeFilters.length > 0) {\n            const conditions = activeFilters.map((filter)=>{\n                const column = filter.column;\n                const value = filter.value;\n                switch(filter.operator){\n                    case 'equals':\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    case 'contains':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"%'\");\n                    case 'starts_with':\n                        return \"\".concat(column, \" LIKE '\").concat(value, \"%'\");\n                    case 'ends_with':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"'\");\n                    case 'regex':\n                        return \"\".concat(column, \" LIKE '%\").concat(value, \"%'\"); // 使用LIKE代替REGEXP以提高兼容性\n                    case 'greater_than':\n                        return \"\".concat(column, \" > \").concat(value);\n                    case 'less_than':\n                        return \"\".concat(column, \" < \").concat(value);\n                    case 'between':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN \").concat(value[0], \" AND \").concat(value[1]);\n                        }\n                        return \"\".concat(column, \" = \").concat(value);\n                    case 'date_range':\n                        if (Array.isArray(value) && value.length === 2) {\n                            return \"\".concat(column, \" BETWEEN '\").concat(value[0], \"' AND '\").concat(value[1], \"'\");\n                        }\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                    default:\n                        return \"\".concat(column, \" = '\").concat(value, \"'\");\n                }\n            });\n            whereClause = \" WHERE \" + conditions.join(\" AND \");\n        }\n        if (withCount) {\n            return \"SELECT COUNT(*) as total FROM \".concat(selectedTable).concat(whereClause, \";\");\n        }\n        const offset = (page - 1) * pageSize;\n        return \"SELECT * FROM \".concat(selectedTable).concat(whereClause, \" LIMIT \").concat(pageSize, \" OFFSET \").concat(offset, \";\");\n    };\n    // 执行结构化查询\n    const handleStructuredQuery = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        const structuredQuery = buildStructuredQuery({\n            page\n        });\n        setQuery(structuredQuery);\n        // 直接执行查询，不依赖状态更新\n        setIsLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            // 首先执行COUNT查询获取总行数\n            const countQuery = buildStructuredQuery({\n                withCount: true\n            });\n            console.log('执行COUNT查询:', countQuery);\n            const countResponse = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: countQuery\n                })\n            });\n            const countData = await countResponse.json();\n            console.log('COUNT查询响应:', countData);\n            let totalCount = 0;\n            if (countResponse.ok && countData.data) {\n                console.log('COUNT查询数据结构:', countData.data);\n                // 检查数据结构，可能是 { tableName: [...] } 格式\n                if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {\n                    // 如果是对象格式，取第一个表的数据\n                    const firstTableData = Object.values(countData.data)[0];\n                    if (firstTableData && firstTableData.length > 0) {\n                        totalCount = parseInt(firstTableData[0].total) || 0;\n                    }\n                } else if (Array.isArray(countData.data) && countData.data.length > 0) {\n                    // 如果是数组格式\n                    totalCount = parseInt(countData.data[0].total) || 0;\n                }\n                console.log('解析的总行数:', totalCount);\n            }\n            setTotalRows(totalCount);\n            setShowPagination(true); // 始终显示分页信息，包括总数\n            // 然后执行数据查询\n            const response = await fetch('/api/database-query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: structuredQuery\n                })\n            });\n            const response_data = await response.json();\n            if (response.ok) {\n                setResult(response_data.data);\n                setCurrentPage(page);\n            } else {\n                throw new Error(response_data.error || '查询失败。');\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"错误\",\n                description: err.message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 添加筛选条件\n    const addFilter = ()=>{\n        if (columns.length === 0) return;\n        const newFilter = {\n            column: columns[0].name,\n            operator: 'equals',\n            value: '',\n            enabled: true\n        };\n        setFilters([\n            ...filters,\n            newFilter\n        ]);\n    };\n    // 更新筛选条件\n    const updateFilter = (index, updates)=>{\n        const newFilters = [\n            ...filters\n        ];\n        newFilters[index] = {\n            ...newFilters[index],\n            ...updates\n        };\n        setFilters(newFilters);\n    };\n    // 删除筛选条件\n    const removeFilter = (index)=>{\n        const newFilters = filters.filter((_, i)=>i !== index);\n        setFilters(newFilters);\n    };\n    const renderTable = (tableName, data)=>{\n        if (data.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"表 '\",\n                    tableName,\n                    \"' 没有数据行。\"\n                ]\n            }, tableName, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 582,\n                columnNumber: 14\n            }, this);\n        }\n        const headers = Object.keys(data[0]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    tableName,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 font-normal\",\n                                        children: [\n                                            \"(\",\n                                            data.length,\n                                            \" rows)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>exportAllDataToCSV(tableName),\n                                disabled: isExporting,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, this),\n                                    isExporting ? \"导出中...\" : \"导出全部数据\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0\",\n                    children: [\n                        isExporting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"导出进度\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                exportProgress,\n                                                \" / \",\n                                                Math.ceil(exportTotal / 100),\n                                                \" 批次\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: \"\".concat(exportTotal > 0 ? exportProgress / Math.ceil(exportTotal / 100) * 100 : 0, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: [\n                                        \"正在导出 \",\n                                        exportTotal,\n                                        \" 条记录...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto max-h-[600px] border rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHeader, {\n                                        className: \"sticky top-0 bg-white z-10 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                            children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2\",\n                                                    style: {\n                                                        minWidth: '120px'\n                                                    },\n                                                    children: header\n                                                }, header, false, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableBody, {\n                                        children: data.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                                className: \"hover:bg-gray-50\",\n                                                children: headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"whitespace-nowrap px-4 py-2 text-sm border-b\",\n                                                        style: {\n                                                            minWidth: '120px'\n                                                        },\n                                                        title: String(row[header]),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-w-[200px] truncate\",\n                                                            children: String(row[header])\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, \"\".concat(rowIndex, \"-\").concat(header), false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, rowIndex, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t\",\n                            children: showPagination ? \"显示第 \".concat((currentPage - 1) * pageSize + 1, \"-\").concat(Math.min(currentPage * pageSize, totalRows), \" 条，总计 \").concat(totalRows, \" 条记录\") : \"总计: \".concat(data.length, \" 条记录\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, tableName, true, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n            lineNumber: 586,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"数据库查询\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 682,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"快速表选择 (gina_db)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                onValueChange: handleTableSelect,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                        className: \"w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                            placeholder: isLoadingTables ? \"加载表中...\" : \"选择要查询的表\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                        children: tables.map((tableName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                value: tableName,\n                                                                children: tableName\n                                                            }, tableName, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: fetchTables,\n                                                disabled: isLoadingTables,\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(isLoadingTables ? 'animate-spin' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"刷新\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 13\n                                    }, this),\n                                    tables.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"在 gina_db 数据库中找到 \",\n                                            tables.length,\n                                            \" 个表\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 687,\n                                columnNumber: 11\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedTable,\n                                                    \" 的筛选条件\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: addFilter,\n                                                disabled: isLoadingColumns || columns.length === 0,\n                                                children: \"添加筛选\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 15\n                                    }, this),\n                                    isLoadingColumns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 mb-2\",\n                                        children: \"加载列中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 17\n                                    }, this),\n                                    filters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-4\",\n                                        children: filters.map((filter, index)=>{\n                                            var _columns_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                        checked: filter.enabled,\n                                                        onCheckedChange: (enabled)=>updateFilter(index, {\n                                                                enabled\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.column,\n                                                        onValueChange: (column)=>updateFilter(index, {\n                                                                column\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-40\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: columns.map((col, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: col.name,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                col.dataType === 'string' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 765,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 766,\n                                                                                    columnNumber: 63\n                                                                                }, this),\n                                                                                col.dataType === 'date' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                    lineNumber: 767,\n                                                                                    columnNumber: 61\n                                                                                }, this),\n                                                                                col.name,\n                                                                                \" (\",\n                                                                                col.type,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 764,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, \"\".concat(col.name, \"-\").concat(colIndex), false, {\n                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                        lineNumber: 763,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: filter.operator,\n                                                        onValueChange: (operator)=>updateFilter(index, {\n                                                                operator\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                className: \"w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                children: (()=>{\n                                                                    const selectedColumn = columns.find((col)=>col.name === filter.column);\n                                                                    if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'string') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"等于\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 787,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"contains\",\n                                                                                children: \"包含\"\n                                                                            }, \"contains\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 788,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"starts_with\",\n                                                                                children: \"开头是\"\n                                                                            }, \"starts_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 789,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"ends_with\",\n                                                                                children: \"结尾是\"\n                                                                            }, \"ends_with\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 790,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"regex\",\n                                                                                children: \"正则表达式\"\n                                                                            }, \"regex\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 791,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'number') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"等于\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 795,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"greater_than\",\n                                                                                children: \"大于\"\n                                                                            }, \"greater_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"less_than\",\n                                                                                children: \"小于\"\n                                                                            }, \"less_than\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 797,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"between\",\n                                                                                children: \"范围\"\n                                                                            }, \"between\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 798,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    } else if ((selectedColumn === null || selectedColumn === void 0 ? void 0 : selectedColumn.dataType) === 'date') {\n                                                                        return [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"equals\",\n                                                                                children: \"等于\"\n                                                                            }, \"equals\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: \"date_range\",\n                                                                                children: \"日期范围\"\n                                                                            }, \"date_range\", false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 803,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ];\n                                                                    }\n                                                                    return [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: \"equals\",\n                                                                            children: \"等于\"\n                                                                        }, \"equals\", false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ];\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    filter.operator === 'between' || filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: filter.operator === 'date_range' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 821,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[0] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[0]), \"yyyy-MM-dd\") : \"开始日期\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 817,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 816,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[0] ? new Date(filter.value[0]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            dateStr,\n                                                                                            currentValue[1]\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 829,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 828,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"to\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                className: \"w-32 justify-start text-left font-normal\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CalendarIcon_Database_Download_Filter_Hash_RefreshCw_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                        lineNumber: 848,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    Array.isArray(filter.value) && filter.value[1] ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(filter.value[1]), \"yyyy-MM-dd\") : \"结束日期\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 844,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 843,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                                            className: \"w-auto p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                                                mode: \"single\",\n                                                                                selected: Array.isArray(filter.value) && filter.value[1] ? new Date(filter.value[1]) : undefined,\n                                                                                onSelect: (date)=>{\n                                                                                    const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                                        '',\n                                                                                        ''\n                                                                                    ];\n                                                                                    const dateStr = date ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, \"yyyy-MM-dd\") : '';\n                                                                                    updateFilter(index, {\n                                                                                        value: [\n                                                                                            currentValue[0],\n                                                                                            dateStr\n                                                                                        ]\n                                                                                    });\n                                                                                },\n                                                                                initialFocus: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                                lineNumber: 856,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                            lineNumber: 855,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"最小值\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[0] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                e.target.value,\n                                                                                currentValue[1]\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 871,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"到\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                    type: \"text\",\n                                                                    placeholder: \"最大值\",\n                                                                    className: \"w-24\",\n                                                                    value: Array.isArray(filter.value) ? filter.value[1] : '',\n                                                                    onChange: (e)=>{\n                                                                        const currentValue = Array.isArray(filter.value) ? filter.value : [\n                                                                            '',\n                                                                            ''\n                                                                        ];\n                                                                        updateFilter(index, {\n                                                                            value: [\n                                                                                currentValue[0],\n                                                                                e.target.value\n                                                                            ]\n                                                                        });\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 812,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        type: ((_columns_find = columns.find((col)=>col.name === filter.column)) === null || _columns_find === void 0 ? void 0 : _columns_find.dataType) === 'number' ? 'number' : 'text',\n                                                        placeholder: \"值\",\n                                                        className: \"flex-1\",\n                                                        value: Array.isArray(filter.value) ? '' : filter.value,\n                                                        onChange: (e)=>updateFilter(index, {\n                                                                value: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeFilter(index),\n                                                        children: \"删除\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: ()=>{\n                                                    // 重置到第一页并执行查询\n                                                    setCurrentPage(1);\n                                                    handleStructuredQuery(1);\n                                                },\n                                                disabled: isLoading,\n                                                className: \"flex-1\",\n                                                children: isLoading ? \"查询中...\" : \"使用筛选查询\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                onClick: async ()=>{\n                                                    // 显示前100行数据（重置筛选条件），但先获取总数并启用分页\n                                                    setFilters([]);\n                                                    setCurrentPage(1);\n                                                    setIsLoading(true);\n                                                    setError(null);\n                                                    setResult(null);\n                                                    try {\n                                                        // 先获取总行数\n                                                        const countQuery = \"SELECT COUNT(*) as total FROM \".concat(selectedTable, \";\");\n                                                        console.log('获取总行数:', countQuery);\n                                                        const countResponse = await fetch('/api/database-query', {\n                                                            method: 'POST',\n                                                            headers: {\n                                                                'Content-Type': 'application/json'\n                                                            },\n                                                            body: JSON.stringify({\n                                                                query: countQuery\n                                                            })\n                                                        });\n                                                        const countData = await countResponse.json();\n                                                        console.log('COUNT查询响应:', countData);\n                                                        console.log('COUNT查询响应状态:', countResponse.ok);\n                                                        let totalCount = 0;\n                                                        if (countResponse.ok && countData.data) {\n                                                            console.log('COUNT查询数据结构:', countData.data);\n                                                            console.log('数据类型:', typeof countData.data);\n                                                            console.log('是否为数组:', Array.isArray(countData.data));\n                                                            // 尝试多种数据结构解析\n                                                            try {\n                                                                // 方法1: 检查是否是 { tableName: [...] } 格式\n                                                                if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {\n                                                                    console.log('尝试对象格式解析...');\n                                                                    const tableNames = Object.keys(countData.data);\n                                                                    console.log('表名:', tableNames);\n                                                                    if (tableNames.length > 0) {\n                                                                        const firstTableData = countData.data[tableNames[0]];\n                                                                        console.log('第一个表的数据:', firstTableData);\n                                                                        if (firstTableData && firstTableData.length > 0) {\n                                                                            console.log('第一行数据:', firstTableData[0]);\n                                                                            totalCount = parseInt(firstTableData[0].total) || 0;\n                                                                        }\n                                                                    }\n                                                                } else if (Array.isArray(countData.data) && countData.data.length > 0) {\n                                                                    console.log('尝试数组格式解析...');\n                                                                    console.log('第一行数据:', countData.data[0]);\n                                                                    totalCount = parseInt(countData.data[0].total) || 0;\n                                                                }\n                                                                console.log('解析的总行数:', totalCount);\n                                                            } catch (parseError) {\n                                                                console.error('解析COUNT数据时出错:', parseError);\n                                                            }\n                                                        } else {\n                                                            console.error('COUNT查询失败:', countData);\n                                                        }\n                                                        setTotalRows(totalCount);\n                                                        setShowPagination(true); // 始终显示分页信息，包括总数\n                                                        // 然后查询前100行\n                                                        const dataQuery = \"SELECT * FROM \".concat(selectedTable, \" LIMIT 100 OFFSET 0;\");\n                                                        setQuery(dataQuery);\n                                                        const dataResponse = await fetch('/api/database-query', {\n                                                            method: 'POST',\n                                                            headers: {\n                                                                'Content-Type': 'application/json'\n                                                            },\n                                                            body: JSON.stringify({\n                                                                query: dataQuery\n                                                            })\n                                                        });\n                                                        const dataResult = await dataResponse.json();\n                                                        if (dataResponse.ok) {\n                                                            setResult(dataResult.data);\n                                                            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                                                                title: \"显示全部数据\",\n                                                                description: \"显示前100行，总计 \".concat(totalCount, \" 条记录\")\n                                                            });\n                                                        } else {\n                                                            throw new Error(dataResult.error || '查询失败。');\n                                                        }\n                                                    } catch (err) {\n                                                        setError(err.message);\n                                                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                                                            title: \"错误\",\n                                                            description: err.message,\n                                                            variant: \"destructive\"\n                                                        });\n                                                    } finally{\n                                                        setIsLoading(false);\n                                                    }\n                                                },\n                                                disabled: isLoading || !selectedTable,\n                                                size: \"sm\",\n                                                children: \"显示全部 (100行)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 917,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 13\n                            }, this),\n                            selectedTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        className: \"text-xs font-medium text-gray-600 mb-1 block\",\n                                        children: \"生成的查询语句:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 1038,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"text-xs text-gray-800 font-mono\",\n                                        children: buildStructuredQuery({\n                                            page: currentPage\n                                        }) || \"SELECT * FROM \".concat(selectedTable, \" LIMIT \").concat(pageSize, \" OFFSET 0;\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1037,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mt-4 bg-destructive/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-destructive\",\n                            children: \"错误\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 1052,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1051,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                            lineNumber: 1055,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1054,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 1050,\n                columnNumber: 9\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold\",\n                                children: \"查询结果\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 13\n                            }, this),\n                            showPagination && totalRows > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"总计 \",\n                                    totalRows,\n                                    \" 条记录，第 \",\n                                    currentPage,\n                                    \" 页，共 \",\n                                    Math.ceil(totalRows / pageSize),\n                                    \" 页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1065,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1062,\n                        columnNumber: 11\n                    }, this),\n                    Object.keys(result).length > 0 ? Object.entries(result).map((param)=>{\n                        let [tableName, data] = param;\n                        return renderTable(tableName, data);\n                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"查询执行成功，但没有返回数据。\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1074,\n                        columnNumber: 13\n                    }, this),\n                    showPagination && totalRows > pageSize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>handleStructuredQuery(currentPage - 1),\n                                disabled: currentPage <= 1 || isLoading,\n                                children: \"上一页\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1080,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: Array.from({\n                                    length: Math.min(5, Math.ceil(totalRows / pageSize))\n                                }, (_, i)=>{\n                                    const pageNum = Math.max(1, currentPage - 2) + i;\n                                    if (pageNum > Math.ceil(totalRows / pageSize)) return null;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: pageNum === currentPage ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleStructuredQuery(pageNum),\n                                        disabled: isLoading,\n                                        children: pageNum\n                                    }, pageNum, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                        lineNumber: 1095,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1089,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>handleStructuredQuery(currentPage + 1),\n                                disabled: currentPage >= Math.ceil(totalRows / pageSize) || isLoading,\n                                children: \"下一页\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                                lineNumber: 1108,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                        lineNumber: 1079,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n                lineNumber: 1061,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\database-query\\\\page.tsx\",\n        lineNumber: 680,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseQueryPage, \"WM7d+N+P+Ku0iGLIs3hjlOjWIUM=\");\n_c = DatabaseQueryPage;\nvar _c;\n$RefreshReg$(_c, \"DatabaseQueryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/database-query/page.tsx\n"));

/***/ })

});