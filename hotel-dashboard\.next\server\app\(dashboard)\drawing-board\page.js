(()=>{var e={};e.id=654,e.ids=[654],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4855:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\drawing-board\\\\DrawingBoard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\pycode\\support_chart2\\hotel-dashboard\\components\\drawing-board\\DrawingBoard.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15265:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},18673:(e,r,t)=>{Promise.resolve().then(t.bind(t,4855))},18936:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24934:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>i});var s=t(60687),a=t(43210),o=t(8730),n=t(24224),d=t(96241);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...n},l)=>{let c=a?o.DX:"button";return(0,s.jsx)(c,{className:(0,d.cn)(i({variant:r,size:t,className:e})),ref:l,...n})});l.displayName="Button"},25297:(e,r,t)=>{Promise.resolve().then(t.bind(t,46786))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39390:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(60687),a=t(43210),o=t(78148),n=t(24224),d=t(96241);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(o.b,{ref:t,className:(0,d.cn)(i(),e),...r}));l.displayName=o.b.displayName},51713:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},53784:()=>{},55192:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>d,wL:()=>p});var s=t(60687),a=t(43210),o=t(96241);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...r}));d.displayName="CardHeader";let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));i.displayName="CardTitle";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let p=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",e),...r}));p.displayName="CardFooter"},58014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>a});var s=t(37413);t(82704);let a={title:"v0 App",description:"Created with v0",generator:"v0.dev"};function o({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{children:e})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,r,t)=>{"use strict";t.d(r,{bq:()=>f,eb:()=>b,gC:()=>h,l6:()=>c,yv:()=>p});var s=t(60687),a=t(43210),o=t(22670),n=t(78272),d=t(3589),i=t(13964),l=t(96241);let c=o.bL;o.YJ;let p=o.WT,f=a.forwardRef(({className:e,children:r,...t},a)=>(0,s.jsxs)(o.l9,{ref:a,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[r,(0,s.jsx)(o.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));f.displayName=o.l9.displayName;let u=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(o.PP,{ref:t,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}));u.displayName=o.PP.displayName;let m=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(o.wn,{ref:t,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));m.displayName=o.wn.displayName;let h=a.forwardRef(({className:e,children:r,position:t="popper",...a},n)=>(0,s.jsx)(o.ZL,{children:(0,s.jsxs)(o.UC,{ref:n,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...a,children:[(0,s.jsx)(u,{}),(0,s.jsx)(o.LM,{className:(0,l.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,s.jsx)(m,{})]})}));h.displayName=o.UC.displayName,a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(o.JU,{ref:t,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...r})).displayName=o.JU.displayName;let b=a.forwardRef(({className:e,children:r,...t},a)=>(0,s.jsxs)(o.q7,{ref:a,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(o.VF,{children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})}),(0,s.jsx)(o.p4,{children:r})]}));b.displayName=o.q7.displayName,a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(o.wv,{ref:t,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...r})).displayName=o.wv.displayName},68988:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(60687),a=t(43210),o=t(96241);let n=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));n.displayName="Input"},77909:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413),a=t(4855);let o=()=>(0,s.jsxs)("div",{className:"flex flex-col h-full p-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4 flex-shrink-0",children:"Drawing Board"}),(0,s.jsx)("div",{className:"flex-grow w-full min-h-0",children:(0,s.jsx)(a.default,{})})]})},82704:()=>{},96241:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var s=t(49384),a=t(82348);function o(...e){return(0,a.QP)((0,s.$)(e))}},99249:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>f,tree:()=>l});var s=t(65239),a=t(48088),o=t(88170),n=t.n(o),d=t(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(r,i);let l={children:["",{children:["(dashboard)",{children:["drawing-board",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77909)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\drawing-board\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\drawing-board\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/drawing-board/page",pathname:"/drawing-board",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[719,825,569,571,97,786],()=>t(99249));module.exports=s})();