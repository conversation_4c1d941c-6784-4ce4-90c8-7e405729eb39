using System;
using System.Data;
using System.Runtime.InteropServices; // Required for DllImport
using System.Text; // Required for Encoding
using Newtonsoft.Json;
using ParamManager;
using System.IO;
using System.Reflection;

namespace WrapperApp
{
    class Program
    {
        /// <summary>
        /// Executes a SQL query using the XrNet.dll library and writes the result to a specified file.
        /// </summary>
        /// <param name="sql">The SQL query to execute.</param>
        /// <param name="outputFilePath">The full path to the file where the JSON result should be saved.</param>
        /// <param name="silentMode">Whether to suppress error messages.</param>
        /// <returns>0 on success, non-zero on failure.</returns>
        public static int SearchDB(string sql, string outputFilePath, bool silentMode = false)
        {
            DataSet dataResult = new DataSet();
            int res = XrPLCom.GetGDbData("online", sql, ref dataResult);

            if (res != 0)
            {
                // Log error to stderr
                if (!silentMode)
                    Console.Error.WriteLine($"数据库读取失败，错误代码: {res}");
                return res; // Return the error code on failure
            }

            try
            {
                // Serialize the DataSet to a JSON string
                string jsonResult = JsonConvert.SerializeObject(dataResult, Formatting.Indented);
                // Write the JSON string to the specified file
                File.WriteAllText(outputFilePath, jsonResult);
                return 0; // Success
            }
            catch (Exception ex)
            {
                if (!silentMode)
                    Console.Error.WriteLine($"将结果写入文件时出错: {ex.Message}");
                return -1; // Indicate a failure during file write
            }
        }

        static int Main(string[] args)
        {
            // Check for silent mode flag first
            bool silentMode = args.Length > 1 && args[1] == "--silent";

            // If in silent mode, redirect stdout to null to suppress all library output
            TextWriter originalOut = Console.Out;
            TextWriter originalError = Console.Error;

            // Always keep some error output for debugging
            if (!silentMode)
            {
                Console.Error.WriteLine($"Starting with {args.Length} arguments");
                Console.Error.WriteLine($"Silent mode: {silentMode}");
            }

            if (silentMode)
            {
                // Redirect stdout to suppress library output, but keep stderr for errors
                Console.SetOut(TextWriter.Null);
            }

            // Generate a unique temporary file name
            string tempFileName = $"{Guid.NewGuid()}.json";
            // Build the full path in the system's temporary directory
            string fullPath = Path.Combine(Path.GetTempPath(), tempFileName);

            try
            {
                // Get the directory where the executable is located
                string exePath = Assembly.GetExecutingAssembly().Location;
                string exeDir = Path.GetDirectoryName(exePath);

                // Set the current working directory to the executable's directory
                // This ensures that dependent files like Configure.xml are found correctly.
                if (!string.IsNullOrEmpty(exeDir))
                {
                    Directory.SetCurrentDirectory(exeDir);
                }
            }
            catch (Exception ex)
            {
                if (!silentMode)
                    Console.Error.WriteLine($"设置工作目录时出错: {ex.Message}");
                // Exit on failure to ensure we don't proceed with an incorrect working directory
                return 1;
            }

            // Initialize the communication layer first
            byte[] userBytes = Encoding.UTF8.GetBytes("web_tool");
            // Call the P/Invoked native function directly
            // Use the wrapper for initialization
            int initResult = XrPLCom.xrCommInit(1, 0, userBytes); // Config needs to be determined
            if (initResult != 0)
            {
                if (!silentMode)
                    Console.Error.WriteLine($"xrCommInit 初始化失败，错误代码: {initResult}");
                return 1; // Failure
            }

            if (args.Length == 0)
            {
                if (!silentMode)
                    Console.Error.WriteLine("错误: 未提供 SQL 查询。");
                return 1; // Failure
            }

            string sqlQuery = args[0];

            try
            {
                // Execute the query and write to the temp file
                int searchResult = SearchDB(sqlQuery, fullPath, silentMode);

                if (searchResult == 0)
                {
                    // On success, print the full path of the temp file to standard output
                    // Restore original stdout and stderr if in silent mode to output the file path
                    if (silentMode)
                    {
                        Console.SetOut(originalOut);
                        Console.SetError(originalError);
                    }
                    Console.WriteLine(fullPath);
                    return 0; // Success
                }
                else
                {
                    // If SearchDB failed, it already logged the error. We just return a failure code.
                    return 1; // Failure
                }
            }
            catch (Exception ex)
            {
                // Catch-all for any other unexpected errors during execution
                if (!silentMode)
                {
                    Console.Error.WriteLine($"处理过程中发生意外错误: {ex.Message}");
                    Console.Error.WriteLine(ex.StackTrace);
                }
                return 1; // Failure
            }
        }
    }
}