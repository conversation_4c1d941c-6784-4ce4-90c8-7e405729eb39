import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import crypto from 'crypto';

// 密码文件路径
const PASSWORD_FILE_PATH = path.join(process.cwd(), 'database-password.txt');

// 默认密码（如果文件不存在）
const DEFAULT_PASSWORD = 'admin123';

// 获取或创建密码文件
async function getStoredPassword(): Promise<string> {
  try {
    // 尝试读取密码文件
    const password = await fs.readFile(PASSWORD_FILE_PATH, 'utf-8');
    return password.trim();
  } catch (error) {
    // 文件不存在，创建默认密码文件
    console.log('密码文件不存在，创建默认密码文件');
    await fs.writeFile(PASSWORD_FILE_PATH, DEFAULT_PASSWORD, 'utf-8');
    return DEFAULT_PASSWORD;
  }
}

// 生成密码哈希（简单的哈希，实际项目中应使用更安全的方法）
function hashPassword(password: string): string {
  return crypto.createHash('sha256').update(password).digest('hex');
}

export async function POST(request: NextRequest) {
  try {
    const { password, action } = await request.json();

    if (action === 'verify') {
      // 验证密码
      const storedPassword = await getStoredPassword();
      const isValid = password === storedPassword;

      if (isValid) {
        return NextResponse.json({ 
          success: true, 
          message: '密码验证成功' 
        });
      } else {
        return NextResponse.json({ 
          success: false, 
          message: '密码错误' 
        }, { status: 401 });
      }
    } else if (action === 'change') {
      // 更改密码（需要提供旧密码）
      const { oldPassword, newPassword } = await request.json();
      const storedPassword = await getStoredPassword();

      if (oldPassword !== storedPassword) {
        return NextResponse.json({ 
          success: false, 
          message: '原密码错误' 
        }, { status: 401 });
      }

      // 保存新密码
      await fs.writeFile(PASSWORD_FILE_PATH, newPassword, 'utf-8');
      
      return NextResponse.json({ 
        success: true, 
        message: '密码修改成功' 
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        message: '无效的操作' 
      }, { status: 400 });
    }

  } catch (error: any) {
    console.error('密码验证API错误:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器错误: ' + error.message 
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // 检查密码文件是否存在
    const storedPassword = await getStoredPassword();
    
    return NextResponse.json({ 
      success: true, 
      message: '密码文件已就绪',
      hasPassword: true,
      defaultPassword: storedPassword === DEFAULT_PASSWORD ? DEFAULT_PASSWORD : null
    });
  } catch (error: any) {
    console.error('获取密码状态错误:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器错误: ' + error.message 
    }, { status: 500 });
  }
}
