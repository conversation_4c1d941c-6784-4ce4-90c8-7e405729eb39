(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10034:(e,t,r)=>{var s=r(2984),n=r(22),a=r(46063);e.exports=function(e,t){return e&&e.length?s(e,n(t,2),a):void 0}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25645:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),n=r(48088),a=r(88170),i=r.n(a),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90597)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}],d=["D:\\pycode\\support_chart2\\hotel-dashboard\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},25920:(e,t,r)=>{Promise.resolve().then(r.bind(r,29591))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29591:(e,t,r)=>{"use strict";r.d(t,{default:()=>sR});var s=r(60687),n=r(43210),a=r.n(n),i=r(78272),l=r(62688);let o=(0,l.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),c=(0,l.A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),d=(0,l.A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),u=(0,l.A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]);var m=r(47033),h=r(14952),x=r(98492),p=r(31158);let f=(0,l.A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),j=(0,l.A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),v=(0,l.A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),y=(0,l.A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),g=(0,l.A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]]),b=(0,l.A)("Utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]]),N=(0,l.A)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]),w=(0,l.A)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]]),k=(0,l.A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),A=(0,l.A)("ChartNoAxesColumnIncreasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]),R=(0,l.A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var O=r(61611);let C=(0,l.A)("Brush",[["path",{d:"m9.06 11.9 8.07-8.06a2.85 2.85 0 1 1 4.03 4.03l-8.06 8.08",key:"1styjt"}],["path",{d:"M7.07 14.94c-1.66 0-3 1.35-3 3.02 0 1.33-2.5 1.52-2 2.02 1.08 1.1 2.49 2.02 4 2.02 2.2 0 4-1.8 4-4.04a3.01 3.01 0 0 0-3-3.02z",key:"z0l1mu"}]]),P=(0,l.A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),S=(0,l.A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);var T=r(24934),M=r(55192),I=r(70569),E=r(11273),D=r(72942),F=r(46059),_=r(14163),L=r(43),B=r(65551),K=r(96963),$="Tabs",[W,Z]=(0,E.A)($,[D.RG]),G=(0,D.RG)(),[q,z]=W($),V=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:i,orientation:l="horizontal",dir:o,activationMode:c="automatic",...d}=e,u=(0,L.jH)(o),[m,h]=(0,B.i)({prop:n,onChange:a,defaultProp:i});return(0,s.jsx)(q,{scope:r,baseId:(0,K.B)(),value:m,onValueChange:h,orientation:l,dir:u,activationMode:c,children:(0,s.jsx)(_.sG.div,{dir:u,"data-orientation":l,...d,ref:t})})});V.displayName=$;var J="TabsList",H=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,i=z(J,r),l=G(r);return(0,s.jsx)(D.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:n,children:(0,s.jsx)(_.sG.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:t})})});H.displayName=J;var U="TabsTrigger",X=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...i}=e,l=z(U,r),o=G(r),c=ee(l.baseId,n),d=et(l.baseId,n),u=n===l.value;return(0,s.jsx)(D.q7,{asChild:!0,...o,focusable:!a,active:u,children:(0,s.jsx)(_.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":d,"data-state":u?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...i,ref:t,onMouseDown:(0,I.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,I.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,I.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;u||a||!e||l.onValueChange(n)})})})});X.displayName=U;var Y="TabsContent",Q=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:i,children:l,...o}=e,c=z(Y,r),d=ee(c.baseId,a),u=et(c.baseId,a),m=a===c.value,h=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.jsx)(F.C,{present:i||m,children:({present:r})=>(0,s.jsx)(_.sG.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:u,tabIndex:0,...o,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&l})})});function ee(e,t){return`${e}-trigger-${t}`}function et(e,t){return`${e}-content-${t}`}Q.displayName=Y;var er=r(96241);let es=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(H,{ref:r,className:(0,er.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));es.displayName=H.displayName;let en=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(X,{ref:r,className:(0,er.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));en.displayName=X.displayName,n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(Q,{ref:r,className:(0,er.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t})).displayName=Q.displayName;var ea=r(13495),ei=r(66156),el="Avatar",[eo,ec]=(0,E.A)(el),[ed,eu]=eo(el),em=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...a}=e,[i,l]=n.useState("idle");return(0,s.jsx)(ed,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:l,children:(0,s.jsx)(_.sG.span,{...a,ref:t})})});em.displayName=el;var eh="AvatarImage",ex=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:a,onLoadingStatusChange:i=()=>{},...l}=e,o=eu(eh,r),c=function(e,t){let[r,s]=n.useState("idle");return(0,ei.N)(()=>{if(!e){s("error");return}let r=!0,n=new window.Image,a=e=>()=>{r&&s(e)};return s("loading"),n.onload=a("loaded"),n.onerror=a("error"),n.src=e,t&&(n.referrerPolicy=t),()=>{r=!1}},[e,t]),r}(a,l.referrerPolicy),d=(0,ea.c)(e=>{i(e),o.onImageLoadingStatusChange(e)});return(0,ei.N)(()=>{"idle"!==c&&d(c)},[c,d]),"loaded"===c?(0,s.jsx)(_.sG.img,{...l,ref:t,src:a}):null});ex.displayName=eh;var ep="AvatarFallback",ef=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:a,...i}=e,l=eu(ep,r),[o,c]=n.useState(void 0===a);return n.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>c(!0),a);return()=>window.clearTimeout(e)}},[a]),o&&"loaded"!==l.imageLoadingStatus?(0,s.jsx)(_.sG.span,{...i,ref:t}):null});ef.displayName=ep;let ej=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(em,{ref:r,className:(0,er.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));ej.displayName=em.displayName;let ev=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(ex,{ref:r,className:(0,er.cn)("aspect-square h-full w-full",e),...t}));ev.displayName=ex.displayName;let ey=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(ef,{ref:r,className:(0,er.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));ey.displayName=ef.displayName;var eg=r(98599),eb=r(9510),eN=r(31355),ew=r(1359),ek=r(32547),eA=r(55509),eR=r(25028),eO=r(8730),eC=r(63376),eP=r(42247),eS=["Enter"," "],eT=["ArrowUp","PageDown","End"],eM=["ArrowDown","PageUp","Home",...eT],eI={ltr:[...eS,"ArrowRight"],rtl:[...eS,"ArrowLeft"]},eE={ltr:["ArrowLeft"],rtl:["ArrowRight"]},eD="Menu",[eF,e_,eL]=(0,eb.N)(eD),[eB,eK]=(0,E.A)(eD,[eL,eA.Bk,D.RG]),e$=(0,eA.Bk)(),eW=(0,D.RG)(),[eZ,eG]=eB(eD),[eq,ez]=eB(eD),eV=e=>{let{__scopeMenu:t,open:r=!1,children:a,dir:i,onOpenChange:l,modal:o=!0}=e,c=e$(t),[d,u]=n.useState(null),m=n.useRef(!1),h=(0,ea.c)(l),x=(0,L.jH)(i);return n.useEffect(()=>{let e=()=>{m.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>m.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,s.jsx)(eA.bL,{...c,children:(0,s.jsx)(eZ,{scope:t,open:r,onOpenChange:h,content:d,onContentChange:u,children:(0,s.jsx)(eq,{scope:t,onClose:n.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:m,dir:x,modal:o,children:a})})})};eV.displayName=eD;var eJ=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,a=e$(r);return(0,s.jsx)(eA.Mz,{...a,...n,ref:t})});eJ.displayName="MenuAnchor";var eH="MenuPortal",[eU,eX]=eB(eH,{forceMount:void 0}),eY=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:a}=e,i=eG(eH,t);return(0,s.jsx)(eU,{scope:t,forceMount:r,children:(0,s.jsx)(F.C,{present:r||i.open,children:(0,s.jsx)(eR.Z,{asChild:!0,container:a,children:n})})})};eY.displayName=eH;var eQ="MenuContent",[e0,e1]=eB(eQ),e2=n.forwardRef((e,t)=>{let r=eX(eQ,e.__scopeMenu),{forceMount:n=r.forceMount,...a}=e,i=eG(eQ,e.__scopeMenu),l=ez(eQ,e.__scopeMenu);return(0,s.jsx)(eF.Provider,{scope:e.__scopeMenu,children:(0,s.jsx)(F.C,{present:n||i.open,children:(0,s.jsx)(eF.Slot,{scope:e.__scopeMenu,children:l.modal?(0,s.jsx)(e5,{...a,ref:t}):(0,s.jsx)(e4,{...a,ref:t})})})})}),e5=n.forwardRef((e,t)=>{let r=eG(eQ,e.__scopeMenu),a=n.useRef(null),i=(0,eg.s)(t,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,eC.Eq)(e)},[]),(0,s.jsx)(e3,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,I.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),e4=n.forwardRef((e,t)=>{let r=eG(eQ,e.__scopeMenu);return(0,s.jsx)(e3,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),e3=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:a=!1,trapFocus:i,onOpenAutoFocus:l,onCloseAutoFocus:o,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:u,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:x,onDismiss:p,disableOutsideScroll:f,...j}=e,v=eG(eQ,r),y=ez(eQ,r),g=e$(r),b=eW(r),N=e_(r),[w,k]=n.useState(null),A=n.useRef(null),R=(0,eg.s)(t,A,v.onContentChange),O=n.useRef(0),C=n.useRef(""),P=n.useRef(0),S=n.useRef(null),T=n.useRef("right"),M=n.useRef(0),E=f?eP.A:n.Fragment,F=f?{as:eO.DX,allowPinchZoom:!0}:void 0,_=e=>{let t=C.current+e,r=N().filter(e=>!e.disabled),s=document.activeElement,n=r.find(e=>e.ref.current===s)?.textValue,a=function(e,t,r){var s;let n=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(s=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(s+r)%e.length]));1===n.length&&(a=a.filter(e=>e!==r));let i=a.find(e=>e.toLowerCase().startsWith(n.toLowerCase()));return i!==r?i:void 0}(r.map(e=>e.textValue),t,n),i=r.find(e=>e.textValue===a)?.ref.current;(function e(t){C.current=t,window.clearTimeout(O.current),""!==t&&(O.current=window.setTimeout(()=>e(""),1e3))})(t),i&&setTimeout(()=>i.focus())};n.useEffect(()=>()=>window.clearTimeout(O.current),[]),(0,ew.Oh)();let L=n.useCallback(e=>T.current===S.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:s}=e,n=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e].x,l=t[e].y,o=t[a].x,c=t[a].y;l>s!=c>s&&r<(o-i)*(s-l)/(c-l)+i&&(n=!n)}return n}({x:e.clientX,y:e.clientY},t)}(e,S.current?.area),[]);return(0,s.jsx)(e0,{scope:r,searchRef:C,onItemEnter:n.useCallback(e=>{L(e)&&e.preventDefault()},[L]),onItemLeave:n.useCallback(e=>{L(e)||(A.current?.focus(),k(null))},[L]),onTriggerLeave:n.useCallback(e=>{L(e)&&e.preventDefault()},[L]),pointerGraceTimerRef:P,onPointerGraceIntentChange:n.useCallback(e=>{S.current=e},[]),children:(0,s.jsx)(E,{...F,children:(0,s.jsx)(ek.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,I.m)(l,e=>{e.preventDefault(),A.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:o,children:(0,s.jsx)(eN.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:x,onDismiss:p,children:(0,s.jsx)(D.bL,{asChild:!0,...b,dir:y.dir,orientation:"vertical",loop:a,currentTabStopId:w,onCurrentTabStopIdChange:k,onEntryFocus:(0,I.m)(d,e=>{y.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,s.jsx)(eA.UC,{role:"menu","aria-orientation":"vertical","data-state":tN(v.open),"data-radix-menu-content":"",dir:y.dir,...g,...j,ref:R,style:{outline:"none",...j.style},onKeyDown:(0,I.m)(j.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,s=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&s&&_(e.key));let n=A.current;if(e.target!==n||!eM.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);eT.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,I.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(O.current),C.current="")}),onPointerMove:(0,I.m)(e.onPointerMove,tA(e=>{let t=e.target,r=M.current!==e.clientX;e.currentTarget.contains(t)&&r&&(T.current=e.clientX>M.current?"right":"left",M.current=e.clientX)}))})})})})})})});e2.displayName=eQ;var e6=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,s.jsx)(_.sG.div,{role:"group",...n,ref:t})});e6.displayName="MenuGroup";var e8=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,s.jsx)(_.sG.div,{...n,ref:t})});e8.displayName="MenuLabel";var e9="MenuItem",e7="menu.itemSelect",te=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:a,...i}=e,l=n.useRef(null),o=ez(e9,e.__scopeMenu),c=e1(e9,e.__scopeMenu),d=(0,eg.s)(t,l),u=n.useRef(!1);return(0,s.jsx)(tt,{...i,ref:d,disabled:r,onClick:(0,I.m)(e.onClick,()=>{let e=l.current;if(!r&&e){let t=new CustomEvent(e7,{bubbles:!0,cancelable:!0});e.addEventListener(e7,e=>a?.(e),{once:!0}),(0,_.hO)(e,t),t.defaultPrevented?u.current=!1:o.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),u.current=!0},onPointerUp:(0,I.m)(e.onPointerUp,e=>{u.current||e.currentTarget?.click()}),onKeyDown:(0,I.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;!r&&(!t||" "!==e.key)&&eS.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});te.displayName=e9;var tt=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:a=!1,textValue:i,...l}=e,o=e1(e9,r),c=eW(r),d=n.useRef(null),u=(0,eg.s)(t,d),[m,h]=n.useState(!1),[x,p]=n.useState("");return n.useEffect(()=>{let e=d.current;e&&p((e.textContent??"").trim())},[l.children]),(0,s.jsx)(eF.ItemSlot,{scope:r,disabled:a,textValue:i??x,children:(0,s.jsx)(D.q7,{asChild:!0,...c,focusable:!a,children:(0,s.jsx)(_.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...l,ref:u,onPointerMove:(0,I.m)(e.onPointerMove,tA(e=>{a?o.onItemLeave(e):(o.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,I.m)(e.onPointerLeave,tA(e=>o.onItemLeave(e))),onFocus:(0,I.m)(e.onFocus,()=>h(!0)),onBlur:(0,I.m)(e.onBlur,()=>h(!1))})})})}),tr=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,s.jsx)(td,{scope:e.__scopeMenu,checked:r,children:(0,s.jsx)(te,{role:"menuitemcheckbox","aria-checked":tw(r)?"mixed":r,...a,ref:t,"data-state":tk(r),onSelect:(0,I.m)(a.onSelect,()=>n?.(!!tw(r)||!r),{checkForDefaultPrevented:!1})})})});tr.displayName="MenuCheckboxItem";var ts="MenuRadioGroup",[tn,ta]=eB(ts,{value:void 0,onValueChange:()=>{}}),ti=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...a}=e,i=(0,ea.c)(n);return(0,s.jsx)(tn,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,s.jsx)(e6,{...a,ref:t})})});ti.displayName=ts;var tl="MenuRadioItem",to=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ta(tl,e.__scopeMenu),i=r===a.value;return(0,s.jsx)(td,{scope:e.__scopeMenu,checked:i,children:(0,s.jsx)(te,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":tk(i),onSelect:(0,I.m)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});to.displayName=tl;var tc="MenuItemIndicator",[td,tu]=eB(tc,{checked:!1}),tm=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...a}=e,i=tu(tc,r);return(0,s.jsx)(F.C,{present:n||tw(i.checked)||!0===i.checked,children:(0,s.jsx)(_.sG.span,{...a,ref:t,"data-state":tk(i.checked)})})});tm.displayName=tc;var th=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,s.jsx)(_.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});th.displayName="MenuSeparator";var tx=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,a=e$(r);return(0,s.jsx)(eA.i3,{...a,...n,ref:t})});tx.displayName="MenuArrow";var tp="MenuSub",[tf,tj]=eB(tp),tv="MenuSubTrigger",ty=n.forwardRef((e,t)=>{let r=eG(tv,e.__scopeMenu),a=ez(tv,e.__scopeMenu),i=tj(tv,e.__scopeMenu),l=e1(tv,e.__scopeMenu),o=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=l,u={__scopeMenu:e.__scopeMenu},m=n.useCallback(()=>{o.current&&window.clearTimeout(o.current),o.current=null},[]);return n.useEffect(()=>m,[m]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,s.jsx)(eJ,{asChild:!0,...u,children:(0,s.jsx)(tt,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":tN(r.open),...e,ref:(0,eg.t)(t,i.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,I.m)(e.onPointerMove,tA(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||o.current||(l.onPointerGraceIntentChange(null),o.current=window.setTimeout(()=>{r.onOpenChange(!0),m()},100))})),onPointerLeave:(0,I.m)(e.onPointerLeave,tA(e=>{m();let t=r.content?.getBoundingClientRect();if(t){let s=r.content?.dataset.side,n="right"===s,a=t[n?"left":"right"],i=t[n?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(n?-5:5),y:e.clientY},{x:a,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:a,y:t.bottom}],side:s}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,I.m)(e.onKeyDown,t=>{let s=""!==l.searchRef.current;!e.disabled&&(!s||" "!==t.key)&&eI[a.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});ty.displayName=tv;var tg="MenuSubContent",tb=n.forwardRef((e,t)=>{let r=eX(eQ,e.__scopeMenu),{forceMount:a=r.forceMount,...i}=e,l=eG(eQ,e.__scopeMenu),o=ez(eQ,e.__scopeMenu),c=tj(tg,e.__scopeMenu),d=n.useRef(null),u=(0,eg.s)(t,d);return(0,s.jsx)(eF.Provider,{scope:e.__scopeMenu,children:(0,s.jsx)(F.C,{present:a||l.open,children:(0,s.jsx)(eF.Slot,{scope:e.__scopeMenu,children:(0,s.jsx)(e3,{id:c.contentId,"aria-labelledby":c.triggerId,...i,ref:u,align:"start",side:"rtl"===o.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{o.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,I.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,I.m)(e.onEscapeKeyDown,e=>{o.onClose(),e.preventDefault()}),onKeyDown:(0,I.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=eE[o.dir].includes(e.key);t&&r&&(l.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function tN(e){return e?"open":"closed"}function tw(e){return"indeterminate"===e}function tk(e){return tw(e)?"indeterminate":e?"checked":"unchecked"}function tA(e){return t=>"mouse"===t.pointerType?e(t):void 0}tb.displayName=tg;var tR="DropdownMenu",[tO,tC]=(0,E.A)(tR,[eK]),tP=eK(),[tS,tT]=tO(tR),tM=e=>{let{__scopeDropdownMenu:t,children:r,dir:a,open:i,defaultOpen:l,onOpenChange:o,modal:c=!0}=e,d=tP(t),u=n.useRef(null),[m=!1,h]=(0,B.i)({prop:i,defaultProp:l,onChange:o});return(0,s.jsx)(tS,{scope:t,triggerId:(0,K.B)(),triggerRef:u,contentId:(0,K.B)(),open:m,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),modal:c,children:(0,s.jsx)(eV,{...d,open:m,onOpenChange:h,dir:a,modal:c,children:r})})};tM.displayName=tR;var tI="DropdownMenuTrigger",tE=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...a}=e,i=tT(tI,r),l=tP(r);return(0,s.jsx)(eJ,{asChild:!0,...l,children:(0,s.jsx)(_.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...a,ref:(0,eg.t)(t,i.triggerRef),onPointerDown:(0,I.m)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,I.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});tE.displayName=tI;var tD=e=>{let{__scopeDropdownMenu:t,...r}=e,n=tP(t);return(0,s.jsx)(eY,{...n,...r})};tD.displayName="DropdownMenuPortal";var tF="DropdownMenuContent",t_=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,i=tT(tF,r),l=tP(r),o=n.useRef(!1);return(0,s.jsx)(e2,{id:i.contentId,"aria-labelledby":i.triggerId,...l,...a,ref:t,onCloseAutoFocus:(0,I.m)(e.onCloseAutoFocus,e=>{o.current||i.triggerRef.current?.focus(),o.current=!1,e.preventDefault()}),onInteractOutside:(0,I.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,s=2===t.button||r;(!i.modal||s)&&(o.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});t_.displayName=tF,n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(e6,{...a,...n,ref:t})}).displayName="DropdownMenuGroup";var tL=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(e8,{...a,...n,ref:t})});tL.displayName="DropdownMenuLabel";var tB=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(te,{...a,...n,ref:t})});tB.displayName="DropdownMenuItem";var tK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(tr,{...a,...n,ref:t})});tK.displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(ti,{...a,...n,ref:t})}).displayName="DropdownMenuRadioGroup";var t$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(to,{...a,...n,ref:t})});t$.displayName="DropdownMenuRadioItem";var tW=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(tm,{...a,...n,ref:t})});tW.displayName="DropdownMenuItemIndicator";var tZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(th,{...a,...n,ref:t})});tZ.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(tx,{...a,...n,ref:t})}).displayName="DropdownMenuArrow";var tG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(ty,{...a,...n,ref:t})});tG.displayName="DropdownMenuSubTrigger";var tq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=tP(r);return(0,s.jsx)(tb,{...a,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tq.displayName="DropdownMenuSubContent";var tz=r(13964),tV=r(65822);n.forwardRef(({className:e,inset:t,children:r,...n},a)=>(0,s.jsxs)(tG,{ref:a,className:(0,er.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...n,children:[r,(0,s.jsx)(h.A,{className:"ml-auto"})]})).displayName=tG.displayName,n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(tq,{ref:r,className:(0,er.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=tq.displayName;let tJ=n.forwardRef(({className:e,sideOffset:t=4,...r},n)=>(0,s.jsx)(tD,{children:(0,s.jsx)(t_,{ref:n,sideOffset:t,className:(0,er.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));tJ.displayName=t_.displayName;let tH=n.forwardRef(({className:e,inset:t,...r},n)=>(0,s.jsx)(tB,{ref:n,className:(0,er.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...r}));tH.displayName=tB.displayName,n.forwardRef(({className:e,children:t,checked:r,...n},a)=>(0,s.jsxs)(tK,{ref:a,className:(0,er.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:r,...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(tW,{children:(0,s.jsx)(tz.A,{className:"h-4 w-4"})})}),t]})).displayName=tK.displayName,n.forwardRef(({className:e,children:t,...r},n)=>(0,s.jsxs)(t$,{ref:n,className:(0,er.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(tW,{children:(0,s.jsx)(tV.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=t$.displayName,n.forwardRef(({className:e,inset:t,...r},n)=>(0,s.jsx)(tL,{ref:n,className:(0,er.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...r})).displayName=tL.displayName;let tU=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(tZ,{ref:r,className:(0,er.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));tU.displayName=tZ.displayName;var tX=r(86287);let tY=(0,r(24224).F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-600",warning:"border-transparent bg-amber-100 text-amber-600"}},defaultVariants:{variant:"default"}});function tQ({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,er.cn)(tY({variant:t}),e),...r})}var t0=r(68988),t1=r(39390),t2=r(63974),t5=r(37826),t4=r(93437),t3=r(15616),t6=r(96752),t8=r(70333),t9=r(48482),t7=r(54460),re=r(90812),rt=r(27747),rr=r(9920),rs=r(84629),rn=(0,t7.gu)({chartName:"BarChart",GraphicalChild:re.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:rt.W},{axisType:"yAxis",AxisComp:rr.h}],formatAxisMap:rs.pr}),ra=r(85168),ri=r(38246),rl=r(50326),ro=(0,t7.gu)({chartName:"LineChart",GraphicalChild:rl.N,axisComponents:[{axisType:"xAxis",AxisComp:rt.W},{axisType:"yAxis",AxisComp:rr.h}],formatAxisMap:rs.pr}),rc=r(5231),rd=r.n(rc),ru=r(49384),rm=r(98986),rh=r(95530),rx=r(54186),rp=["points","className","baseLinePoints","connectNulls"];function rf(){return(rf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function rj(e){return function(e){if(Array.isArray(e))return rv(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return rv(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rv(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rv(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,s=Array(t);r<t;r++)s[r]=e[r];return s}var ry=function(e){return e&&e.x===+e.x&&e.y===+e.y},rg=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(function(e){ry(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),ry(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},rb=function(e,t){var r=rg(e);t&&(r=[r.reduce(function(e,t){return[].concat(rj(e),rj(t))},[])]);var s=r.map(function(e){return e.reduce(function(e,t,r){return"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)},"")}).join("");return 1===r.length?"".concat(s,"Z"):s},rN=function(e,t,r){var s=rb(e,r);return"".concat("Z"===s.slice(-1)?s.slice(0,-1):s,"L").concat(rb(t.reverse(),r).slice(1))},rw=function(e){var t=e.points,r=e.className,s=e.baseLinePoints,n=e.connectNulls,i=function(e,t){if(null==e)return{};var r,s,n=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)r=a[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,rp);if(!t||!t.length)return null;var l=(0,ru.A)("recharts-polygon",r);if(s&&s.length){var o=i.stroke&&"none"!==i.stroke,c=rN(t,s,n);return a().createElement("g",{className:l},a().createElement("path",rf({},(0,rx.J9)(i,!0),{fill:"Z"===c.slice(-1)?i.fill:"none",stroke:"none",d:c})),o?a().createElement("path",rf({},(0,rx.J9)(i,!0),{fill:"none",d:rb(t,n)})):null,o?a().createElement("path",rf({},(0,rx.J9)(i,!0),{fill:"none",d:rb(s,n)})):null)}var d=rb(t,n);return a().createElement("path",rf({},(0,rx.J9)(i,!0),{fill:"Z"===d.slice(-1)?i.fill:"none",className:l,d:d}))},rk=r(23561),rA=r(4057),rR=r(19335);function rO(e){return(rO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rC(){return(rC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function rP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function rS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rP(Object(r),!0).forEach(function(t){rD(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rT(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,rF(s.key),s)}}function rM(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(rM=function(){return!!e})()}function rI(e){return(rI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function rE(e,t){return(rE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function rD(e,t,r){return(t=rF(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rF(e){var t=function(e,t){if("object"!=rO(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=rO(s))return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==rO(t)?t:t+""}var r_=Math.PI/180,rL=function(e){var t,r;function s(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,s),e=s,t=arguments,e=rI(e),function(e,t){if(t&&("object"===rO(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,rM()?Reflect.construct(e,t||[],rI(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&rE(e,t)}(s,e),t=[{key:"getTickLineCoord",value:function(e){var t=this.props,r=t.cx,s=t.cy,n=t.radius,a=t.orientation,i=t.tickSize,l=(0,rR.IZ)(r,s,n,e.coordinate),o=(0,rR.IZ)(r,s,n+("inner"===a?-1:1)*(i||8),e.coordinate);return{x1:l.x,y1:l.y,x2:o.x,y2:o.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,r=Math.cos(-e.coordinate*r_);return r>1e-5?"outer"===t?"start":"end":r<-1e-5?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,s=e.radius,n=e.axisLine,i=e.axisLineType,l=rS(rS({},(0,rx.J9)(this.props,!1)),{},{fill:"none"},(0,rx.J9)(n,!1));if("circle"===i)return a().createElement(rh.c,rC({className:"recharts-polar-angle-axis-line"},l,{cx:t,cy:r,r:s}));var o=this.props.ticks.map(function(e){return(0,rR.IZ)(t,r,s,e.coordinate)});return a().createElement(rw,rC({className:"recharts-polar-angle-axis-line"},l,{points:o}))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,n=t.tick,i=t.tickLine,l=t.tickFormatter,o=t.stroke,c=(0,rx.J9)(this.props,!1),d=(0,rx.J9)(n,!1),u=rS(rS({},c),{},{fill:"none"},(0,rx.J9)(i,!1)),m=r.map(function(t,r){var m=e.getTickLineCoord(t),h=rS(rS(rS({textAnchor:e.getTickTextAnchor(t)},c),{},{stroke:"none",fill:o},d),{},{index:r,payload:t,x:m.x2,y:m.y2});return a().createElement(rm.W,rC({className:(0,ru.A)("recharts-polar-angle-axis-tick",(0,rR.Zk)(n)),key:"tick-".concat(t.coordinate)},(0,rA.XC)(e.props,t,r)),i&&a().createElement("line",rC({className:"recharts-polar-angle-axis-tick-line"},u,m)),n&&s.renderTickItem(n,h,l?l(t.value,r):t.value))});return a().createElement(rm.W,{className:"recharts-polar-angle-axis-ticks"},m)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.radius,s=e.axisLine;return!(r<=0)&&t&&t.length?a().createElement(rm.W,{className:(0,ru.A)("recharts-polar-angle-axis",this.props.className)},s&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var s;return a().isValidElement(e)?a().cloneElement(e,t):rd()(e)?e(t):a().createElement(rk.E,rC({},t,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],t&&rT(s.prototype,t),r&&rT(s,r),Object.defineProperty(s,"prototype",{writable:!1}),s}(n.PureComponent);rD(rL,"displayName","PolarAngleAxis"),rD(rL,"axisType","angleAxis"),rD(rL,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var rB=r(57088),rK=r.n(rB),r$=r(10034),rW=r.n(r$),rZ=r(97633),rG=["cx","cy","angle","ticks","axisLine"],rq=["ticks","tick","angle","tickFormatter","stroke"];function rz(e){return(rz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rV(){return(rV=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function rJ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function rH(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rJ(Object(r),!0).forEach(function(t){r1(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rJ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rU(e,t){if(null==e)return{};var r,s,n=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)r=a[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function rX(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,r2(s.key),s)}}function rY(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(rY=function(){return!!e})()}function rQ(e){return(rQ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function r0(e,t){return(r0=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function r1(e,t,r){return(t=r2(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function r2(e){var t=function(e,t){if("object"!=rz(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=rz(s))return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==rz(t)?t:t+""}var r5=function(e){var t,r;function s(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,s),e=s,t=arguments,e=rQ(e),function(e,t){if(t&&("object"===rz(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,rY()?Reflect.construct(e,t||[],rQ(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r0(e,t)}(s,e),t=[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,r=this.props,s=r.angle,n=r.cx,a=r.cy;return(0,rR.IZ)(n,a,t,s)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,r=e.cy,s=e.angle,n=e.ticks,a=rK()(n,function(e){return e.coordinate||0});return{cx:t,cy:r,startAngle:s,endAngle:s,innerRadius:rW()(n,function(e){return e.coordinate||0}).coordinate||0,outerRadius:a.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,s=e.angle,n=e.ticks,i=e.axisLine,l=rU(e,rG),o=n.reduce(function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]},[1/0,-1/0]),c=(0,rR.IZ)(t,r,o[0],s),d=(0,rR.IZ)(t,r,o[1],s),u=rH(rH(rH({},(0,rx.J9)(l,!1)),{},{fill:"none"},(0,rx.J9)(i,!1)),{},{x1:c.x,y1:c.y,x2:d.x,y2:d.y});return a().createElement("line",rV({className:"recharts-polar-radius-axis-line"},u))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,n=t.tick,i=t.angle,l=t.tickFormatter,o=t.stroke,c=rU(t,rq),d=this.getTickTextAnchor(),u=(0,rx.J9)(c,!1),m=(0,rx.J9)(n,!1),h=r.map(function(t,r){var c=e.getTickValueCoord(t),h=rH(rH(rH(rH({textAnchor:d,transform:"rotate(".concat(90-i,", ").concat(c.x,", ").concat(c.y,")")},u),{},{stroke:"none",fill:o},m),{},{index:r},c),{},{payload:t});return a().createElement(rm.W,rV({className:(0,ru.A)("recharts-polar-radius-axis-tick",(0,rR.Zk)(n)),key:"tick-".concat(t.coordinate)},(0,rA.XC)(e.props,t,r)),s.renderTickItem(n,h,l?l(t.value,r):t.value))});return a().createElement(rm.W,{className:"recharts-polar-radius-axis-ticks"},h)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.axisLine,s=e.tick;return t&&t.length?a().createElement(rm.W,{className:(0,ru.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),s&&this.renderTicks(),rZ.J.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var s;return a().isValidElement(e)?a().cloneElement(e,t):rd()(e)?e(t):a().createElement(rk.E,rV({},t,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],t&&rX(s.prototype,t),r&&rX(s,r),Object.defineProperty(s,"prototype",{writable:!1}),s}(n.PureComponent);r1(r5,"displayName","PolarRadiusAxis"),r1(r5,"axisType","radiusAxis"),r1(r5,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var r4=r(93492),r3=r(40491),r6=r.n(r3),r8=r(71967),r9=r.n(r8),r7=r(37456),se=r.n(r7),st=r(81888),sr=r(98845),ss=r(25679),sn=r(20237),sa=r(22989),si=r(30087),sl=r(10521),so=r(67629);function sc(e){return(sc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sd(){return(sd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function su(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function sm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?su(Object(r),!0).forEach(function(t){sj(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):su(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function sh(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,sv(s.key),s)}}function sx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(sx=function(){return!!e})()}function sp(e){return(sp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function sf(e,t){return(sf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function sj(e,t,r){return(t=sv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sv(e){var t=function(e,t){if("object"!=sc(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=sc(s))return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sc(t)?t:t+""}var sy=function(e){var t,r;function s(e){var t,r,n;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,s),r=s,n=[e],r=sp(r),sj(t=function(e,t){if(t&&("object"===sc(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,sx()?Reflect.construct(r,n||[],sp(this).constructor):r.apply(this,n)),"pieRef",null),sj(t,"sectorRefs",[]),sj(t,"id",(0,sa.NF)("recharts-pie-")),sj(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),rd()(e)&&e()}),sj(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),rd()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&sf(e,t)}(s,e),t=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,r=t.label,n=t.labelLine,i=t.dataKey,l=t.valueKey,o=(0,rx.J9)(this.props,!1),c=(0,rx.J9)(r,!1),d=(0,rx.J9)(n,!1),u=r&&r.offsetRadius||20,m=e.map(function(e,t){var m=(e.startAngle+e.endAngle)/2,h=(0,rR.IZ)(e.cx,e.cy,e.outerRadius+u,m),x=sm(sm(sm(sm({},o),e),{},{stroke:"none"},c),{},{index:t,textAnchor:s.getTextAnchor(h.x,e.cx)},h),p=sm(sm(sm(sm({},o),e),{},{fill:"none",stroke:e.fill},d),{},{index:t,points:[(0,rR.IZ)(e.cx,e.cy,e.outerRadius,m),h]}),f=i;return se()(i)&&se()(l)?f="value":se()(i)&&(f=l),a().createElement(rm.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},n&&s.renderLabelLineItem(n,p,"line"),s.renderLabelItem(r,x,(0,si.kr)(e,f)))});return a().createElement(rm.W,{className:"recharts-pie-labels"},m)}},{key:"renderSectorsStatically",value:function(e){var t=this,r=this.props,s=r.activeShape,n=r.blendStroke,i=r.inactiveShape;return e.map(function(r,l){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==e.length)return null;var o=t.isActiveIndex(l),c=i&&t.hasActiveIndex()?i:null,d=sm(sm({},r),{},{stroke:n?r.fill:r.stroke,tabIndex:-1});return a().createElement(rm.W,sd({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},(0,rA.XC)(t.props,r,l),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(l)}),a().createElement(so.yp,sd({option:o?s:c,isActive:o,shapeType:"sector"},d)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,r=t.sectors,s=t.isAnimationActive,n=t.animationBegin,i=t.animationDuration,l=t.animationEasing,o=t.animationId,c=this.state,d=c.prevSectors,u=c.prevIsAnimationActive;return a().createElement(r4.Ay,{begin:n,duration:i,isActive:s,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(o,"-").concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var s=t.t,n=[],i=(r&&r[0]).startAngle;return r.forEach(function(e,t){var r=d&&d[t],a=t>0?r6()(e,"paddingAngle",0):0;if(r){var l=(0,sa.Dj)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),o=sm(sm({},e),{},{startAngle:i+a,endAngle:i+l(s)+a});n.push(o),i=o.endAngle}else{var c=e.endAngle,u=e.startAngle,m=(0,sa.Dj)(0,c-u)(s),h=sm(sm({},e),{},{startAngle:i+a,endAngle:i+m+a});n.push(h),i=h.endAngle}}),a().createElement(rm.W,null,e.renderSectorsStatically(n))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var r=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"ArrowRight":var s=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[s].focus(),t.setState({sectorToFocus:s});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,r=e.isAnimationActive,s=this.state.prevSectors;return r&&t&&t.length&&(!s||!r9()(s,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,r=t.hide,s=t.sectors,n=t.className,i=t.label,l=t.cx,o=t.cy,c=t.innerRadius,d=t.outerRadius,u=t.isAnimationActive,m=this.state.isAnimationFinished;if(r||!s||!s.length||!(0,sa.Et)(l)||!(0,sa.Et)(o)||!(0,sa.Et)(c)||!(0,sa.Et)(d))return null;var h=(0,ru.A)("recharts-pie",n);return a().createElement(rm.W,{tabIndex:this.props.rootTabIndex,className:h,ref:function(t){e.pieRef=t}},this.renderSectors(),i&&this.renderLabels(s),rZ.J.renderCallByParent(this.props,null,!1),(!u||m)&&sr.Z.renderCallByParent(this.props,s,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);if(rd()(e))return e(t);var s=(0,ru.A)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return a().createElement(st.I,sd({},t,{key:r,type:"linear",className:s}))}},{key:"renderLabelItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);var s=r;if(rd()(e)&&(s=e(t),a().isValidElement(s)))return s;var n=(0,ru.A)("recharts-pie-label-text","boolean"==typeof e||rd()(e)?"":e.className);return a().createElement(rk.E,sd({},t,{alignmentBaseline:"middle",className:n}),s)}}],t&&sh(s.prototype,t),r&&sh(s,r),Object.defineProperty(s,"prototype",{writable:!1}),s}(n.PureComponent);sj(sy,"displayName","Pie"),sj(sy,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!sn.m.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),sj(sy,"parseDeltaAngle",function(e,t){return(0,sa.sA)(t-e)*Math.min(Math.abs(t-e),360)}),sj(sy,"getRealPieData",function(e){var t=e.data,r=e.children,s=(0,rx.J9)(e,!1),n=(0,rx.aS)(r,ss.f);return t&&t.length?t.map(function(e,t){return sm(sm(sm({payload:e},s),e),n&&n[t]&&n[t].props)}):n&&n.length?n.map(function(e){return sm(sm({},s),e.props)}):[]}),sj(sy,"parseCoordinateOfPie",function(e,t){var r=t.top,s=t.left,n=t.width,a=t.height,i=(0,rR.lY)(n,a);return{cx:s+(0,sa.F4)(e.cx,n,n/2),cy:r+(0,sa.F4)(e.cy,a,a/2),innerRadius:(0,sa.F4)(e.innerRadius,i,0),outerRadius:(0,sa.F4)(e.outerRadius,i,.8*i),maxRadius:e.maxRadius||Math.sqrt(n*n+a*a)/2}}),sj(sy,"getComposedData",function(e){var t,r,s=e.item,n=e.offset,a=void 0!==s.type.defaultProps?sm(sm({},s.type.defaultProps),s.props):s.props,i=sy.getRealPieData(a);if(!i||!i.length)return null;var l=a.cornerRadius,o=a.startAngle,c=a.endAngle,d=a.paddingAngle,u=a.dataKey,m=a.nameKey,h=a.valueKey,x=a.tooltipType,p=Math.abs(a.minAngle),f=sy.parseCoordinateOfPie(a,n),j=sy.parseDeltaAngle(o,c),v=Math.abs(j),y=u;se()(u)&&se()(h)?((0,sl.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),y="value"):se()(u)&&((0,sl.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),y=h);var g=i.filter(function(e){return 0!==(0,si.kr)(e,y,0)}).length,b=v-g*p-(v>=360?g:g-1)*d,N=i.reduce(function(e,t){var r=(0,si.kr)(t,y,0);return e+((0,sa.Et)(r)?r:0)},0);return N>0&&(t=i.map(function(e,t){var s,n=(0,si.kr)(e,y,0),a=(0,si.kr)(e,m,t),i=((0,sa.Et)(n)?n:0)/N,c=(s=t?r.endAngle+(0,sa.sA)(j)*d*+(0!==n):o)+(0,sa.sA)(j)*((0!==n?p:0)+i*b),u=(s+c)/2,h=(f.innerRadius+f.outerRadius)/2,v=[{name:a,value:n,payload:e,dataKey:y,type:x}],g=(0,rR.IZ)(f.cx,f.cy,h,u);return r=sm(sm(sm({percent:i,cornerRadius:l,name:a,tooltipPayload:v,midAngle:u,middleRadius:h,tooltipPosition:g},e),f),{},{value:(0,si.kr)(e,y),startAngle:s,endAngle:c,payload:e,paddingAngle:(0,sa.sA)(j)*d})})),sm(sm({},f),{},{sectors:t,data:i})});var sg=(0,t7.gu)({chartName:"PieChart",GraphicalChild:sy,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:rL},{axisType:"radiusAxis",AxisComp:r5}],formatAxisMap:rR.pr,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),sb=r(5147),sN=r(27558),sw=r(46786);let sk=()=>(0,s.jsxs)("div",{className:"flex flex-col h-full p-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4 flex-shrink-0",children:"Drawing Board"}),(0,s.jsx)("div",{className:"flex-grow w-full min-h-0",children:(0,s.jsx)(sw.default,{})})]});var sA=r(66369);function sR(){let[e,t]=(0,n.useState)("stays"),[r,a]=(0,n.useState)("dashboard"),[l,I]=(0,n.useState)(!1),[E,D]=(0,n.useState)(!1),{toast:F}=(0,t8.dj)(),_=[{name:"Breakfast",value:35},{name:"Lunch",value:45},{name:"Dinner",value:55},{name:"Room Service",value:25}],L=["#0088FE","#00C49F","#FFBB28","#FF8042"];return(0,s.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[l&&(0,s.jsx)(T.$,{variant:"outline",size:"icon",className:"fixed bottom-4 right-4 z-50 rounded-full h-12 w-12 shadow-lg bg-white",onClick:()=>D(!0),children:(0,s.jsx)(k,{className:"h-6 w-6"})}),(0,s.jsxs)("div",{className:`${l?"fixed inset-0 z-50 transform transition-transform duration-300 ease-in-out":"w-64"} ${l&&!E?"-translate-x-full":"translate-x-0"} bg-white border-r border-gray-200 flex flex-col`,children:[l&&(0,s.jsx)("div",{className:"flex justify-end p-4",children:(0,s.jsx)(T.$,{variant:"ghost",size:"icon",onClick:()=>D(!1),children:(0,s.jsx)(m.A,{className:"h-6 w-6"})})}),(0,s.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,s.jsx)("h1",{className:"text-2xl font-semibold text-purple-600",children:"XREAL TEST"})}),(0,s.jsx)("div",{className:"flex-1 py-4 overflow-y-auto",children:(0,s.jsxs)("nav",{className:"space-y-1 px-2",children:[(0,s.jsxs)("button",{onClick:()=>a("dashboard"),className:`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${"dashboard"===r?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,s.jsx)(A,{className:"mr-3 h-5 w-5"}),"Dashboard"]}),(0,s.jsxs)("button",{onClick:()=>a("log-analysis"),className:`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${"log-analysis"===r?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,s.jsx)(R,{className:"mr-3 h-5 w-5"}),"胶合日志分析"]}),(0,s.jsxs)("button",{onClick:()=>a("surface-data-query"),className:`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${"surface-data-query"===r?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,s.jsx)(O.A,{className:"mr-3 h-5 w-5"}),"面形数据查询"]}),(0,s.jsxs)("button",{onClick:()=>a("drawing-board"),className:`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${"drawing-board"===r?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,s.jsx)(C,{className:"mr-3 h-5 w-5"}),"画图板"]}),(0,s.jsxs)("button",{onClick:()=>a("database-query"),className:`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${"database-query"===r?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,s.jsx)(O.A,{className:"mr-3 h-5 w-5"}),"数据库查询"]}),(0,s.jsxs)("button",{onClick:()=>a("premium"),className:`flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ${"premium"===r?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,s.jsx)(P,{className:"mr-3 h-5 w-5"}),"Try Premium Version"]})]})})]}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsxs)("header",{className:"bg-white border-b border-gray-200 flex items-center justify-between px-4 py-4 md:px-6",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[l&&(0,s.jsx)(T.$,{variant:"ghost",size:"icon",className:"mr-2",onClick:()=>D(!0),children:(0,s.jsx)(k,{className:"h-5 w-5"})}),(0,s.jsx)("h1",{className:"text-xl font-semibold text-gray-800",children:"dashboard"===r?"Dashboard":"check-in-out"===r?"Check In-Out":"rooms"===r?"Rooms":"messages"===r?"Messages":"customer-review"===r?"Customer Review":"billing"===r?"Billing System":"food-delivery"===r?"Food Delivery":"log-analysis"===r?"胶合日志分析":"surface-data-query"===r?"面形数据查询":"drawing-board"===r?"画图板":"database-query"===r?"数据库查询":"Premium Version"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(T.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,s.jsx)(S,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"})]}),(0,s.jsxs)(tM,{children:[(0,s.jsx)(tE,{asChild:!0,children:(0,s.jsx)(T.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,s.jsxs)(ej,{className:"h-8 w-8",children:[(0,s.jsx)(ev,{src:"/placeholder.svg?height=32&width=32",alt:"User"}),(0,s.jsx)(ey,{children:"U"})]})})}),(0,s.jsxs)(tJ,{align:"end",children:[(0,s.jsx)(tH,{children:"Profile"}),(0,s.jsx)(tH,{children:"Settings"}),(0,s.jsx)(tH,{children:"Logout"})]})]})]})]}),(0,s.jsxs)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 bg-gray-50",children:["dashboard"===r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"flex justify-end mb-4",children:(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Wed // July 26th, 2023"})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6",children:[(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4 flex items-center",children:[(0,s.jsx)("div",{className:"bg-blue-50 p-3 rounded-full mr-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-blue-500",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M5 12h14"}),(0,s.jsx)("path",{d:"M12 5l7 7-7 7"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Arrival ",(0,s.jsx)("span",{className:"text-xs",children:"(This week)"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold mr-2",children:"73"}),(0,s.jsx)("span",{className:"text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded",children:"+24%"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Previous week: 35"})]})]})}),(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4 flex items-center",children:[(0,s.jsx)("div",{className:"bg-amber-50 p-3 rounded-full mr-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-amber-500",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M19 12H5"}),(0,s.jsx)("path",{d:"M12 19l-7-7 7-7"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Departure ",(0,s.jsx)("span",{className:"text-xs",children:"(This week)"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold mr-2",children:"35"}),(0,s.jsx)("span",{className:"text-xs px-1.5 py-0.5 bg-red-100 text-red-600 rounded",children:"-12%"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Previous week: 97"})]})]})}),(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4 flex items-center",children:[(0,s.jsx)("div",{className:"bg-cyan-50 p-3 rounded-full mr-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-cyan-500",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("rect",{x:"3",y:"4",width:"18",height:"18",rx:"2",ry:"2"}),(0,s.jsx)("line",{x1:"16",y1:"2",x2:"16",y2:"6"}),(0,s.jsx)("line",{x1:"8",y1:"2",x2:"8",y2:"6"}),(0,s.jsx)("line",{x1:"3",y1:"10",x2:"21",y2:"10"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Booking ",(0,s.jsx)("span",{className:"text-xs",children:"(This week)"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold mr-2",children:"237"}),(0,s.jsx)("span",{className:"text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded",children:"+31%"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Previous week: 187"})]})]})}),(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Today Activities"}),(0,s.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1",children:(0,s.jsx)("span",{children:"5"})}),(0,s.jsxs)("p",{className:"text-xs",children:["Room",(0,s.jsx)("br",{}),"Available"]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1",children:(0,s.jsx)("span",{children:"10"})}),(0,s.jsxs)("p",{className:"text-xs",children:["Room",(0,s.jsx)("br",{}),"Blocked"]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1",children:(0,s.jsx)("span",{children:"15"})}),(0,s.jsx)("p",{className:"text-xs",children:"Guest"})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Total Revenue"}),(0,s.jsx)("p",{className:"text-lg font-bold",children:"Rs.35k"})]})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6",children:[(0,s.jsxs)(M.Zp,{children:[(0,s.jsxs)(M.aR,{className:"flex flex-row items-center justify-between p-4 pb-2",children:[(0,s.jsx)(M.ZB,{className:"text-base font-medium",children:"Revenue"}),(0,s.jsxs)(tM,{children:[(0,s.jsx)(tE,{asChild:!0,children:(0,s.jsxs)(T.$,{variant:"ghost",size:"sm",className:"h-8 text-xs",children:["this week ",(0,s.jsx)(i.A,{className:"ml-1 h-3 w-3"})]})}),(0,s.jsxs)(tJ,{align:"end",children:[(0,s.jsx)(tH,{children:"This Month"}),(0,s.jsx)(tH,{children:"This Year"})]})]})]}),(0,s.jsx)(M.Wu,{className:"p-4 pt-0",children:(0,s.jsx)("div",{className:"h-[200px] w-full",children:(0,s.jsx)(t9.u,{width:"100%",height:"100%",children:(0,s.jsxs)(rn,{data:[{name:"Sun",value:8},{name:"Mon",value:10},{name:"Tue",value:12},{name:"Wed",value:11},{name:"Thu",value:9},{name:"Fri",value:11},{name:"Sat",value:12}],margin:{top:10,right:10,left:-20,bottom:0},children:[(0,s.jsx)(ra.d,{strokeDasharray:"3 3",vertical:!1}),(0,s.jsx)(rt.W,{dataKey:"name",axisLine:!1,tickLine:!1}),(0,s.jsx)(rr.h,{hide:!0}),(0,s.jsx)(ri.m,{content:({active:e,payload:t})=>e&&t&&t.length?(0,s.jsx)("div",{className:"bg-white p-2 border rounded shadow-sm",children:(0,s.jsx)("p",{className:"text-xs",children:`${t[0].value} K`})}):null}),(0,s.jsx)(re.y,{dataKey:"value",fill:"#F59E0B",radius:[4,4,0,0]})]})})})})]}),(0,s.jsxs)(M.Zp,{children:[(0,s.jsxs)(M.aR,{className:"flex flex-row items-center justify-between p-4 pb-2",children:[(0,s.jsx)(M.ZB,{className:"text-base font-medium",children:"Guests"}),(0,s.jsxs)(tM,{children:[(0,s.jsx)(tE,{asChild:!0,children:(0,s.jsxs)(T.$,{variant:"ghost",size:"sm",className:"h-8 text-xs",children:["this week ",(0,s.jsx)(i.A,{className:"ml-1 h-3 w-3"})]})}),(0,s.jsxs)(tJ,{align:"end",children:[(0,s.jsx)(tH,{children:"This Month"}),(0,s.jsx)(tH,{children:"This Year"})]})]})]}),(0,s.jsx)(M.Wu,{className:"p-4 pt-0",children:(0,s.jsx)("div",{className:"h-[200px] w-full",children:(0,s.jsx)(t9.u,{width:"100%",height:"100%",children:(0,s.jsxs)(ro,{data:[{name:"Sun",value:8e3},{name:"Mon",value:1e4},{name:"Tue",value:12e3},{name:"Wed",value:9e3},{name:"Thu",value:6e3},{name:"Fri",value:8e3}],margin:{top:10,right:10,left:-20,bottom:0},children:[(0,s.jsx)(ra.d,{strokeDasharray:"3 3",vertical:!1}),(0,s.jsx)(rt.W,{dataKey:"name",axisLine:!1,tickLine:!1}),(0,s.jsx)(rr.h,{hide:!0}),(0,s.jsx)(ri.m,{content:({active:e,payload:t})=>e&&t&&t.length?(0,s.jsx)("div",{className:"bg-white p-2 border rounded shadow-sm",children:(0,s.jsx)("p",{className:"text-xs",children:`${t[0].value}`})}):null}),(0,s.jsx)(rl.N,{type:"monotone",dataKey:"value",stroke:"#3B82F6",strokeWidth:2,dot:{r:4,fill:"white",stroke:"#3B82F6",strokeWidth:2},activeDot:{r:6},fill:"url(#colorUv)"}),(0,s.jsx)("defs",{children:(0,s.jsxs)("linearGradient",{id:"colorUv",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,s.jsx)("stop",{offset:"5%",stopColor:"#3B82F6",stopOpacity:.2}),(0,s.jsx)("stop",{offset:"95%",stopColor:"#3B82F6",stopOpacity:0})]})}),(0,s.jsx)("area",{type:"monotone",dataKey:"value",stroke:"none",fill:"url(#colorUv)"})]})})})})]}),(0,s.jsxs)(M.Zp,{children:[(0,s.jsxs)(M.aR,{className:"flex flex-row items-center justify-between p-4 pb-2",children:[(0,s.jsx)(M.ZB,{className:"text-base font-medium",children:"Rooms"}),(0,s.jsxs)(tM,{children:[(0,s.jsx)(tE,{asChild:!0,children:(0,s.jsxs)(T.$,{variant:"ghost",size:"sm",className:"h-8 text-xs",children:["this week ",(0,s.jsx)(i.A,{className:"ml-1 h-3 w-3"})]})}),(0,s.jsxs)(tJ,{align:"end",children:[(0,s.jsx)(tH,{children:"This Month"}),(0,s.jsx)(tH,{children:"This Year"})]})]})]}),(0,s.jsxs)(M.Wu,{className:"p-4 pt-0",children:[(0,s.jsx)("div",{className:"text-xs mb-2",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("p",{children:"Total 50 Rooms"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,s.jsx)("span",{children:"Occupied"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("span",{className:"h-2 w-2 rounded-full bg-green-500"}),(0,s.jsx)("span",{children:"Booked"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("span",{className:"h-2 w-2 rounded-full bg-amber-500"}),(0,s.jsx)("span",{children:"Available"})]})]})]})}),(0,s.jsx)("div",{className:"h-[180px] w-full",children:(0,s.jsx)(t9.u,{width:"100%",height:"100%",children:(0,s.jsxs)(rn,{data:[{name:"Sun",occupied:15,booked:10,available:25},{name:"Mon",occupied:20,booked:12,available:18},{name:"Tue",occupied:18,booked:15,available:17},{name:"Wed",occupied:22,booked:10,available:18},{name:"Thu",occupied:20,booked:15,available:15},{name:"Fri",occupied:18,booked:12,available:20},{name:"Sat",occupied:15,booked:10,available:25}],margin:{top:10,right:10,left:-20,bottom:0},children:[(0,s.jsx)(ra.d,{strokeDasharray:"3 3",vertical:!1}),(0,s.jsx)(rt.W,{dataKey:"name",axisLine:!1,tickLine:!1}),(0,s.jsx)(rr.h,{hide:!0}),(0,s.jsx)(ri.m,{content:({active:e,payload:t})=>e&&t&&t.length?(0,s.jsxs)("div",{className:"bg-white p-2 border rounded shadow-sm",children:[(0,s.jsx)("p",{className:"text-xs",children:`Occupied: ${t[0].value}`}),(0,s.jsx)("p",{className:"text-xs",children:`Booked: ${t[1].value}`}),(0,s.jsx)("p",{className:"text-xs",children:`Available: ${t[2].value}`})]}):null}),(0,s.jsx)(re.y,{dataKey:"occupied",fill:"#3B82F6",radius:[4,4,0,0]}),(0,s.jsx)(re.y,{dataKey:"booked",fill:"#10B981",radius:[4,4,0,0]}),(0,s.jsx)(re.y,{dataKey:"available",fill:"#F59E0B",radius:[4,4,0,0]})]})})})]})]})]}),(0,s.jsxs)(M.Zp,{className:"mb-6",children:[(0,s.jsx)(M.aR,{className:"p-4 pb-0",children:(0,s.jsxs)(M.ZB,{className:"text-base font-medium",children:["Todays Booking ",(0,s.jsx)("span",{className:"text-xs font-normal text-gray-500",children:"(8 Guest today)"})]})}),(0,s.jsx)(M.Wu,{className:"p-4",children:(0,s.jsxs)(V,{defaultValue:"stays",className:"w-full",children:[(0,s.jsxs)(es,{className:"mb-4 border-b w-full justify-start rounded-none bg-transparent p-0",children:[(0,s.jsx)(en,{value:"stays",className:"rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none",onClick:()=>t("stays"),children:"Stays"}),(0,s.jsx)(en,{value:"packages",className:"rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none",onClick:()=>t("packages"),children:"Packages"}),(0,s.jsx)(en,{value:"arrivals",className:"rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none",onClick:()=>t("arrivals"),children:"Arrivals"}),(0,s.jsx)(en,{value:"departure",className:"rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none",onClick:()=>t("departure"),children:"Departure"})]}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between mb-4 gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search guest by name or phone number or booking ID",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full md:w-[400px] text-sm"})]}),(0,s.jsxs)(T.$,{className:"bg-blue-500 hover:bg-blue-600 text-white",children:[(0,s.jsx)(c,{className:"h-4 w-4 mr-2"}),"Add Booking"]})]}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)(t6.XI,{children:[(0,s.jsx)(t6.A0,{children:(0,s.jsxs)(t6.Hj,{children:[(0,s.jsx)(t6.nd,{className:"whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:["NAME ",(0,s.jsx)(i.A,{className:"h-4 w-4 ml-1"})]})}),(0,s.jsx)(t6.nd,{className:"whitespace-nowrap",children:"BOOKING ID"}),(0,s.jsx)(t6.nd,{className:"whitespace-nowrap",children:"NIGHTS"}),(0,s.jsx)(t6.nd,{className:"whitespace-nowrap",children:"ROOM TYPE"}),(0,s.jsx)(t6.nd,{className:"whitespace-nowrap",children:"GUESTS"}),(0,s.jsx)(t6.nd,{className:"whitespace-nowrap",children:"PAID"}),(0,s.jsx)(t6.nd,{className:"whitespace-nowrap",children:"COST"}),(0,s.jsx)(t6.nd,{className:"whitespace-nowrap",children:"ACTION"})]})}),(0,s.jsx)(t6.BF,{children:[{id:1,name:"Ram Kailash",phone:"9905598912",bookingId:"SDK89635",nights:2,roomType:"1 King Room",guests:2,paid:"rsp.150",cost:"rsp.1500",avatar:"/placeholder.svg?height=32&width=32"},{id:2,name:"Samira Karki",phone:"9815394203",bookingId:"SDK89635",nights:4,roomType:["1 Queen","1 King Room"],guests:5,paid:"paid",cost:"rsp.5500",avatar:"/placeholder.svg?height=32&width=32"},{id:3,name:"Jeevan Rai",phone:"9865328452",bookingId:"SDK89635",nights:1,roomType:["1 Deluxe","1 King Room"],guests:3,paid:"rsp.150",cost:"rsp.2500",avatar:"/placeholder.svg?height=32&width=32"},{id:4,name:"Bindu Sharma",phone:"9845653124",bookingId:"SDK89635",nights:3,roomType:["1 Deluxe","1 King Room"],guests:2,paid:"rsp.150",cost:"rsp.3000",avatar:"/placeholder.svg?height=32&width=32"}].map(e=>(0,s.jsxs)(t6.Hj,{children:[(0,s.jsx)(t6.nA,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)(ej,{className:"h-8 w-8 mr-3",children:[(0,s.jsx)(ev,{src:e.avatar,alt:e.name}),(0,s.jsx)(ey,{children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.name}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.phone})]})]})}),(0,s.jsx)(t6.nA,{children:e.bookingId}),(0,s.jsx)(t6.nA,{children:e.nights}),(0,s.jsx)(t6.nA,{children:Array.isArray(e.roomType)?(0,s.jsx)("div",{children:e.roomType.map((e,t)=>(0,s.jsx)("p",{children:e},t))}):e.roomType}),(0,s.jsxs)(t6.nA,{children:[e.guests," Guests"]}),(0,s.jsx)(t6.nA,{children:"paid"===e.paid?(0,s.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-600 rounded text-xs",children:"paid"}):e.paid}),(0,s.jsx)(t6.nA,{children:e.cost}),(0,s.jsx)(t6.nA,{children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(T.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,s.jsx)(d,{className:"h-4 w-4"})}),(0,s.jsx)(T.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,s.jsx)(u,{className:"h-4 w-4"})})]})})]},e.id))})]})}),(0,s.jsx)("div",{className:"flex justify-end mt-4",children:(0,s.jsx)(T.$,{variant:"link",className:"text-blue-500 hover:text-blue-600",children:"See other Bookings"})})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)(M.Zp,{children:[(0,s.jsx)(M.aR,{className:"p-4 pb-0",children:(0,s.jsx)(M.ZB,{className:"text-base font-medium",children:"Calender"})}),(0,s.jsxs)(M.Wu,{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)(T.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)("h3",{className:"text-sm font-medium",children:"August 2023"}),(0,s.jsx)(T.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-7 gap-1 text-center text-xs",children:[(0,s.jsx)("div",{className:"py-1 font-medium",children:"SU"}),(0,s.jsx)("div",{className:"py-1 font-medium",children:"MO"}),(0,s.jsx)("div",{className:"py-1 font-medium",children:"TU"}),(0,s.jsx)("div",{className:"py-1 font-medium",children:"WE"}),(0,s.jsx)("div",{className:"py-1 font-medium",children:"TH"}),(0,s.jsx)("div",{className:"py-1 font-medium",children:"FR"}),(0,s.jsx)("div",{className:"py-1 font-medium",children:"SA"}),(0,s.jsx)("div",{className:"py-1 text-gray-400",children:"31"}),(0,s.jsx)("div",{className:"py-1",children:"1"}),(0,s.jsxs)("div",{className:"py-1 relative",children:["2",(0,s.jsx)("span",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"})]}),(0,s.jsx)("div",{className:"py-1",children:"3"}),(0,s.jsx)("div",{className:"py-1",children:"4"}),(0,s.jsx)("div",{className:"py-1",children:"5"}),(0,s.jsx)("div",{className:"py-1",children:"6"}),(0,s.jsx)("div",{className:"py-1",children:"7"}),(0,s.jsx)("div",{className:"py-1",children:"8"}),(0,s.jsxs)("div",{className:"py-1 relative",children:["9",(0,s.jsx)("span",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"})]}),(0,s.jsx)("div",{className:"py-1",children:"10"}),(0,s.jsx)("div",{className:"py-1",children:"11"}),(0,s.jsx)("div",{className:"py-1",children:"12"}),(0,s.jsx)("div",{className:"py-1",children:"13"}),(0,s.jsx)("div",{className:"py-1",children:"14"}),(0,s.jsx)("div",{className:"py-1",children:"15"}),(0,s.jsx)("div",{className:"py-1",children:"16"}),(0,s.jsx)("div",{className:"py-1",children:"17"}),(0,s.jsx)("div",{className:"py-1",children:"18"}),(0,s.jsx)("div",{className:"py-1",children:"19"}),(0,s.jsx)("div",{className:"py-1",children:"20"}),(0,s.jsx)("div",{className:"py-1",children:"21"}),(0,s.jsx)("div",{className:"py-1",children:"22"}),(0,s.jsx)("div",{className:"py-1",children:"23"}),(0,s.jsxs)("div",{className:"py-1 relative",children:["24",(0,s.jsx)("span",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"})]}),(0,s.jsx)("div",{className:"py-1",children:"25"}),(0,s.jsx)("div",{className:"py-1",children:"26"}),(0,s.jsx)("div",{className:"py-1",children:"27"}),(0,s.jsx)("div",{className:"py-1",children:"28"}),(0,s.jsx)("div",{className:"py-1",children:"29"}),(0,s.jsx)("div",{className:"py-1",children:"30"}),(0,s.jsx)("div",{className:"py-1",children:"31"}),(0,s.jsx)("div",{className:"py-1 text-gray-400",children:"1"}),(0,s.jsx)("div",{className:"py-1 text-gray-400",children:"2"}),(0,s.jsx)("div",{className:"py-1 text-gray-400",children:"3"})]}),(0,s.jsxs)("div",{className:"mt-6 border rounded-md p-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium mb-2",children:"August 02, 2023 Booking Lists"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mb-3",children:"(3 Bookings)"}),(0,s.jsx)("div",{className:"space-y-3",children:[{date:2,guest:"Carl Larson II",nights:2,guests:2},{date:9,guest:"Mrs. Emmett Morar",nights:2,guests:2},{date:24,guest:"Marjorie Klocko",nights:2,guests:2}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)(ej,{className:"h-8 w-8 mr-3",children:[(0,s.jsx)(ev,{src:"/placeholder.svg?height=32&width=32",alt:e.guest}),(0,s.jsx)(ey,{children:e.guest.split(" ").map(e=>e[0]).join("")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:e.guest}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[e.nights," Nights | ",e.guests," Guests"]})]})]},t))})]})]})]}),(0,s.jsxs)(M.Zp,{children:[(0,s.jsxs)(M.aR,{className:"flex flex-row items-center justify-between p-4 pb-0",children:[(0,s.jsx)(M.ZB,{className:"text-base font-medium",children:"Overall Rating"}),(0,s.jsxs)(tM,{children:[(0,s.jsx)(tE,{asChild:!0,children:(0,s.jsxs)(T.$,{variant:"ghost",size:"sm",className:"h-8 text-xs",children:["This Week ",(0,s.jsx)(i.A,{className:"ml-1 h-3 w-3"})]})}),(0,s.jsxs)(tJ,{align:"end",children:[(0,s.jsx)(tH,{children:"This Month"}),(0,s.jsx)(tH,{children:"This Year"})]})]})]}),(0,s.jsxs)(M.Wu,{className:"p-4",children:[(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsxs)("div",{className:"relative w-48 h-24",children:[(0,s.jsxs)("svg",{viewBox:"0 0 100 50",className:"w-full h-full",children:[(0,s.jsx)("path",{d:"M 0 50 A 50 50 0 0 1 100 50",fill:"none",stroke:"#e5e7eb",strokeWidth:"10"}),(0,s.jsx)("path",{d:"M 0 50 A 50 50 0 0 1 90 50",fill:"none",stroke:"#3b82f6",strokeWidth:"10"})]}),(0,s.jsx)("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Rating"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:"4.5/5"}),(0,s.jsx)("span",{className:"text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded",children:"+31%"})]})})]})}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm",children:"Cleanliness"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(tX.k,{value:90,className:"h-2 w-32"}),(0,s.jsx)("span",{className:"text-sm",children:"4.5"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm",children:"Facilities"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(tX.k,{value:90,className:"h-2 w-32"}),(0,s.jsx)("span",{className:"text-sm",children:"4.5"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm",children:"Location"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(tX.k,{value:50,className:"h-2 w-32"}),(0,s.jsx)("span",{className:"text-sm",children:"2.5"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm",children:"Room Comfort"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(tX.k,{value:50,className:"h-2 w-32"}),(0,s.jsx)("span",{className:"text-sm",children:"2.5"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm",children:"Service"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(tX.k,{value:76,className:"h-2 w-32"}),(0,s.jsx)("span",{className:"text-sm",children:"3.8"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm",children:"Value for money"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(tX.k,{value:76,className:"h-2 w-32"}),(0,s.jsx)("span",{className:"text-sm",children:"3.8"})]})]})]})]})]})]})]}),"billing"===r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold",children:"Billing System"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(T.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),"Filter"]}),(0,s.jsxs)(T.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),"Export"]}),(0,s.jsxs)(T.$,{size:"sm",className:"flex items-center gap-1",children:[(0,s.jsx)(c,{className:"h-4 w-4"}),"New Invoice"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4 flex items-center",children:[(0,s.jsx)("div",{className:"bg-blue-50 p-3 rounded-full mr-4",children:(0,s.jsx)(f,{className:"h-6 w-6 text-blue-500"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Total Revenue"}),(0,s.jsx)("h3",{className:"text-2xl font-bold",children:"Rs.125,000"}),(0,s.jsx)("p",{className:"text-xs text-green-600",children:"+12% from last month"})]})]})}),(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4 flex items-center",children:[(0,s.jsx)("div",{className:"bg-green-50 p-3 rounded-full mr-4",children:(0,s.jsx)(j,{className:"h-6 w-6 text-green-500"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Paid Invoices"}),(0,s.jsx)("h3",{className:"text-2xl font-bold",children:"Rs.98,500"}),(0,s.jsx)("p",{className:"text-xs text-green-600",children:"78% of total"})]})]})}),(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4 flex items-center",children:[(0,s.jsx)("div",{className:"bg-amber-50 p-3 rounded-full mr-4",children:(0,s.jsx)(v,{className:"h-6 w-6 text-amber-500"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Pending Payments"}),(0,s.jsx)("h3",{className:"text-2xl font-bold",children:"Rs.26,500"}),(0,s.jsx)("p",{className:"text-xs text-amber-600",children:"22% of total"})]})]})})]}),(0,s.jsxs)(M.Zp,{className:"mb-6",children:[(0,s.jsx)(M.aR,{className:"p-4 pb-0",children:(0,s.jsx)(M.ZB,{className:"text-base font-medium",children:"Recent Invoices"})}),(0,s.jsx)(M.Wu,{className:"p-4",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)(t6.XI,{children:[(0,s.jsx)(t6.A0,{children:(0,s.jsxs)(t6.Hj,{children:[(0,s.jsx)(t6.nd,{children:"Invoice ID"}),(0,s.jsx)(t6.nd,{children:"Guest"}),(0,s.jsx)(t6.nd,{children:"Date"}),(0,s.jsx)(t6.nd,{children:"Amount"}),(0,s.jsx)(t6.nd,{children:"Status"}),(0,s.jsx)(t6.nd,{className:"text-right",children:"Actions"})]})}),(0,s.jsx)(t6.BF,{children:[{id:"INV-2023-001",guest:"Ram Kailash",date:"26 Jul 2023",amount:"rsp.1500",status:"Paid",items:[{description:"Room Charges (2 nights)",amount:"rsp.1200"},{description:"Food & Beverages",amount:"rsp.300"}]},{id:"INV-2023-002",guest:"Samira Karki",date:"25 Jul 2023",amount:"rsp.5500",status:"Paid",items:[{description:"Room Charges (4 nights)",amount:"rsp.4800"},{description:"Food & Beverages",amount:"rsp.700"}]},{id:"INV-2023-003",guest:"Jeevan Rai",date:"24 Jul 2023",amount:"rsp.2500",status:"Pending",items:[{description:"Room Charges (1 night)",amount:"rsp.2000"},{description:"Food & Beverages",amount:"rsp.500"}]}].map(e=>(0,s.jsxs)(t6.Hj,{children:[(0,s.jsx)(t6.nA,{className:"font-medium",children:e.id}),(0,s.jsx)(t6.nA,{children:e.guest}),(0,s.jsx)(t6.nA,{children:e.date}),(0,s.jsx)(t6.nA,{children:e.amount}),(0,s.jsx)(t6.nA,{children:(0,s.jsx)(tQ,{variant:"Paid"===e.status?"success":"warning",children:e.status})}),(0,s.jsx)(t6.nA,{className:"text-right",children:(0,s.jsxs)(tM,{children:[(0,s.jsx)(tE,{asChild:!0,children:(0,s.jsx)(T.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,s.jsx)(y,{className:"h-4 w-4"})})}),(0,s.jsxs)(tJ,{align:"end",children:[(0,s.jsx)(tH,{onClick:()=>{F({title:"Invoice details",description:`Viewing details for invoice ${e.id}`})},children:"View Details"}),(0,s.jsxs)(tH,{onClick:()=>{F({title:"Invoice printed",description:`Invoice ${e.id} sent to printer`})},children:[(0,s.jsx)(g,{className:"h-4 w-4 mr-2"}),"Print"]}),(0,s.jsxs)(tH,{onClick:()=>{F({title:"Invoice downloaded",description:`Invoice ${e.id} downloaded as PDF`})},children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Download"]}),(0,s.jsx)(tU,{}),(0,s.jsx)(tH,{onClick:()=>{F({title:"Payment reminder sent",description:`Reminder sent to ${e.guest}`})},children:"Send Reminder"})]})]})})]},e.id))})]})})})]}),(0,s.jsxs)(t5.lG,{children:[(0,s.jsx)(t5.zM,{asChild:!0,children:(0,s.jsx)(T.$,{className:"mb-6",children:"Create New Invoice"})}),(0,s.jsxs)(t5.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(t5.c7,{children:[(0,s.jsx)(t5.L3,{children:"Create New Invoice"}),(0,s.jsx)(t5.rr,{children:"Create a new invoice for a guest. Fill in all the required details."})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(t1.J,{htmlFor:"guest",className:"text-right",children:"Guest"}),(0,s.jsxs)(t2.l6,{children:[(0,s.jsx)(t2.bq,{className:"col-span-3",children:(0,s.jsx)(t2.yv,{placeholder:"Select guest"})}),(0,s.jsxs)(t2.gC,{children:[(0,s.jsx)(t2.eb,{value:"ram",children:"Ram Kailash"}),(0,s.jsx)(t2.eb,{value:"samira",children:"Samira Karki"}),(0,s.jsx)(t2.eb,{value:"jeevan",children:"Jeevan Rai"}),(0,s.jsx)(t2.eb,{value:"bindu",children:"Bindu Sharma"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(t1.J,{htmlFor:"room",className:"text-right",children:"Room"}),(0,s.jsxs)(t2.l6,{children:[(0,s.jsx)(t2.bq,{className:"col-span-3",children:(0,s.jsx)(t2.yv,{placeholder:"Select room"})}),(0,s.jsxs)(t2.gC,{children:[(0,s.jsx)(t2.eb,{value:"101",children:"101 - King Room"}),(0,s.jsx)(t2.eb,{value:"102",children:"102 - Queen Room"}),(0,s.jsx)(t2.eb,{value:"201",children:"201 - Deluxe Room"}),(0,s.jsx)(t2.eb,{value:"301",children:"301 - Suite"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(t1.J,{htmlFor:"date",className:"text-right",children:"Date"}),(0,s.jsx)(t0.p,{id:"date",type:"date",className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(t1.J,{htmlFor:"amount",className:"text-right",children:"Amount"}),(0,s.jsx)(t0.p,{id:"amount",type:"number",placeholder:"0.00",className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(t1.J,{htmlFor:"description",className:"text-right",children:"Description"}),(0,s.jsx)(t3.T,{id:"description",placeholder:"Invoice description",className:"col-span-3"})]})]}),(0,s.jsx)(t5.Es,{children:(0,s.jsx)(T.$,{type:"submit",onClick:()=>{F({title:"Invoice created",description:"New invoice has been created successfully"})},children:"Create Invoice"})})]})]})]}),"food-delivery"===r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold",children:"Food Delivery System"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(T.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),"Filter"]}),(0,s.jsxs)(T.$,{size:"sm",className:"flex items-center gap-1",children:[(0,s.jsx)(c,{className:"h-4 w-4"}),"New Order"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4 flex items-center",children:[(0,s.jsx)("div",{className:"bg-blue-50 p-3 rounded-full mr-4",children:(0,s.jsx)(b,{className:"h-6 w-6 text-blue-500"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Total Orders"}),(0,s.jsx)("h3",{className:"text-2xl font-bold",children:"42"}),(0,s.jsx)("p",{className:"text-xs text-green-600",children:"+8% from yesterday"})]})]})}),(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4 flex items-center",children:[(0,s.jsx)("div",{className:"bg-green-50 p-3 rounded-full mr-4",children:(0,s.jsx)(N,{className:"h-6 w-6 text-green-500"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Completed"}),(0,s.jsx)("h3",{className:"text-2xl font-bold",children:"35"}),(0,s.jsx)("p",{className:"text-xs text-green-600",children:"83% of total"})]})]})}),(0,s.jsx)(M.Zp,{children:(0,s.jsxs)(M.Wu,{className:"p-4 flex items-center",children:[(0,s.jsx)("div",{className:"bg-amber-50 p-3 rounded-full mr-4",children:(0,s.jsx)(w,{className:"h-6 w-6 text-amber-500"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"In Progress"}),(0,s.jsx)("h3",{className:"text-2xl font-bold",children:"7"}),(0,s.jsx)("p",{className:"text-xs text-amber-600",children:"17% of total"})]})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,s.jsx)("div",{className:"md:col-span-2",children:(0,s.jsxs)(M.Zp,{children:[(0,s.jsx)(M.aR,{className:"p-4 pb-0",children:(0,s.jsx)(M.ZB,{className:"text-base font-medium",children:"Active Orders"})}),(0,s.jsx)(M.Wu,{className:"p-4",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)(t6.XI,{children:[(0,s.jsx)(t6.A0,{children:(0,s.jsxs)(t6.Hj,{children:[(0,s.jsx)(t6.nd,{children:"Order ID"}),(0,s.jsx)(t6.nd,{children:"Guest"}),(0,s.jsx)(t6.nd,{children:"Room"}),(0,s.jsx)(t6.nd,{children:"Items"}),(0,s.jsx)(t6.nd,{children:"Total"}),(0,s.jsx)(t6.nd,{children:"Status"}),(0,s.jsx)(t6.nd,{children:"Actions"})]})}),(0,s.jsx)(t6.BF,{children:[{id:"FO-1234",guest:"Ram Kailash",room:"101",items:["Chicken Curry","Naan Bread","Rice"],total:"rsp.850",status:"Delivered",time:"12:30 PM"},{id:"FO-1235",guest:"Samira Karki",room:"205",items:["Vegetable Pasta","Garlic Bread","Tiramisu"],total:"rsp.1200",status:"Preparing",time:"1:15 PM"},{id:"FO-1236",guest:"Jeevan Rai",room:"310",items:["Club Sandwich","French Fries","Coke"],total:"rsp.650",status:"On the way",time:"1:45 PM"}].map(e=>(0,s.jsxs)(t6.Hj,{children:[(0,s.jsx)(t6.nA,{className:"font-medium",children:e.id}),(0,s.jsx)(t6.nA,{children:e.guest}),(0,s.jsx)(t6.nA,{children:e.room}),(0,s.jsx)(t6.nA,{children:(0,s.jsx)("div",{className:"flex flex-col",children:e.items.map((e,t)=>(0,s.jsx)("span",{className:"text-xs",children:e},t))})}),(0,s.jsx)(t6.nA,{children:e.total}),(0,s.jsx)(t6.nA,{children:(0,s.jsx)(tQ,{variant:"Delivered"===e.status?"success":"Preparing"===e.status?"warning":"default",children:e.status})}),(0,s.jsx)(t6.nA,{children:(0,s.jsxs)(tM,{children:[(0,s.jsx)(tE,{asChild:!0,children:(0,s.jsx)(T.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,s.jsx)(y,{className:"h-4 w-4"})})}),(0,s.jsxs)(tJ,{align:"end",children:[(0,s.jsx)(tH,{onClick:()=>{F({title:"Order details",description:`Viewing details for order ${e.id}`})},children:"View Details"}),(0,s.jsx)(tH,{onClick:()=>{F({title:"Order status updated",description:`Order ${e.id} marked as delivered`})},children:"Mark as Delivered"}),(0,s.jsx)(tU,{}),(0,s.jsx)(tH,{onClick:()=>{F({title:"Order cancelled",description:`Order ${e.id} has been cancelled`,variant:"destructive"})},children:"Cancel Order"})]})]})})]},e.id))})]})})})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(M.Zp,{children:[(0,s.jsx)(M.aR,{className:"p-4 pb-0",children:(0,s.jsx)(M.ZB,{className:"text-base font-medium",children:"Order Distribution"})}),(0,s.jsxs)(M.Wu,{className:"p-4",children:[(0,s.jsx)("div",{className:"h-[250px]",children:(0,s.jsx)(t9.u,{width:"100%",height:"100%",children:(0,s.jsxs)(sg,{children:[(0,s.jsx)(sy,{data:_,cx:"50%",cy:"50%",labelLine:!1,outerRadius:80,fill:"#8884d8",dataKey:"value",label:({name:e,percent:t})=>`${e}: ${(100*t).toFixed(0)}%`,children:_.map((e,t)=>(0,s.jsx)(ss.f,{fill:L[t%L.length]},`cell-${t}`))}),(0,s.jsx)(ri.m,{})]})})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-4 mt-4",children:_.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full mr-1",style:{backgroundColor:L[t%L.length]}}),(0,s.jsxs)("span",{className:"text-xs",children:[e.name,": ",e.value]})]},t))})]})]})})]}),(0,s.jsxs)(t5.lG,{children:[(0,s.jsx)(t5.zM,{asChild:!0,children:(0,s.jsx)(T.$,{className:"mb-6",children:"Place New Order"})}),(0,s.jsxs)(t5.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(t5.c7,{children:[(0,s.jsx)(t5.L3,{children:"Place New Food Order"}),(0,s.jsx)(t5.rr,{children:"Create a new food order for a guest. Select items from the menu."})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(t1.J,{htmlFor:"guest",className:"text-right",children:"Guest"}),(0,s.jsxs)(t2.l6,{children:[(0,s.jsx)(t2.bq,{className:"col-span-3",children:(0,s.jsx)(t2.yv,{placeholder:"Select guest"})}),(0,s.jsxs)(t2.gC,{children:[(0,s.jsx)(t2.eb,{value:"ram",children:"Ram Kailash - Room 101"}),(0,s.jsx)(t2.eb,{value:"samira",children:"Samira Karki - Room 205"}),(0,s.jsx)(t2.eb,{value:"jeevan",children:"Jeevan Rai - Room 310"}),(0,s.jsx)(t2.eb,{value:"bindu",children:"Bindu Sharma - Room 402"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(t1.J,{className:"text-right",children:"Menu Items"}),(0,s.jsxs)("div",{className:"col-span-3 border rounded-md p-3 space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(t4.S,{id:"item1"}),(0,s.jsxs)(t1.J,{htmlFor:"item1",className:"flex justify-between w-full",children:[(0,s.jsx)("span",{children:"Chicken Curry"}),(0,s.jsx)("span",{children:"Rs.450"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(t4.S,{id:"item2"}),(0,s.jsxs)(t1.J,{htmlFor:"item2",className:"flex justify-between w-full",children:[(0,s.jsx)("span",{children:"Vegetable Pasta"}),(0,s.jsx)("span",{children:"Rs.350"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(t4.S,{id:"item3"}),(0,s.jsxs)(t1.J,{htmlFor:"item3",className:"flex justify-between w-full",children:[(0,s.jsx)("span",{children:"Club Sandwich"}),(0,s.jsx)("span",{children:"Rs.250"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(t4.S,{id:"item4"}),(0,s.jsxs)(t1.J,{htmlFor:"item4",className:"flex justify-between w-full",children:[(0,s.jsx)("span",{children:"Naan Bread"}),(0,s.jsx)("span",{children:"Rs.50"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(t4.S,{id:"item5"}),(0,s.jsxs)(t1.J,{htmlFor:"item5",className:"flex justify-between w-full",children:[(0,s.jsx)("span",{children:"Rice"}),(0,s.jsx)("span",{children:"Rs.100"})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(t1.J,{htmlFor:"special",className:"text-right",children:"Special Instructions"}),(0,s.jsx)(t3.T,{id:"special",placeholder:"Any special requests",className:"col-span-3"})]})]}),(0,s.jsx)(t5.Es,{children:(0,s.jsx)(T.$,{type:"submit",onClick:()=>{F({title:"Order placed",description:"Food order has been placed successfully"})},children:"Place Order"})})]})]})]}),"log-analysis"===r&&(0,s.jsx)(sN.default,{})," ","surface-data-query"===r&&(0,s.jsx)(sb.default,{})," ","drawing-board"===r&&(0,s.jsx)(sk,{})," ","database-query"===r&&(0,s.jsx)(sA.default,{})," ","dashboard"!==r&&"billing"!==r&&"food-delivery"!==r&&"log-analysis"!==r&&"surface-data-query"!==r&&"drawing-board"!==r&&"database-query"!==r&&(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsxs)(M.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(M.aR,{children:[(0,s.jsx)(M.ZB,{children:"Coming Soon"}),(0,s.jsx)(M.BT,{children:"This section is under development and will be available soon."})]}),(0,s.jsx)(M.Wu,{children:(0,s.jsxs)("p",{children:["The"," ","check-in-out"===r?"Check In-Out":"rooms"===r?"Rooms":"messages"===r?"Messages":"customer-review"===r?"Customer Review":"Premium"," ","module is currently being built. Please check back later."]})}),(0,s.jsx)(M.wL,{children:(0,s.jsx)(T.$,{onClick:()=>a("dashboard"),children:"Return to Dashboard"})})]})})]})]})]})}},33873:e=>{"use strict";e.exports=require("path")},57088:(e,t,r)=>{var s=r(2984),n=r(99180),a=r(22);e.exports=function(e,t){return e&&e.length?s(e,a(t,2),n):void 0}},62368:(e,t,r)=>{Promise.resolve().then(r.bind(r,70958))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70958:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\dashboard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\pycode\\support_chart2\\hotel-dashboard\\dashboard.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},90597:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(37413),n=r(70958);function a(){return(0,s.jsx)(n.default,{})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[719,825,569,571,97,313,541,198,382,558,147,786],()=>r(25645));module.exports=s})();