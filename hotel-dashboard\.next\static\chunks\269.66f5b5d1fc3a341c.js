(()=>{"use strict";if("undefined"!=typeof DedicatedWorkerGlobalScope&&self instanceof DedicatedWorkerGlobalScope&&"function"==typeof self.postMessage){let o=/^\w+\s+(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2},\d{3})/;function e(e){let r=o.exec(e);return r?r[1]:null}let r=/####### 胶厚值:([+-]?(?:\d+\.?\d*|\.\d+))/,t=/####### 准直diff:([+-]?(?:\d+\.?\d*|\.\d+))/,l=/打开真空泵(?:(?!打开真空泵)[\s\S])*?insert into g_support/g;self.onmessage=function(o){console.log("[Worker] Message received in worker:",o);let s=o.data;if(!s||"string"!=typeof s){console.error("[Worker] Invalid log content received:",s);let e={error:"Invalid log content received by worker."};console.log("[Worker] Posting error message (invalid content):",e),self.postMessage(e);return}console.log("[Worker] Received log content. Length: ".concat(s.length,". Starting parsing..."));try{let o=function(o){if(!o)return[];let s=[],n=0;for(let c of Array.from(o.matchAll(l))){n++;let o=c[0],l="block_".concat(n);if(o){let n=function(o,l){if(!l)return null;let s=l.split("\n");if(!s.length)return null;let n=null,c=null;if(s.length>0){for(let o=0;o<s.length;o++){let r=e(s[o]);if(r){n=r;break}}for(let o=s.length-1;o>=0;o--){let r=e(s[o]);if(r){c=r;break}}!c&&n&&(c=n)}let i=[],a=[],g=[];return s.forEach((o,l)=>{let s=e(o);o.includes("打开放气阀")&&s&&i.push({timestamp:s,line_content:o.trim()});let c=r.exec(o);if(c)try{let e=c[1],o=parseFloat(e),r=s||n;r&&a.push({timestamp:r,value:o})}catch(e){}let f=t.exec(o);if(f)try{let e=f[1],o=parseFloat(e),r=s||n;r&&g.push({timestamp:r,value:o})}catch(e){}}),{block_id:o,start_time:n,end_time:c,lines_count:s.length,valve_open_events:i,glue_thickness_values:a,collimation_diff_values:g}}(l,o);n&&(n.glue_thickness_values.length>0||n.collimation_diff_values.length>0)&&s.push(n)}}return s}(s);if(console.log("[Worker] Parsing complete. Found ".concat(o.length," blocks with data.")),o&&o.length>0){console.log("[Worker] Sending ".concat(o.length," processed blocks to main thread."));let e={success:!0,allBlocks:o,message:"Successfully processed ".concat(o.length," blocks.")};console.log("[Worker] Posting success message (with blocks):",JSON.stringify(e)),self.postMessage(e)}else{let e="No relevant data blocks with glue/collimation values were found in the log file after parsing.";console.log("[Worker] ".concat(e));let o={success:!0,allBlocks:[],message:e};console.log("[Worker] Posting success message (no blocks):",JSON.stringify(o)),self.postMessage(o)}}catch(o){console.error("[Worker] Critical error during log processing in worker:",o);let e={error:o.message||"Unknown error in worker processing."};console.log("[Worker] Posting critical error message:",JSON.stringify(e)),self.postMessage(e)}},self.onerror=function(e){console.error("[Worker] Uncaught error in worker script:",e)},console.log('[Worker] logParser.worker.ts script loaded and event listener for "message" set up.')}else console.warn("[Worker] logParser.worker.ts loaded in non-worker context (or main thread). Exports are available, but worker-specific code will not run.")})(),_N_E={};