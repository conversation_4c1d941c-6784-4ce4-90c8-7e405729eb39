(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{17720:(e,s,a)=>{Promise.resolve().then(a.bind(a,57022))},57022:(e,s,a)=>{"use strict";a.d(s,{default:()=>ey});var l=a(95155),i=a(12115),t=a(66474),r=a(47924),n=a(84616),d=a(13717),c=a(74126),x=a(42355),o=a(13052),m=a(1482),h=a(91788),j=a(81586),u=a(55868),p=a(14186),N=a(5623),g=a(81304),f=a(95747),v=a(86151),b=a(29799),y=a(74783),w=a(50741),k=a(57434),A=a(54213),R=a(76028),C=a(69037),F=a(23861),S=a(97168),B=a(88482),T=a(60704),I=a(53999);let W=T.bL,D=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(T.B8,{ref:s,className:(0,I.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...i})});D.displayName=T.B8.displayName;let O=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(T.l9,{ref:s,className:(0,I.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...i})});O.displayName=T.l9.displayName,i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(T.UC,{ref:s,className:(0,I.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...i})}).displayName=T.UC.displayName;var P=a(85977);let z=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(P.bL,{ref:s,className:(0,I.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...i})});z.displayName=P.bL.displayName;let Z=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(P._V,{ref:s,className:(0,I.cn)("aspect-square h-full w-full",a),...i})});Z.displayName=P._V.displayName;let K=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(P.H4,{ref:s,className:(0,I.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...i})});K.displayName=P.H4.displayName;var L=a(48698),M=a(5196),$=a(9428);let J=L.bL,E=L.l9;L.YJ,L.ZL,L.Pb,L.z6,i.forwardRef((e,s)=>{let{className:a,inset:i,children:t,...r}=e;return(0,l.jsxs)(L.ZP,{ref:s,className:(0,I.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",i&&"pl-8",a),...r,children:[t,(0,l.jsx)(o.A,{className:"ml-auto"})]})}).displayName=L.ZP.displayName,i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(L.G5,{ref:s,className:(0,I.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...i})}).displayName=L.G5.displayName;let q=i.forwardRef((e,s)=>{let{className:a,sideOffset:i=4,...t}=e;return(0,l.jsx)(L.ZL,{children:(0,l.jsx)(L.UC,{ref:s,sideOffset:i,className:(0,I.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...t})})});q.displayName=L.UC.displayName;let G=i.forwardRef((e,s)=>{let{className:a,inset:i,...t}=e;return(0,l.jsx)(L.q7,{ref:s,className:(0,I.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",i&&"pl-8",a),...t})});G.displayName=L.q7.displayName,i.forwardRef((e,s)=>{let{className:a,children:i,checked:t,...r}=e;return(0,l.jsxs)(L.H_,{ref:s,className:(0,I.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:t,...r,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(L.VF,{children:(0,l.jsx)(M.A,{className:"h-4 w-4"})})}),i]})}).displayName=L.H_.displayName,i.forwardRef((e,s)=>{let{className:a,children:i,...t}=e;return(0,l.jsxs)(L.hN,{ref:s,className:(0,I.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...t,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(L.VF,{children:(0,l.jsx)($.A,{className:"h-2 w-2 fill-current"})})}),i]})}).displayName=L.hN.displayName,i.forwardRef((e,s)=>{let{className:a,inset:i,...t}=e;return(0,l.jsx)(L.JU,{ref:s,className:(0,I.cn)("px-2 py-1.5 text-sm font-semibold",i&&"pl-8",a),...t})}).displayName=L.JU.displayName;let V=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(L.wv,{ref:s,className:(0,I.cn)("-mx-1 my-1 h-px bg-muted",a),...i})});V.displayName=L.wv.displayName;var _=a(41397);let U=(0,a(74466).F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-600",warning:"border-transparent bg-amber-100 text-amber-600"}},defaultVariants:{variant:"default"}});function H(e){let{className:s,variant:a,...i}=e;return(0,l.jsx)("div",{className:(0,I.cn)(U({variant:a}),s),...i})}var Y=a(89852),X=a(82714),Q=a(95784),ee=a(99840),es=a(95139),ea=a(99474),el=a(88524),ei=a(14503),et=a(83540),er=a(3401),en=a(94754),ed=a(96025),ec=a(16238),ex=a(94517),eo=a(83394),em=a(93504),eh=a(21374),ej=a(8782),eu=a(34e3),ep=a(54811),eN=a(4508),eg=a(49587),ef=a(82991);let ev=()=>(0,l.jsxs)("div",{className:"flex flex-col h-full p-4",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold mb-4 flex-shrink-0",children:"Drawing Board"}),(0,l.jsx)("div",{className:"flex-grow w-full min-h-0",children:(0,l.jsx)(ef.default,{})})]});var eb=a(93394);function ey(){let[e,s]=(0,i.useState)("stays"),[a,T]=(0,i.useState)("dashboard"),[I,P]=(0,i.useState)(!1),[L,M]=(0,i.useState)(!1),{toast:$}=(0,ei.dj)();(0,i.useEffect)(()=>{let e=()=>{P(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]);let U=[{name:"Breakfast",value:35},{name:"Lunch",value:45},{name:"Dinner",value:55},{name:"Room Service",value:25}],ef=["#0088FE","#00C49F","#FFBB28","#FF8042"];return(0,l.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[I&&(0,l.jsx)(S.$,{variant:"outline",size:"icon",className:"fixed bottom-4 right-4 z-50 rounded-full h-12 w-12 shadow-lg bg-white",onClick:()=>M(!0),children:(0,l.jsx)(y.A,{className:"h-6 w-6"})}),(0,l.jsxs)("div",{className:"".concat(I?"fixed inset-0 z-50 transform transition-transform duration-300 ease-in-out":"w-64"," ").concat(I&&!L?"-translate-x-full":"translate-x-0"," bg-white border-r border-gray-200 flex flex-col"),children:[I&&(0,l.jsx)("div",{className:"flex justify-end p-4",children:(0,l.jsx)(S.$,{variant:"ghost",size:"icon",onClick:()=>M(!1),children:(0,l.jsx)(x.A,{className:"h-6 w-6"})})}),(0,l.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,l.jsx)("h1",{className:"text-2xl font-semibold text-purple-600",children:"XREAL TEST"})}),(0,l.jsx)("div",{className:"flex-1 py-4 overflow-y-auto",children:(0,l.jsxs)("nav",{className:"space-y-1 px-2",children:[(0,l.jsxs)("button",{onClick:()=>T("dashboard"),className:"flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ".concat("dashboard"===a?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,l.jsx)(w.A,{className:"mr-3 h-5 w-5"}),"Dashboard"]}),(0,l.jsxs)("button",{onClick:()=>T("log-analysis"),className:"flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ".concat("log-analysis"===a?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,l.jsx)(k.A,{className:"mr-3 h-5 w-5"}),"胶合日志分析"]}),(0,l.jsxs)("button",{onClick:()=>T("surface-data-query"),className:"flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ".concat("surface-data-query"===a?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,l.jsx)(A.A,{className:"mr-3 h-5 w-5"}),"面形数据查询"]}),(0,l.jsxs)("button",{onClick:()=>T("drawing-board"),className:"flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ".concat("drawing-board"===a?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,l.jsx)(R.A,{className:"mr-3 h-5 w-5"}),"画图板"]}),(0,l.jsxs)("button",{onClick:()=>T("database-query"),className:"flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ".concat("database-query"===a?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,l.jsx)(A.A,{className:"mr-3 h-5 w-5"}),"数据库查询"]}),(0,l.jsxs)("button",{onClick:()=>T("premium"),className:"flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md ".concat("premium"===a?"text-blue-600 bg-blue-50 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,l.jsx)(C.A,{className:"mr-3 h-5 w-5"}),"Try Premium Version"]})]})})]}),(0,l.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,l.jsxs)("header",{className:"bg-white border-b border-gray-200 flex items-center justify-between px-4 py-4 md:px-6",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[I&&(0,l.jsx)(S.$,{variant:"ghost",size:"icon",className:"mr-2",onClick:()=>M(!0),children:(0,l.jsx)(y.A,{className:"h-5 w-5"})}),(0,l.jsx)("h1",{className:"text-xl font-semibold text-gray-800",children:"dashboard"===a?"Dashboard":"check-in-out"===a?"Check In-Out":"rooms"===a?"Rooms":"messages"===a?"Messages":"customer-review"===a?"Customer Review":"billing"===a?"Billing System":"food-delivery"===a?"Food Delivery":"log-analysis"===a?"胶合日志分析":"surface-data-query"===a?"面形数据查询":"drawing-board"===a?"画图板":"database-query"===a?"数据库查询":"Premium Version"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)(S.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,l.jsx)(F.A,{className:"h-5 w-5"}),(0,l.jsx)("span",{className:"absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"})]}),(0,l.jsxs)(J,{children:[(0,l.jsx)(E,{asChild:!0,children:(0,l.jsx)(S.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,l.jsxs)(z,{className:"h-8 w-8",children:[(0,l.jsx)(Z,{src:"/placeholder.svg?height=32&width=32",alt:"User"}),(0,l.jsx)(K,{children:"U"})]})})}),(0,l.jsxs)(q,{align:"end",children:[(0,l.jsx)(G,{children:"Profile"}),(0,l.jsx)(G,{children:"Settings"}),(0,l.jsx)(G,{children:"Logout"})]})]})]})]}),(0,l.jsxs)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 bg-gray-50",children:["dashboard"===a&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"flex justify-end mb-4",children:(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Wed // July 26th, 2023"})}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6",children:[(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4 flex items-center",children:[(0,l.jsx)("div",{className:"bg-blue-50 p-3 rounded-full mr-4",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-blue-500",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("path",{d:"M5 12h14"}),(0,l.jsx)("path",{d:"M12 5l7 7-7 7"})]})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["Arrival ",(0,l.jsx)("span",{className:"text-xs",children:"(This week)"})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("h3",{className:"text-2xl font-bold mr-2",children:"73"}),(0,l.jsx)("span",{className:"text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded",children:"+24%"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"Previous week: 35"})]})]})}),(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4 flex items-center",children:[(0,l.jsx)("div",{className:"bg-amber-50 p-3 rounded-full mr-4",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-amber-500",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("path",{d:"M19 12H5"}),(0,l.jsx)("path",{d:"M12 19l-7-7 7-7"})]})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["Departure ",(0,l.jsx)("span",{className:"text-xs",children:"(This week)"})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("h3",{className:"text-2xl font-bold mr-2",children:"35"}),(0,l.jsx)("span",{className:"text-xs px-1.5 py-0.5 bg-red-100 text-red-600 rounded",children:"-12%"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"Previous week: 97"})]})]})}),(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4 flex items-center",children:[(0,l.jsx)("div",{className:"bg-cyan-50 p-3 rounded-full mr-4",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-cyan-500",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("rect",{x:"3",y:"4",width:"18",height:"18",rx:"2",ry:"2"}),(0,l.jsx)("line",{x1:"16",y1:"2",x2:"16",y2:"6"}),(0,l.jsx)("line",{x1:"8",y1:"2",x2:"8",y2:"6"}),(0,l.jsx)("line",{x1:"3",y1:"10",x2:"21",y2:"10"})]})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["Booking ",(0,l.jsx)("span",{className:"text-xs",children:"(This week)"})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("h3",{className:"text-2xl font-bold mr-2",children:"237"}),(0,l.jsx)("span",{className:"text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded",children:"+31%"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"Previous week: 187"})]})]})}),(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4",children:[(0,l.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"Today Activities"}),(0,l.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1",children:(0,l.jsx)("span",{children:"5"})}),(0,l.jsxs)("p",{className:"text-xs",children:["Room",(0,l.jsx)("br",{}),"Available"]})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1",children:(0,l.jsx)("span",{children:"10"})}),(0,l.jsxs)("p",{className:"text-xs",children:["Room",(0,l.jsx)("br",{}),"Blocked"]})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1",children:(0,l.jsx)("span",{children:"15"})}),(0,l.jsx)("p",{className:"text-xs",children:"Guest"})]})]}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"Total Revenue"}),(0,l.jsx)("p",{className:"text-lg font-bold",children:"Rs.35k"})]})]})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6",children:[(0,l.jsxs)(B.Zp,{children:[(0,l.jsxs)(B.aR,{className:"flex flex-row items-center justify-between p-4 pb-2",children:[(0,l.jsx)(B.ZB,{className:"text-base font-medium",children:"Revenue"}),(0,l.jsxs)(J,{children:[(0,l.jsx)(E,{asChild:!0,children:(0,l.jsxs)(S.$,{variant:"ghost",size:"sm",className:"h-8 text-xs",children:["this week ",(0,l.jsx)(t.A,{className:"ml-1 h-3 w-3"})]})}),(0,l.jsxs)(q,{align:"end",children:[(0,l.jsx)(G,{children:"This Month"}),(0,l.jsx)(G,{children:"This Year"})]})]})]}),(0,l.jsx)(B.Wu,{className:"p-4 pt-0",children:(0,l.jsx)("div",{className:"h-[200px] w-full",children:(0,l.jsx)(et.u,{width:"100%",height:"100%",children:(0,l.jsxs)(er.E,{data:[{name:"Sun",value:8},{name:"Mon",value:10},{name:"Tue",value:12},{name:"Wed",value:11},{name:"Thu",value:9},{name:"Fri",value:11},{name:"Sat",value:12}],margin:{top:10,right:10,left:-20,bottom:0},children:[(0,l.jsx)(en.d,{strokeDasharray:"3 3",vertical:!1}),(0,l.jsx)(ed.W,{dataKey:"name",axisLine:!1,tickLine:!1}),(0,l.jsx)(ec.h,{hide:!0}),(0,l.jsx)(ex.m,{content:e=>{let{active:s,payload:a}=e;return s&&a&&a.length?(0,l.jsx)("div",{className:"bg-white p-2 border rounded shadow-sm",children:(0,l.jsx)("p",{className:"text-xs",children:"".concat(a[0].value," K")})}):null}}),(0,l.jsx)(eo.y,{dataKey:"value",fill:"#F59E0B",radius:[4,4,0,0]})]})})})})]}),(0,l.jsxs)(B.Zp,{children:[(0,l.jsxs)(B.aR,{className:"flex flex-row items-center justify-between p-4 pb-2",children:[(0,l.jsx)(B.ZB,{className:"text-base font-medium",children:"Guests"}),(0,l.jsxs)(J,{children:[(0,l.jsx)(E,{asChild:!0,children:(0,l.jsxs)(S.$,{variant:"ghost",size:"sm",className:"h-8 text-xs",children:["this week ",(0,l.jsx)(t.A,{className:"ml-1 h-3 w-3"})]})}),(0,l.jsxs)(q,{align:"end",children:[(0,l.jsx)(G,{children:"This Month"}),(0,l.jsx)(G,{children:"This Year"})]})]})]}),(0,l.jsx)(B.Wu,{className:"p-4 pt-0",children:(0,l.jsx)("div",{className:"h-[200px] w-full",children:(0,l.jsx)(et.u,{width:"100%",height:"100%",children:(0,l.jsxs)(em.b,{data:[{name:"Sun",value:8e3},{name:"Mon",value:1e4},{name:"Tue",value:12e3},{name:"Wed",value:9e3},{name:"Thu",value:6e3},{name:"Fri",value:8e3}],margin:{top:10,right:10,left:-20,bottom:0},children:[(0,l.jsx)(en.d,{strokeDasharray:"3 3",vertical:!1}),(0,l.jsx)(ed.W,{dataKey:"name",axisLine:!1,tickLine:!1}),(0,l.jsx)(ec.h,{hide:!0}),(0,l.jsx)(ex.m,{content:e=>{let{active:s,payload:a}=e;return s&&a&&a.length?(0,l.jsx)("div",{className:"bg-white p-2 border rounded shadow-sm",children:(0,l.jsx)("p",{className:"text-xs",children:"".concat(a[0].value)})}):null}}),(0,l.jsx)(eh.N,{type:"monotone",dataKey:"value",stroke:"#3B82F6",strokeWidth:2,dot:{r:4,fill:"white",stroke:"#3B82F6",strokeWidth:2},activeDot:{r:6},fill:"url(#colorUv)"}),(0,l.jsx)("defs",{children:(0,l.jsxs)("linearGradient",{id:"colorUv",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,l.jsx)("stop",{offset:"5%",stopColor:"#3B82F6",stopOpacity:.2}),(0,l.jsx)("stop",{offset:"95%",stopColor:"#3B82F6",stopOpacity:0})]})}),(0,l.jsx)("area",{type:"monotone",dataKey:"value",stroke:"none",fill:"url(#colorUv)"})]})})})})]}),(0,l.jsxs)(B.Zp,{children:[(0,l.jsxs)(B.aR,{className:"flex flex-row items-center justify-between p-4 pb-2",children:[(0,l.jsx)(B.ZB,{className:"text-base font-medium",children:"Rooms"}),(0,l.jsxs)(J,{children:[(0,l.jsx)(E,{asChild:!0,children:(0,l.jsxs)(S.$,{variant:"ghost",size:"sm",className:"h-8 text-xs",children:["this week ",(0,l.jsx)(t.A,{className:"ml-1 h-3 w-3"})]})}),(0,l.jsxs)(q,{align:"end",children:[(0,l.jsx)(G,{children:"This Month"}),(0,l.jsx)(G,{children:"This Year"})]})]})]}),(0,l.jsxs)(B.Wu,{className:"p-4 pt-0",children:[(0,l.jsx)("div",{className:"text-xs mb-2",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("p",{children:"Total 50 Rooms"}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,l.jsx)("span",{children:"Occupied"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)("span",{className:"h-2 w-2 rounded-full bg-green-500"}),(0,l.jsx)("span",{children:"Booked"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)("span",{className:"h-2 w-2 rounded-full bg-amber-500"}),(0,l.jsx)("span",{children:"Available"})]})]})]})}),(0,l.jsx)("div",{className:"h-[180px] w-full",children:(0,l.jsx)(et.u,{width:"100%",height:"100%",children:(0,l.jsxs)(er.E,{data:[{name:"Sun",occupied:15,booked:10,available:25},{name:"Mon",occupied:20,booked:12,available:18},{name:"Tue",occupied:18,booked:15,available:17},{name:"Wed",occupied:22,booked:10,available:18},{name:"Thu",occupied:20,booked:15,available:15},{name:"Fri",occupied:18,booked:12,available:20},{name:"Sat",occupied:15,booked:10,available:25}],margin:{top:10,right:10,left:-20,bottom:0},children:[(0,l.jsx)(en.d,{strokeDasharray:"3 3",vertical:!1}),(0,l.jsx)(ed.W,{dataKey:"name",axisLine:!1,tickLine:!1}),(0,l.jsx)(ec.h,{hide:!0}),(0,l.jsx)(ex.m,{content:e=>{let{active:s,payload:a}=e;return s&&a&&a.length?(0,l.jsxs)("div",{className:"bg-white p-2 border rounded shadow-sm",children:[(0,l.jsx)("p",{className:"text-xs",children:"Occupied: ".concat(a[0].value)}),(0,l.jsx)("p",{className:"text-xs",children:"Booked: ".concat(a[1].value)}),(0,l.jsx)("p",{className:"text-xs",children:"Available: ".concat(a[2].value)})]}):null}}),(0,l.jsx)(eo.y,{dataKey:"occupied",fill:"#3B82F6",radius:[4,4,0,0]}),(0,l.jsx)(eo.y,{dataKey:"booked",fill:"#10B981",radius:[4,4,0,0]}),(0,l.jsx)(eo.y,{dataKey:"available",fill:"#F59E0B",radius:[4,4,0,0]})]})})})]})]})]}),(0,l.jsxs)(B.Zp,{className:"mb-6",children:[(0,l.jsx)(B.aR,{className:"p-4 pb-0",children:(0,l.jsxs)(B.ZB,{className:"text-base font-medium",children:["Todays Booking ",(0,l.jsx)("span",{className:"text-xs font-normal text-gray-500",children:"(8 Guest today)"})]})}),(0,l.jsx)(B.Wu,{className:"p-4",children:(0,l.jsxs)(W,{defaultValue:"stays",className:"w-full",children:[(0,l.jsxs)(D,{className:"mb-4 border-b w-full justify-start rounded-none bg-transparent p-0",children:[(0,l.jsx)(O,{value:"stays",className:"rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none",onClick:()=>s("stays"),children:"Stays"}),(0,l.jsx)(O,{value:"packages",className:"rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none",onClick:()=>s("packages"),children:"Packages"}),(0,l.jsx)(O,{value:"arrivals",className:"rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none",onClick:()=>s("arrivals"),children:"Arrivals"}),(0,l.jsx)(O,{value:"departure",className:"rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none",onClick:()=>s("departure"),children:"Departure"})]}),(0,l.jsxs)("div",{className:"flex flex-col md:flex-row justify-between mb-4 gap-4",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(r.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)("input",{type:"text",placeholder:"Search guest by name or phone number or booking ID",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full md:w-[400px] text-sm"})]}),(0,l.jsxs)(S.$,{className:"bg-blue-500 hover:bg-blue-600 text-white",children:[(0,l.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Add Booking"]})]}),(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)(el.XI,{children:[(0,l.jsx)(el.A0,{children:(0,l.jsxs)(el.Hj,{children:[(0,l.jsx)(el.nd,{className:"whitespace-nowrap",children:(0,l.jsxs)("div",{className:"flex items-center",children:["NAME ",(0,l.jsx)(t.A,{className:"h-4 w-4 ml-1"})]})}),(0,l.jsx)(el.nd,{className:"whitespace-nowrap",children:"BOOKING ID"}),(0,l.jsx)(el.nd,{className:"whitespace-nowrap",children:"NIGHTS"}),(0,l.jsx)(el.nd,{className:"whitespace-nowrap",children:"ROOM TYPE"}),(0,l.jsx)(el.nd,{className:"whitespace-nowrap",children:"GUESTS"}),(0,l.jsx)(el.nd,{className:"whitespace-nowrap",children:"PAID"}),(0,l.jsx)(el.nd,{className:"whitespace-nowrap",children:"COST"}),(0,l.jsx)(el.nd,{className:"whitespace-nowrap",children:"ACTION"})]})}),(0,l.jsx)(el.BF,{children:[{id:1,name:"Ram Kailash",phone:"9905598912",bookingId:"SDK89635",nights:2,roomType:"1 King Room",guests:2,paid:"rsp.150",cost:"rsp.1500",avatar:"/placeholder.svg?height=32&width=32"},{id:2,name:"Samira Karki",phone:"9815394203",bookingId:"SDK89635",nights:4,roomType:["1 Queen","1 King Room"],guests:5,paid:"paid",cost:"rsp.5500",avatar:"/placeholder.svg?height=32&width=32"},{id:3,name:"Jeevan Rai",phone:"9865328452",bookingId:"SDK89635",nights:1,roomType:["1 Deluxe","1 King Room"],guests:3,paid:"rsp.150",cost:"rsp.2500",avatar:"/placeholder.svg?height=32&width=32"},{id:4,name:"Bindu Sharma",phone:"9845653124",bookingId:"SDK89635",nights:3,roomType:["1 Deluxe","1 King Room"],guests:2,paid:"rsp.150",cost:"rsp.3000",avatar:"/placeholder.svg?height=32&width=32"}].map(e=>(0,l.jsxs)(el.Hj,{children:[(0,l.jsx)(el.nA,{children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsxs)(z,{className:"h-8 w-8 mr-3",children:[(0,l.jsx)(Z,{src:e.avatar,alt:e.name}),(0,l.jsx)(K,{children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.name}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:e.phone})]})]})}),(0,l.jsx)(el.nA,{children:e.bookingId}),(0,l.jsx)(el.nA,{children:e.nights}),(0,l.jsx)(el.nA,{children:Array.isArray(e.roomType)?(0,l.jsx)("div",{children:e.roomType.map((e,s)=>(0,l.jsx)("p",{children:e},s))}):e.roomType}),(0,l.jsxs)(el.nA,{children:[e.guests," Guests"]}),(0,l.jsx)(el.nA,{children:"paid"===e.paid?(0,l.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-600 rounded text-xs",children:"paid"}):e.paid}),(0,l.jsx)(el.nA,{children:e.cost}),(0,l.jsx)(el.nA,{children:(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(S.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,l.jsx)(d.A,{className:"h-4 w-4"})}),(0,l.jsx)(S.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,l.jsx)(c.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),(0,l.jsx)("div",{className:"flex justify-end mt-4",children:(0,l.jsx)(S.$,{variant:"link",className:"text-blue-500 hover:text-blue-600",children:"See other Bookings"})})]})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)(B.Zp,{children:[(0,l.jsx)(B.aR,{className:"p-4 pb-0",children:(0,l.jsx)(B.ZB,{className:"text-base font-medium",children:"Calender"})}),(0,l.jsxs)(B.Wu,{className:"p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsx)(S.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,l.jsx)(x.A,{className:"h-4 w-4"})}),(0,l.jsx)("h3",{className:"text-sm font-medium",children:"August 2023"}),(0,l.jsx)(S.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,l.jsx)(o.A,{className:"h-4 w-4"})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-7 gap-1 text-center text-xs",children:[(0,l.jsx)("div",{className:"py-1 font-medium",children:"SU"}),(0,l.jsx)("div",{className:"py-1 font-medium",children:"MO"}),(0,l.jsx)("div",{className:"py-1 font-medium",children:"TU"}),(0,l.jsx)("div",{className:"py-1 font-medium",children:"WE"}),(0,l.jsx)("div",{className:"py-1 font-medium",children:"TH"}),(0,l.jsx)("div",{className:"py-1 font-medium",children:"FR"}),(0,l.jsx)("div",{className:"py-1 font-medium",children:"SA"}),(0,l.jsx)("div",{className:"py-1 text-gray-400",children:"31"}),(0,l.jsx)("div",{className:"py-1",children:"1"}),(0,l.jsxs)("div",{className:"py-1 relative",children:["2",(0,l.jsx)("span",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"})]}),(0,l.jsx)("div",{className:"py-1",children:"3"}),(0,l.jsx)("div",{className:"py-1",children:"4"}),(0,l.jsx)("div",{className:"py-1",children:"5"}),(0,l.jsx)("div",{className:"py-1",children:"6"}),(0,l.jsx)("div",{className:"py-1",children:"7"}),(0,l.jsx)("div",{className:"py-1",children:"8"}),(0,l.jsxs)("div",{className:"py-1 relative",children:["9",(0,l.jsx)("span",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"})]}),(0,l.jsx)("div",{className:"py-1",children:"10"}),(0,l.jsx)("div",{className:"py-1",children:"11"}),(0,l.jsx)("div",{className:"py-1",children:"12"}),(0,l.jsx)("div",{className:"py-1",children:"13"}),(0,l.jsx)("div",{className:"py-1",children:"14"}),(0,l.jsx)("div",{className:"py-1",children:"15"}),(0,l.jsx)("div",{className:"py-1",children:"16"}),(0,l.jsx)("div",{className:"py-1",children:"17"}),(0,l.jsx)("div",{className:"py-1",children:"18"}),(0,l.jsx)("div",{className:"py-1",children:"19"}),(0,l.jsx)("div",{className:"py-1",children:"20"}),(0,l.jsx)("div",{className:"py-1",children:"21"}),(0,l.jsx)("div",{className:"py-1",children:"22"}),(0,l.jsx)("div",{className:"py-1",children:"23"}),(0,l.jsxs)("div",{className:"py-1 relative",children:["24",(0,l.jsx)("span",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"})]}),(0,l.jsx)("div",{className:"py-1",children:"25"}),(0,l.jsx)("div",{className:"py-1",children:"26"}),(0,l.jsx)("div",{className:"py-1",children:"27"}),(0,l.jsx)("div",{className:"py-1",children:"28"}),(0,l.jsx)("div",{className:"py-1",children:"29"}),(0,l.jsx)("div",{className:"py-1",children:"30"}),(0,l.jsx)("div",{className:"py-1",children:"31"}),(0,l.jsx)("div",{className:"py-1 text-gray-400",children:"1"}),(0,l.jsx)("div",{className:"py-1 text-gray-400",children:"2"}),(0,l.jsx)("div",{className:"py-1 text-gray-400",children:"3"})]}),(0,l.jsxs)("div",{className:"mt-6 border rounded-md p-3",children:[(0,l.jsx)("h4",{className:"text-sm font-medium mb-2",children:"August 02, 2023 Booking Lists"}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mb-3",children:"(3 Bookings)"}),(0,l.jsx)("div",{className:"space-y-3",children:[{date:2,guest:"Carl Larson II",nights:2,guests:2},{date:9,guest:"Mrs. Emmett Morar",nights:2,guests:2},{date:24,guest:"Marjorie Klocko",nights:2,guests:2}].map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsxs)(z,{className:"h-8 w-8 mr-3",children:[(0,l.jsx)(Z,{src:"/placeholder.svg?height=32&width=32",alt:e.guest}),(0,l.jsx)(K,{children:e.guest.split(" ").map(e=>e[0]).join("")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium",children:e.guest}),(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:[e.nights," Nights | ",e.guests," Guests"]})]})]},s))})]})]})]}),(0,l.jsxs)(B.Zp,{children:[(0,l.jsxs)(B.aR,{className:"flex flex-row items-center justify-between p-4 pb-0",children:[(0,l.jsx)(B.ZB,{className:"text-base font-medium",children:"Overall Rating"}),(0,l.jsxs)(J,{children:[(0,l.jsx)(E,{asChild:!0,children:(0,l.jsxs)(S.$,{variant:"ghost",size:"sm",className:"h-8 text-xs",children:["This Week ",(0,l.jsx)(t.A,{className:"ml-1 h-3 w-3"})]})}),(0,l.jsxs)(q,{align:"end",children:[(0,l.jsx)(G,{children:"This Month"}),(0,l.jsx)(G,{children:"This Year"})]})]})]}),(0,l.jsxs)(B.Wu,{className:"p-4",children:[(0,l.jsx)("div",{className:"flex justify-center mb-6",children:(0,l.jsxs)("div",{className:"relative w-48 h-24",children:[(0,l.jsxs)("svg",{viewBox:"0 0 100 50",className:"w-full h-full",children:[(0,l.jsx)("path",{d:"M 0 50 A 50 50 0 0 1 100 50",fill:"none",stroke:"#e5e7eb",strokeWidth:"10"}),(0,l.jsx)("path",{d:"M 0 50 A 50 50 0 0 1 90 50",fill:"none",stroke:"#3b82f6",strokeWidth:"10"})]}),(0,l.jsx)("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-sm font-medium",children:"Rating"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:"4.5/5"}),(0,l.jsx)("span",{className:"text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded",children:"+31%"})]})})]})}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Cleanliness"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(_.k,{value:90,className:"h-2 w-32"}),(0,l.jsx)("span",{className:"text-sm",children:"4.5"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Facilities"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(_.k,{value:90,className:"h-2 w-32"}),(0,l.jsx)("span",{className:"text-sm",children:"4.5"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Location"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(_.k,{value:50,className:"h-2 w-32"}),(0,l.jsx)("span",{className:"text-sm",children:"2.5"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Room Comfort"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(_.k,{value:50,className:"h-2 w-32"}),(0,l.jsx)("span",{className:"text-sm",children:"2.5"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Service"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(_.k,{value:76,className:"h-2 w-32"}),(0,l.jsx)("span",{className:"text-sm",children:"3.8"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm",children:"Value for money"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(_.k,{value:76,className:"h-2 w-32"}),(0,l.jsx)("span",{className:"text-sm",children:"3.8"})]})]})]})]})]})]})]}),"billing"===a&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Billing System"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)(S.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,l.jsx)(m.A,{className:"h-4 w-4"}),"Filter"]}),(0,l.jsxs)(S.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,l.jsx)(h.A,{className:"h-4 w-4"}),"Export"]}),(0,l.jsxs)(S.$,{size:"sm",className:"flex items-center gap-1",children:[(0,l.jsx)(n.A,{className:"h-4 w-4"}),"New Invoice"]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4 flex items-center",children:[(0,l.jsx)("div",{className:"bg-blue-50 p-3 rounded-full mr-4",children:(0,l.jsx)(j.A,{className:"h-6 w-6 text-blue-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"Total Revenue"}),(0,l.jsx)("h3",{className:"text-2xl font-bold",children:"Rs.125,000"}),(0,l.jsx)("p",{className:"text-xs text-green-600",children:"+12% from last month"})]})]})}),(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4 flex items-center",children:[(0,l.jsx)("div",{className:"bg-green-50 p-3 rounded-full mr-4",children:(0,l.jsx)(u.A,{className:"h-6 w-6 text-green-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"Paid Invoices"}),(0,l.jsx)("h3",{className:"text-2xl font-bold",children:"Rs.98,500"}),(0,l.jsx)("p",{className:"text-xs text-green-600",children:"78% of total"})]})]})}),(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4 flex items-center",children:[(0,l.jsx)("div",{className:"bg-amber-50 p-3 rounded-full mr-4",children:(0,l.jsx)(p.A,{className:"h-6 w-6 text-amber-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"Pending Payments"}),(0,l.jsx)("h3",{className:"text-2xl font-bold",children:"Rs.26,500"}),(0,l.jsx)("p",{className:"text-xs text-amber-600",children:"22% of total"})]})]})})]}),(0,l.jsxs)(B.Zp,{className:"mb-6",children:[(0,l.jsx)(B.aR,{className:"p-4 pb-0",children:(0,l.jsx)(B.ZB,{className:"text-base font-medium",children:"Recent Invoices"})}),(0,l.jsx)(B.Wu,{className:"p-4",children:(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)(el.XI,{children:[(0,l.jsx)(el.A0,{children:(0,l.jsxs)(el.Hj,{children:[(0,l.jsx)(el.nd,{children:"Invoice ID"}),(0,l.jsx)(el.nd,{children:"Guest"}),(0,l.jsx)(el.nd,{children:"Date"}),(0,l.jsx)(el.nd,{children:"Amount"}),(0,l.jsx)(el.nd,{children:"Status"}),(0,l.jsx)(el.nd,{className:"text-right",children:"Actions"})]})}),(0,l.jsx)(el.BF,{children:[{id:"INV-2023-001",guest:"Ram Kailash",date:"26 Jul 2023",amount:"rsp.1500",status:"Paid",items:[{description:"Room Charges (2 nights)",amount:"rsp.1200"},{description:"Food & Beverages",amount:"rsp.300"}]},{id:"INV-2023-002",guest:"Samira Karki",date:"25 Jul 2023",amount:"rsp.5500",status:"Paid",items:[{description:"Room Charges (4 nights)",amount:"rsp.4800"},{description:"Food & Beverages",amount:"rsp.700"}]},{id:"INV-2023-003",guest:"Jeevan Rai",date:"24 Jul 2023",amount:"rsp.2500",status:"Pending",items:[{description:"Room Charges (1 night)",amount:"rsp.2000"},{description:"Food & Beverages",amount:"rsp.500"}]}].map(e=>(0,l.jsxs)(el.Hj,{children:[(0,l.jsx)(el.nA,{className:"font-medium",children:e.id}),(0,l.jsx)(el.nA,{children:e.guest}),(0,l.jsx)(el.nA,{children:e.date}),(0,l.jsx)(el.nA,{children:e.amount}),(0,l.jsx)(el.nA,{children:(0,l.jsx)(H,{variant:"Paid"===e.status?"success":"warning",children:e.status})}),(0,l.jsx)(el.nA,{className:"text-right",children:(0,l.jsxs)(J,{children:[(0,l.jsx)(E,{asChild:!0,children:(0,l.jsx)(S.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,l.jsx)(N.A,{className:"h-4 w-4"})})}),(0,l.jsxs)(q,{align:"end",children:[(0,l.jsx)(G,{onClick:()=>{$({title:"Invoice details",description:"Viewing details for invoice ".concat(e.id)})},children:"View Details"}),(0,l.jsxs)(G,{onClick:()=>{$({title:"Invoice printed",description:"Invoice ".concat(e.id," sent to printer")})},children:[(0,l.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Print"]}),(0,l.jsxs)(G,{onClick:()=>{$({title:"Invoice downloaded",description:"Invoice ".concat(e.id," downloaded as PDF")})},children:[(0,l.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Download"]}),(0,l.jsx)(V,{}),(0,l.jsx)(G,{onClick:()=>{$({title:"Payment reminder sent",description:"Reminder sent to ".concat(e.guest)})},children:"Send Reminder"})]})]})})]},e.id))})]})})})]}),(0,l.jsxs)(ee.lG,{children:[(0,l.jsx)(ee.zM,{asChild:!0,children:(0,l.jsx)(S.$,{className:"mb-6",children:"Create New Invoice"})}),(0,l.jsxs)(ee.Cf,{className:"sm:max-w-[600px]",children:[(0,l.jsxs)(ee.c7,{children:[(0,l.jsx)(ee.L3,{children:"Create New Invoice"}),(0,l.jsx)(ee.rr,{children:"Create a new invoice for a guest. Fill in all the required details."})]}),(0,l.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)(X.J,{htmlFor:"guest",className:"text-right",children:"Guest"}),(0,l.jsxs)(Q.l6,{children:[(0,l.jsx)(Q.bq,{className:"col-span-3",children:(0,l.jsx)(Q.yv,{placeholder:"Select guest"})}),(0,l.jsxs)(Q.gC,{children:[(0,l.jsx)(Q.eb,{value:"ram",children:"Ram Kailash"}),(0,l.jsx)(Q.eb,{value:"samira",children:"Samira Karki"}),(0,l.jsx)(Q.eb,{value:"jeevan",children:"Jeevan Rai"}),(0,l.jsx)(Q.eb,{value:"bindu",children:"Bindu Sharma"})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)(X.J,{htmlFor:"room",className:"text-right",children:"Room"}),(0,l.jsxs)(Q.l6,{children:[(0,l.jsx)(Q.bq,{className:"col-span-3",children:(0,l.jsx)(Q.yv,{placeholder:"Select room"})}),(0,l.jsxs)(Q.gC,{children:[(0,l.jsx)(Q.eb,{value:"101",children:"101 - King Room"}),(0,l.jsx)(Q.eb,{value:"102",children:"102 - Queen Room"}),(0,l.jsx)(Q.eb,{value:"201",children:"201 - Deluxe Room"}),(0,l.jsx)(Q.eb,{value:"301",children:"301 - Suite"})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)(X.J,{htmlFor:"date",className:"text-right",children:"Date"}),(0,l.jsx)(Y.p,{id:"date",type:"date",className:"col-span-3"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)(X.J,{htmlFor:"amount",className:"text-right",children:"Amount"}),(0,l.jsx)(Y.p,{id:"amount",type:"number",placeholder:"0.00",className:"col-span-3"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)(X.J,{htmlFor:"description",className:"text-right",children:"Description"}),(0,l.jsx)(ea.T,{id:"description",placeholder:"Invoice description",className:"col-span-3"})]})]}),(0,l.jsx)(ee.Es,{children:(0,l.jsx)(S.$,{type:"submit",onClick:()=>{$({title:"Invoice created",description:"New invoice has been created successfully"})},children:"Create Invoice"})})]})]})]}),"food-delivery"===a&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Food Delivery System"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)(S.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[(0,l.jsx)(m.A,{className:"h-4 w-4"}),"Filter"]}),(0,l.jsxs)(S.$,{size:"sm",className:"flex items-center gap-1",children:[(0,l.jsx)(n.A,{className:"h-4 w-4"}),"New Order"]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4 flex items-center",children:[(0,l.jsx)("div",{className:"bg-blue-50 p-3 rounded-full mr-4",children:(0,l.jsx)(f.A,{className:"h-6 w-6 text-blue-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"Total Orders"}),(0,l.jsx)("h3",{className:"text-2xl font-bold",children:"42"}),(0,l.jsx)("p",{className:"text-xs text-green-600",children:"+8% from yesterday"})]})]})}),(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4 flex items-center",children:[(0,l.jsx)("div",{className:"bg-green-50 p-3 rounded-full mr-4",children:(0,l.jsx)(v.A,{className:"h-6 w-6 text-green-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"Completed"}),(0,l.jsx)("h3",{className:"text-2xl font-bold",children:"35"}),(0,l.jsx)("p",{className:"text-xs text-green-600",children:"83% of total"})]})]})}),(0,l.jsx)(B.Zp,{children:(0,l.jsxs)(B.Wu,{className:"p-4 flex items-center",children:[(0,l.jsx)("div",{className:"bg-amber-50 p-3 rounded-full mr-4",children:(0,l.jsx)(b.A,{className:"h-6 w-6 text-amber-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"In Progress"}),(0,l.jsx)("h3",{className:"text-2xl font-bold",children:"7"}),(0,l.jsx)("p",{className:"text-xs text-amber-600",children:"17% of total"})]})]})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,l.jsx)("div",{className:"md:col-span-2",children:(0,l.jsxs)(B.Zp,{children:[(0,l.jsx)(B.aR,{className:"p-4 pb-0",children:(0,l.jsx)(B.ZB,{className:"text-base font-medium",children:"Active Orders"})}),(0,l.jsx)(B.Wu,{className:"p-4",children:(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)(el.XI,{children:[(0,l.jsx)(el.A0,{children:(0,l.jsxs)(el.Hj,{children:[(0,l.jsx)(el.nd,{children:"Order ID"}),(0,l.jsx)(el.nd,{children:"Guest"}),(0,l.jsx)(el.nd,{children:"Room"}),(0,l.jsx)(el.nd,{children:"Items"}),(0,l.jsx)(el.nd,{children:"Total"}),(0,l.jsx)(el.nd,{children:"Status"}),(0,l.jsx)(el.nd,{children:"Actions"})]})}),(0,l.jsx)(el.BF,{children:[{id:"FO-1234",guest:"Ram Kailash",room:"101",items:["Chicken Curry","Naan Bread","Rice"],total:"rsp.850",status:"Delivered",time:"12:30 PM"},{id:"FO-1235",guest:"Samira Karki",room:"205",items:["Vegetable Pasta","Garlic Bread","Tiramisu"],total:"rsp.1200",status:"Preparing",time:"1:15 PM"},{id:"FO-1236",guest:"Jeevan Rai",room:"310",items:["Club Sandwich","French Fries","Coke"],total:"rsp.650",status:"On the way",time:"1:45 PM"}].map(e=>(0,l.jsxs)(el.Hj,{children:[(0,l.jsx)(el.nA,{className:"font-medium",children:e.id}),(0,l.jsx)(el.nA,{children:e.guest}),(0,l.jsx)(el.nA,{children:e.room}),(0,l.jsx)(el.nA,{children:(0,l.jsx)("div",{className:"flex flex-col",children:e.items.map((e,s)=>(0,l.jsx)("span",{className:"text-xs",children:e},s))})}),(0,l.jsx)(el.nA,{children:e.total}),(0,l.jsx)(el.nA,{children:(0,l.jsx)(H,{variant:"Delivered"===e.status?"success":"Preparing"===e.status?"warning":"default",children:e.status})}),(0,l.jsx)(el.nA,{children:(0,l.jsxs)(J,{children:[(0,l.jsx)(E,{asChild:!0,children:(0,l.jsx)(S.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,l.jsx)(N.A,{className:"h-4 w-4"})})}),(0,l.jsxs)(q,{align:"end",children:[(0,l.jsx)(G,{onClick:()=>{$({title:"Order details",description:"Viewing details for order ".concat(e.id)})},children:"View Details"}),(0,l.jsx)(G,{onClick:()=>{$({title:"Order status updated",description:"Order ".concat(e.id," marked as delivered")})},children:"Mark as Delivered"}),(0,l.jsx)(V,{}),(0,l.jsx)(G,{onClick:()=>{$({title:"Order cancelled",description:"Order ".concat(e.id," has been cancelled"),variant:"destructive"})},children:"Cancel Order"})]})]})})]},e.id))})]})})})]})}),(0,l.jsx)("div",{children:(0,l.jsxs)(B.Zp,{children:[(0,l.jsx)(B.aR,{className:"p-4 pb-0",children:(0,l.jsx)(B.ZB,{className:"text-base font-medium",children:"Order Distribution"})}),(0,l.jsxs)(B.Wu,{className:"p-4",children:[(0,l.jsx)("div",{className:"h-[250px]",children:(0,l.jsx)(et.u,{width:"100%",height:"100%",children:(0,l.jsxs)(ej.r,{children:[(0,l.jsx)(eu.F,{data:U,cx:"50%",cy:"50%",labelLine:!1,outerRadius:80,fill:"#8884d8",dataKey:"value",label:e=>{let{name:s,percent:a}=e;return"".concat(s,": ").concat((100*a).toFixed(0),"%")},children:U.map((e,s)=>(0,l.jsx)(ep.f,{fill:ef[s%ef.length]},"cell-".concat(s)))}),(0,l.jsx)(ex.m,{})]})})}),(0,l.jsx)("div",{className:"flex flex-wrap justify-center gap-4 mt-4",children:U.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-3 h-3 rounded-full mr-1",style:{backgroundColor:ef[s%ef.length]}}),(0,l.jsxs)("span",{className:"text-xs",children:[e.name,": ",e.value]})]},s))})]})]})})]}),(0,l.jsxs)(ee.lG,{children:[(0,l.jsx)(ee.zM,{asChild:!0,children:(0,l.jsx)(S.$,{className:"mb-6",children:"Place New Order"})}),(0,l.jsxs)(ee.Cf,{className:"sm:max-w-[600px]",children:[(0,l.jsxs)(ee.c7,{children:[(0,l.jsx)(ee.L3,{children:"Place New Food Order"}),(0,l.jsx)(ee.rr,{children:"Create a new food order for a guest. Select items from the menu."})]}),(0,l.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)(X.J,{htmlFor:"guest",className:"text-right",children:"Guest"}),(0,l.jsxs)(Q.l6,{children:[(0,l.jsx)(Q.bq,{className:"col-span-3",children:(0,l.jsx)(Q.yv,{placeholder:"Select guest"})}),(0,l.jsxs)(Q.gC,{children:[(0,l.jsx)(Q.eb,{value:"ram",children:"Ram Kailash - Room 101"}),(0,l.jsx)(Q.eb,{value:"samira",children:"Samira Karki - Room 205"}),(0,l.jsx)(Q.eb,{value:"jeevan",children:"Jeevan Rai - Room 310"}),(0,l.jsx)(Q.eb,{value:"bindu",children:"Bindu Sharma - Room 402"})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)(X.J,{className:"text-right",children:"Menu Items"}),(0,l.jsxs)("div",{className:"col-span-3 border rounded-md p-3 space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(es.S,{id:"item1"}),(0,l.jsxs)(X.J,{htmlFor:"item1",className:"flex justify-between w-full",children:[(0,l.jsx)("span",{children:"Chicken Curry"}),(0,l.jsx)("span",{children:"Rs.450"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(es.S,{id:"item2"}),(0,l.jsxs)(X.J,{htmlFor:"item2",className:"flex justify-between w-full",children:[(0,l.jsx)("span",{children:"Vegetable Pasta"}),(0,l.jsx)("span",{children:"Rs.350"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(es.S,{id:"item3"}),(0,l.jsxs)(X.J,{htmlFor:"item3",className:"flex justify-between w-full",children:[(0,l.jsx)("span",{children:"Club Sandwich"}),(0,l.jsx)("span",{children:"Rs.250"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(es.S,{id:"item4"}),(0,l.jsxs)(X.J,{htmlFor:"item4",className:"flex justify-between w-full",children:[(0,l.jsx)("span",{children:"Naan Bread"}),(0,l.jsx)("span",{children:"Rs.50"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(es.S,{id:"item5"}),(0,l.jsxs)(X.J,{htmlFor:"item5",className:"flex justify-between w-full",children:[(0,l.jsx)("span",{children:"Rice"}),(0,l.jsx)("span",{children:"Rs.100"})]})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)(X.J,{htmlFor:"special",className:"text-right",children:"Special Instructions"}),(0,l.jsx)(ea.T,{id:"special",placeholder:"Any special requests",className:"col-span-3"})]})]}),(0,l.jsx)(ee.Es,{children:(0,l.jsx)(S.$,{type:"submit",onClick:()=>{$({title:"Order placed",description:"Food order has been placed successfully"})},children:"Place Order"})})]})]})]}),"log-analysis"===a&&(0,l.jsx)(eg.default,{})," ","surface-data-query"===a&&(0,l.jsx)(eN.default,{})," ","drawing-board"===a&&(0,l.jsx)(ev,{})," ","database-query"===a&&(0,l.jsx)(eb.default,{})," ","dashboard"!==a&&"billing"!==a&&"food-delivery"!==a&&"log-analysis"!==a&&"surface-data-query"!==a&&"drawing-board"!==a&&"database-query"!==a&&(0,l.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,l.jsxs)(B.Zp,{className:"w-full max-w-md",children:[(0,l.jsxs)(B.aR,{children:[(0,l.jsx)(B.ZB,{children:"Coming Soon"}),(0,l.jsx)(B.BT,{children:"This section is under development and will be available soon."})]}),(0,l.jsx)(B.Wu,{children:(0,l.jsxs)("p",{children:["The"," ","check-in-out"===a?"Check In-Out":"rooms"===a?"Rooms":"messages"===a?"Messages":"customer-review"===a?"Customer Review":"Premium"," ","module is currently being built. Please check back later."]})}),(0,l.jsx)(B.wL,{children:(0,l.jsx)(S.$,{onClick:()=>T("dashboard"),children:"Return to Dashboard"})})]})})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[650,739,855,433,127,116,548,394,587,508,991,441,684,358],()=>s(17720)),_N_E=e.O()}]);