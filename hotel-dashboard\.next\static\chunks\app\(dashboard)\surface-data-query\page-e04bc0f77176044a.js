(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[908],{4884:(e,t,r)=>{"use strict";r.d(t,{bL:()=>x,zi:()=>w});var n=r(12115),a=r(85185),s=r(6101),o=r(46081),i=r(5845),d=r(45503),l=r(11275),u=r(63655),c=r(95155),f="Switch",[m,p]=(0,o.A)(f),[b,v]=m(f),g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:o,checked:d,defaultChecked:l,required:f,disabled:m,value:p="on",onCheckedChange:v,form:g,...h}=e,[y,x]=n.useState(null),w=(0,s.s)(t,e=>x(e)),O=n.useRef(!1),k=!y||g||!!y.closest("form"),[E=!1,A]=(0,i.i)({prop:d,defaultProp:l,onChange:v});return(0,c.jsxs)(b,{scope:r,checked:E,disabled:m,children:[(0,c.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":E,"aria-required":f,"data-state":T(E),"data-disabled":m?"":void 0,disabled:m,value:p,...h,ref:w,onClick:(0,a.m)(e.onClick,e=>{A(e=>!e),k&&(O.current=e.isPropagationStopped(),O.current||e.stopPropagation())})}),k&&(0,c.jsx)(N,{control:y,bubbles:!O.current,name:o,value:p,checked:E,required:f,disabled:m,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=f;var h="SwitchThumb",y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,a=v(h,r);return(0,c.jsx)(u.sG.span,{"data-state":T(a.checked),"data-disabled":a.disabled?"":void 0,...n,ref:t})});y.displayName=h;var N=e=>{let{control:t,checked:r,bubbles:a=!0,...s}=e,o=n.useRef(null),i=(0,d.Z)(r),u=(0,l.X)(t);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(i!==r&&t){let n=new Event("click",{bubbles:a});t.call(e,r),e.dispatchEvent(n)}},[i,r,a]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...s,tabIndex:-1,ref:o,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function T(e){return e?"checked":"unchecked"}var x=g,w=y},14503:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f,oR:()=>c});var n=r(12115);let a=0,s=new Map,o=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function u(e){l=i(l,e),d.forEach(e=>{e(l)})}function c(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>u({type:"DISMISS_TOAST",toastId:r});return u({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(l);return n.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},28905:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var n=r(12115),a=r(6101),s=r(52712),o=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[a,o]=n.useState(),d=n.useRef({}),l=n.useRef(e),u=n.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=i(d.current);u.current="mounted"===c?e:"none"},[c]),(0,s.N)(()=>{let t=d.current,r=l.current;if(r!==e){let n=u.current,a=i(t);e?f("MOUNT"):"none"===a||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==a?f("ANIMATION_OUT"):f("UNMOUNT"),l.current=e}},[e,f]),(0,s.N)(()=>{if(a){var e;let t;let r=null!==(e=a.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=i(d.current).includes(e.animationName);if(e.target===a&&n&&(f("ANIMATION_END"),!l.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},s=e=>{e.target===a&&(u.current=i(d.current))};return a.addEventListener("animationstart",s),a.addEventListener("animationcancel",n),a.addEventListener("animationend",n),()=>{r.clearTimeout(t),a.removeEventListener("animationstart",s),a.removeEventListener("animationcancel",n),a.removeEventListener("animationend",n)}}f("ANIMATION_END")},[a,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{e&&(d.current=getComputedStyle(e)),o(e)},[])}}(t),d="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),l=(0,a.s)(o.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,a=n&&"isReactWarning"in n&&n.isReactWarning;return a?e.ref:(a=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(d));return"function"==typeof r||o.isPresent?n.cloneElement(d,{ref:l}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(52596),a=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var n=r(95155),a=r(12115),s=r(40968),o=r(74466),i=r(53999);let d=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.b,{ref:t,className:(0,i.cn)(d(),r),...a})});l.displayName=s.b.displayName},83355:(e,t,r)=>{Promise.resolve().then(r.bind(r,4508))},88524:(e,t,r)=>{"use strict";r.d(t,{A0:()=>i,BF:()=>d,Hj:()=>l,XI:()=>o,nA:()=>c,nd:()=>u});var n=r(95155),a=r(12115),s=r(53999);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,s.cn)("w-full caption-bottom text-sm",r),...a})})});o.displayName="Table";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("thead",{ref:t,className:(0,s.cn)("[&_tr]:border-b",r),...a})});i.displayName="TableHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,s.cn)("[&_tr:last-child]:border-0",r),...a})});d.displayName="TableBody",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,s.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tr",{ref:t,className:(0,s.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});l.displayName="TableRow";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("th",{ref:t,className:(0,s.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...a})});u.displayName="TableHead";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("td",{ref:t,className:(0,s.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...a})});c.displayName="TableCell",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("caption",{ref:t,className:(0,s.cn)("mt-4 text-sm text-muted-foreground",r),...a})}).displayName="TableCaption"},90088:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});var n=r(95155),a=r(12115),s=r(4884),o=r(53999);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.bL,{className:(0,o.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...a,ref:t,children:(0,n.jsx)(s.zi,{className:(0,o.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=s.bL.displayName},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>d});var n=r(95155),a=r(12115),s=r(99708),o=r(74466),i=r(53999);let d=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:r,variant:a,size:o,asChild:l=!1,...u}=e,c=l?s.DX:"button";return(0,n.jsx)(c,{className:(0,i.cn)(d({variant:a,size:o,className:r})),ref:t,...u})});l.displayName="Button"}},e=>{var t=t=>e(e.s=t);e.O(0,[650,739,116,508,441,684,358],()=>t(83355)),_N_E=e.O()}]);