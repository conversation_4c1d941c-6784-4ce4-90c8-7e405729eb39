(()=>{var e={};e.id=908,e.ids=[908],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7071:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\surface-data-query\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\surface-data-query\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15265:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},18936:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24934:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>d});var n=r(60687),s=r(43210),o=r(8730),a=r(24224),i=r(96241);let d=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...a},l)=>{let u=s?o.DX:"button";return(0,n.jsx)(u,{className:(0,i.cn)(d({variant:t,size:r,className:e})),ref:l,...a})});l.displayName="Button"},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39390:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var n=r(60687),s=r(43210),o=r(78148),a=r(24224),i=r(96241);let d=(0,a.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)(o.b,{ref:r,className:(0,i.cn)(d(),e),...t}));l.displayName=o.b.displayName},42902:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});var n=r(60687),s=r(43210),o=r(90270),a=r(96241);let i=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)(o.bL,{className:(0,a.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:r,children:(0,n.jsx)(o.zi,{className:(0,a.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));i.displayName=o.bL.displayName},46059:(e,t,r)=>{"use strict";r.d(t,{C:()=>a});var n=r(43210),s=r(98599),o=r(66156),a=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[s,a]=n.useState(),d=n.useRef({}),l=n.useRef(e),u=n.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=i(d.current);u.current="mounted"===c?e:"none"},[c]),(0,o.N)(()=>{let t=d.current,r=l.current;if(r!==e){let n=u.current,s=i(t);e?p("MOUNT"):"none"===s||t?.display==="none"?p("UNMOUNT"):r&&n!==s?p("ANIMATION_OUT"):p("UNMOUNT"),l.current=e}},[e,p]),(0,o.N)(()=>{if(s){let e;let t=s.ownerDocument.defaultView??window,r=r=>{let n=i(d.current).includes(r.animationName);if(r.target===s&&n&&(p("ANIMATION_END"),!l.current)){let r=s.style.animationFillMode;s.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===s.style.animationFillMode&&(s.style.animationFillMode=r)})}},n=e=>{e.target===s&&(u.current=i(d.current))};return s.addEventListener("animationstart",n),s.addEventListener("animationcancel",r),s.addEventListener("animationend",r),()=>{t.clearTimeout(e),s.removeEventListener("animationstart",n),s.removeEventListener("animationcancel",r),s.removeEventListener("animationend",r)}}p("ANIMATION_END")},[s,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{e&&(d.current=getComputedStyle(e)),a(e)},[])}}(t),d="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),l=(0,s.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(d));return"function"==typeof r||a.isPresent?n.cloneElement(d,{ref:l}):null};function i(e){return e?.animationName||"none"}a.displayName="Presence"},51713:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},53784:()=>{},56049:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var n=r(65239),s=r(48088),o=r(88170),a=r.n(o),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["(dashboard)",{children:["surface-data-query",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7071)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\surface-data-query\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\surface-data-query\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/surface-data-query/page",pathname:"/surface-data-query",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>s});var n=r(37413);r(82704);let s={title:"v0 App",description:"Created with v0",generator:"v0.dev"};function o({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{children:e})})}},59453:(e,t,r)=>{Promise.resolve().then(r.bind(r,7071))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70333:(e,t,r)=>{"use strict";r.d(t,{dj:()=>p,oR:()=>c});var n=r(43210);let s=0,o=new Map,a=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function u(e){l=i(l,e),d.forEach(e=>{e(l)})}function c({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=n.useState(l);return n.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},78013:(e,t,r)=>{Promise.resolve().then(r.bind(r,5147))},82704:()=>{},90270:(e,t,r)=>{"use strict";r.d(t,{bL:()=>w,zi:()=>T});var n=r(43210),s=r(70569),o=r(98599),a=r(11273),i=r(65551),d=r(83721),l=r(18853),u=r(14163),c=r(60687),p="Switch",[f,m]=(0,a.A)(p),[b,h]=f(p),v=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:a,checked:d,defaultChecked:l,required:p,disabled:f,value:m="on",onCheckedChange:h,form:v,...g}=e,[y,w]=n.useState(null),T=(0,o.s)(t,e=>w(e)),P=n.useRef(!1),_=!y||v||!!y.closest("form"),[A=!1,k]=(0,i.i)({prop:d,defaultProp:l,onChange:h});return(0,c.jsxs)(b,{scope:r,checked:A,disabled:f,children:[(0,c.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":p,"data-state":N(A),"data-disabled":f?"":void 0,disabled:f,value:m,...g,ref:T,onClick:(0,s.m)(e.onClick,e=>{k(e=>!e),_&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),_&&(0,c.jsx)(x,{control:y,bubbles:!P.current,name:a,value:m,checked:A,required:p,disabled:f,form:v,style:{transform:"translateX(-100%)"}})]})});v.displayName=p;var g="SwitchThumb",y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,s=h(g,r);return(0,c.jsx)(u.sG.span,{"data-state":N(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:t})});y.displayName=g;var x=e=>{let{control:t,checked:r,bubbles:s=!0,...o}=e,a=n.useRef(null),i=(0,d.Z)(r),u=(0,l.X)(t);return n.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(i!==r&&t){let n=new Event("click",{bubbles:s});t.call(e,r),e.dispatchEvent(n)}},[i,r,s]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...o,tabIndex:-1,ref:a,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function N(e){return e?"checked":"unchecked"}var w=v,T=y},96241:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var n=r(49384),s=r(82348);function o(...e){return(0,s.QP)((0,n.$)(e))}},96752:(e,t,r)=>{"use strict";r.d(t,{A0:()=>i,BF:()=>d,Hj:()=>l,XI:()=>a,nA:()=>c,nd:()=>u});var n=r(60687),s=r(43210),o=r(96241);let a=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:r,className:(0,o.cn)("w-full caption-bottom text-sm",e),...t})}));a.displayName="Table";let i=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("thead",{ref:r,className:(0,o.cn)("[&_tr]:border-b",e),...t}));i.displayName="TableHeader";let d=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("tbody",{ref:r,className:(0,o.cn)("[&_tr:last-child]:border-0",e),...t}));d.displayName="TableBody",s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("tfoot",{ref:r,className:(0,o.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let l=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("tr",{ref:r,className:(0,o.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));l.displayName="TableRow";let u=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("th",{ref:r,className:(0,o.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));u.displayName="TableHead";let c=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("td",{ref:r,className:(0,o.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));c.displayName="TableCell",s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("caption",{ref:r,className:(0,o.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[719,825,569,571,198,147],()=>r(56049));module.exports=n})();