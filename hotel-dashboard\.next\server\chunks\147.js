"use strict";exports.id=147,exports.ids=[147],exports.modules={5147:(e,t,a)=>{a.r(t),a.d(t,{default:()=>$});var s=a(60687),r=a(43210),l=a(15616),i=a(24934),n=a(39390),o=a(42902),d=a(41862);function c({onSearch:e,isLoading:t}){let[a,c]=(0,r.useState)(""),[m,h]=(0,r.useState)(!0);return(0,s.jsxs)("form",{onSubmit:t=>{t.preventDefault(),e(a,m)},className:"p-4 border rounded-lg space-y-4 bg-card text-card-foreground",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(n.J,{htmlFor:"searchTerms",children:"搜索条件 (每行一个SN)"}),(0,s.jsx)(l.T,{id:"searchTerms",value:a,onChange:e=>c(e.target.value),placeholder:"例如: GDDX2.*202506.*",rows:5,disabled:t})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 pt-2",children:[(0,s.jsx)(o.d,{id:"useRegex",checked:m,onCheckedChange:h,disabled:t}),(0,s.jsx)(n.J,{htmlFor:"useRegex",children:"使用正则表达式"})]}),(0,s.jsx)(i.$,{type:"submit",disabled:t,className:"w-full md:w-auto",children:t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),"搜索中..."]}):"搜索"})]})}var m=a(93437),h=a(96752),f=a(68123),x=a(96241);let u=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(f.bL,{ref:r,className:(0,x.cn)("relative overflow-hidden",e),...a,children:[(0,s.jsx)(f.LM,{className:"h-full w-full rounded-[inherit]",children:t}),(0,s.jsx)(p,{}),(0,s.jsx)(f.OK,{})]}));u.displayName=f.bL.displayName;let p=r.forwardRef(({className:e,orientation:t="vertical",...a},r)=>(0,s.jsx)(f.VM,{ref:r,orientation:t,className:(0,x.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...a,children:(0,s.jsx)(f.lr,{className:"relative flex-1 rounded-full bg-border"})}));p.displayName=f.VM.displayName;var j=a(13861),g=a(37826),b=a(43649),w=a(16969),N=a(50004),v=a(13943),y=a(54568);let S=({isOpen:e,onClose:t,surfaceFile:a})=>{let[l,n]=(0,r.useState)(!1),[o,c]=(0,r.useState)(null),[m,h]=(0,r.useState)(null),f=(0,r.useRef)(null);(0,r.useEffect)(()=>{e&&a?x():(h(null),c(null),n(!1),f.current&&f.current.resetTransform())},[e,a]);let x=async()=>{if(a){n(!0),c(null),h(null),f.current&&f.current.resetTransform();try{let e=await fetch("/api/surface-data/preview",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filePath:a.fullPath})});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t?.error||`Error fetching preview: ${e.statusText}`)}let t=await e.json();if(!t.success)throw Error(t.error||"An unknown error occurred in the API.");if(!t.imageData)throw Error("No image data received from API.");h(t.imageData)}catch(e){console.error("Error fetching or processing preview data:",e),c(e instanceof Error?e.message:"An unknown error occurred.")}finally{n(!1)}}};return(0,s.jsx)(g.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(g.Cf,{className:"max-w-4xl h-[700px] flex flex-col",children:[(0,s.jsxs)(g.c7,{children:[(0,s.jsxs)(g.L3,{children:["Point Cloud Preview: ",a?.name||"Loading..."]}),(0,s.jsx)(g.HM,{asChild:!0,children:(0,s.jsx)(i.$,{variant:"ghost",size:"icon",className:"absolute top-4 right-4",children:"\xd7"})})]}),(0,s.jsxs)("div",{className:"flex-grow flex items-center justify-center relative border rounded-md overflow-hidden bg-slate-100",children:[l&&(0,s.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-center bg-background/80 z-20",children:[(0,s.jsx)(d.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"mt-2 text-muted-foreground",children:"Loading preview data..."})]}),o&&!l&&(0,s.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-center bg-destructive/10 p-4 z-20",children:[(0,s.jsx)(b.A,{className:"h-12 w-12 text-destructive"}),(0,s.jsx)("p",{className:"mt-2 text-destructive-foreground font-semibold",children:"Error loading preview"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-destructive-foreground/80 text-center",children:o}),(0,s.jsx)(i.$,{onClick:x,className:"mt-4",children:"Try Again"})]}),!l&&!o&&m&&(0,s.jsx)(y.GT,{ref:f,initialScale:1,minScale:.5,maxScale:10,centerOnInit:!0,limitToBounds:!0,doubleClick:{mode:"reset"},children:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"absolute top-2 right-2 z-10 flex gap-1",children:[(0,s.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>e.zoomIn(),children:(0,s.jsx)(w.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>e.zoomOut(),children:(0,s.jsx)(N.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>e.resetTransform(),children:(0,s.jsx)(v.A,{className:"h-4 w-4"})})]}),(0,s.jsx)(y.WZ,{wrapperStyle:{width:"100%",height:"100%"},contentStyle:{width:"100%",height:"100%"},children:(0,s.jsx)("img",{src:m,alt:`Preview of ${a?.name||"point cloud"}`,className:"w-full h-full object-contain"})})]})}),!l&&!o&&!m&&(0,s.jsx)("div",{className:"text-muted-foreground",children:"No preview available or file not selected."})]})]})})};var k=a(9989);let C=k.Kq,R=k.bL,A=k.l9,T=r.forwardRef(({className:e,sideOffset:t=4,...a},r)=>(0,s.jsx)(k.UC,{ref:r,sideOffset:t,className:(0,x.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}));function z({results:e,isLoading:t,selectedFiles:a,setSelectedFiles:l,onDownloadSelected:n}){let[o,c]=(0,r.useState)(null),[f,x]=(0,r.useState)(!1),p=e=>{c(e),x(!0)},g=(e,t)=>{t?l(t=>[...t,e]):l(t=>t.filter(t=>t.path!==e.path))},b=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][t]},w=e=>e?new Date(1e3*e).toLocaleString():"-";return t&&0===e.length?(0,s.jsxs)("div",{className:"flex justify-center items-center h-40",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-2",children:"正在加载搜索结果..."})]}):t||0!==e.length?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold",children:["搜索结果 (",e.length," 条)"]}),(0,s.jsx)(i.$,{onClick:n,disabled:0===a.length||t,children:t&&a.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),"下载中..."]}):`下载选中 (${a.length})`})]}),(0,s.jsx)(u,{className:"h-[400px] rounded-md border",children:(0,s.jsxs)(h.XI,{children:[(0,s.jsx)(h.A0,{children:(0,s.jsxs)(h.Hj,{children:[(0,s.jsx)(h.nd,{className:"w-[50px]",children:(0,s.jsx)(m.S,{checked:e.length>0&&a.length===e.length,onCheckedChange:t=>{t?l(e):l([])},disabled:0===e.length||t})}),(0,s.jsx)(h.nd,{className:"w-[40%]",children:"文件名"}),(0,s.jsx)(h.nd,{className:"w-[20%]",children:"大小"}),(0,s.jsx)(h.nd,{className:"w-[25%]",children:"修改时间"}),(0,s.jsx)(h.nd,{className:"w-[15%] text-center",children:"预览"})]})}),(0,s.jsx)(h.BF,{children:e.map(e=>(0,s.jsxs)(h.Hj,{children:[(0,s.jsx)(h.nA,{children:(0,s.jsx)(m.S,{checked:a.some(t=>t.path===e.path),onCheckedChange:t=>g(e,!!t),disabled:t})}),(0,s.jsx)(h.nA,{className:"font-medium truncate max-w-xs",title:e.name,children:e.name}),(0,s.jsx)(h.nA,{children:b(e.size)}),(0,s.jsx)(h.nA,{children:w(e.modifyTime)}),(0,s.jsx)(h.nA,{className:"text-center",children:(0,s.jsx)(C,{children:(0,s.jsxs)(R,{children:[(0,s.jsx)(A,{asChild:!0,children:(0,s.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>p(e),"aria-label":`预览 ${e.name}`,disabled:t,children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})}),(0,s.jsx)(T,{children:(0,s.jsx)("p",{children:"预览文件"})})]})})})]},e.path))})]})}),o&&(0,s.jsx)(S,{isOpen:f,onClose:()=>{x(!1),c(null)},surfaceFile:o})]}):(0,s.jsx)("div",{className:"text-center p-4",children:"没有搜索结果。"})}T.displayName=k.UC.displayName;var L=a(70333);function $(){let[e,t]=(0,r.useState)([]),[a,l]=(0,r.useState)(!1),[i,n]=(0,r.useState)(null),[o,d]=(0,r.useState)([]),m=async(e,a)=>{l(!0),n(null),t([]),d([]);try{let s;let r=e.split("\n").map(e=>e.trim()).filter(e=>e.length>0);if(0===r.length){(0,L.oR)({title:"搜索条件不足",description:"请输入至少一个搜索条件。",variant:"destructive"}),l(!1);return}s=a?r.join("|"):r.map(e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")).join("|");let i=new URLSearchParams;i.append("regex",s);let n=await fetch(`/api/ftp/search?${i.toString()}`);if(!n.ok){let e=await n.json();throw Error(e.error||`HTTP error! status: ${n.status}`)}let o=await n.json();t(o.files),0===o.files.length?(0,L.oR)({title:"搜索结果",description:"未找到匹配的文件。"}):o.files.length>=o.limit&&(0,L.oR)({title:"搜索结果提示",description:`搜索结果已达到${o.limit}条上限，可能还有更多结果未显示。请优化搜索条件。`,variant:"default"})}catch(t){let e=t instanceof Error?t.message:"搜索过程中发生未知错误";n(e),(0,L.oR)({title:"搜索错误",description:e,variant:"destructive"})}finally{l(!1)}},h=async()=>{if(0===o.length){(0,L.oR)({title:"下载提示",description:"请至少选择一个文件进行下载。",variant:"default"});return}l(!0),n(null);try{let e=o.map(e=>e.fullPath),t=await fetch("/api/ftp/download",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filePaths:e})});if(!t.ok){let e=await t.json();throw Error(e.error||`HTTP error! status: ${t.status}`)}let a=await t.blob(),s=window.URL.createObjectURL(a),r=document.createElement("a");r.href=s;let l=new Date().toISOString().replace(/[:.]/g,"-");r.download=o.length>1?`surface_data_${l}.zip`:o[0].name,document.body.appendChild(r),r.click(),r.remove(),window.URL.revokeObjectURL(s),(0,L.oR)({title:"下载成功",description:`${o.length} 个文件已开始下载。`}),d([])}catch(t){let e=t instanceof Error?t.message:"下载过程中发生未知错误";n(e),(0,L.oR)({title:"下载错误",description:e,variant:"destructive"})}finally{l(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto p-4 space-y-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"面形数据查询"}),(0,s.jsx)(c,{onSearch:m,isLoading:a}),i&&(0,s.jsxs)("div",{className:"text-red-500",children:["错误: ",i]}),(0,s.jsx)(z,{results:e,isLoading:a,selectedFiles:o,setSelectedFiles:d,onDownloadSelected:h})]})}},15616:(e,t,a)=>{a.d(t,{T:()=>i});var s=a(60687),r=a(43210),l=a(96241);let i=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));i.displayName="Textarea"},37826:(e,t,a)=>{a.d(t,{Cf:()=>f,Es:()=>u,HM:()=>m,L3:()=>p,c7:()=>x,lG:()=>o,rr:()=>j,zM:()=>d});var s=a(60687),r=a(43210),l=a(26134),i=a(11860),n=a(96241);let o=l.bL,d=l.l9,c=l.ZL,m=l.bm,h=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.hJ,{ref:a,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));h.displayName=l.hJ.displayName;let f=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(c,{children:[(0,s.jsx)(h,{}),(0,s.jsxs)(l.UC,{ref:r,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,s.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));f.displayName=l.UC.displayName;let x=({className:e,...t})=>(0,s.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});x.displayName="DialogHeader";let u=({className:e,...t})=>(0,s.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});u.displayName="DialogFooter";let p=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.hE,{ref:a,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));p.displayName=l.hE.displayName;let j=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.VY,{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));j.displayName=l.VY.displayName},93437:(e,t,a)=>{a.d(t,{S:()=>o});var s=a(60687),r=a(43210),l=a(40211),i=a(13964),n=a(96241);let o=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.bL,{ref:a,className:(0,n.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:(0,s.jsx)(l.C1,{className:(0,n.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})}));o.displayName=l.bL.displayName}};