(()=>{var e={};e.id=888,e.ids=[888],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},92787:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>o,serverHooks:()=>d,workAsyncStorage:()=>i,workUnitAsyncStorage:()=>n});var s=r(96559),a=r(48088),u=r(37719),p=r(99685);let o=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/feature/update/route",pathname:"/api/feature/update",filename:"route",bundlePath:"app/api/feature/update/route"},resolvedPagePath:"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\api\\feature\\update\\route.ts",nextConfigOutput:"",userland:p}),{workAsyncStorage:i,workUnitAsyncStorage:n,serverHooks:d}=o;function c(){return(0,u.patchFetch)({workAsyncStorage:i,workUnitAsyncStorage:n})}},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)},99685:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[719],()=>r(92787));module.exports=s})();