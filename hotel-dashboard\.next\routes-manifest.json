{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/database-query", "regex": "^/database\\-query(?:/)?$", "routeKeys": {}, "namedRegex": "^/database\\-query(?:/)?$"}, {"page": "/drawing-board", "regex": "^/drawing\\-board(?:/)?$", "routeKeys": {}, "namedRegex": "^/drawing\\-board(?:/)?$"}, {"page": "/feature-update", "regex": "^/feature\\-update(?:/)?$", "routeKeys": {}, "namedRegex": "^/feature\\-update(?:/)?$"}, {"page": "/log-analysis", "regex": "^/log\\-analysis(?:/)?$", "routeKeys": {}, "namedRegex": "^/log\\-analysis(?:/)?$"}, {"page": "/surface-data-query", "regex": "^/surface\\-data\\-query(?:/)?$", "routeKeys": {}, "namedRegex": "^/surface\\-data\\-query(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}