"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[394],{14503:(e,t,a)=>{a.d(t,{dj:()=>m,oR:()=>u});var s=a(12115);let r=0,l=new Map,n=e=>{if(l.has(e))return;let t=setTimeout(()=>{l.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],c={toasts:[]};function d(e){c=i(c,e),o.forEach(e=>{e(c)})}function u(e){let{...t}=e,a=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:a});return d({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function m(){let[e,t]=s.useState(c);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,t,a)=>{a.d(t,{cn:()=>l});var s=a(52596),r=a(39688);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},82714:(e,t,a)=>{a.d(t,{J:()=>c});var s=a(95155),r=a(12115),l=a(40968),n=a(74466),i=a(53999);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.b,{ref:t,className:(0,i.cn)(o(),a),...r})});c.displayName=l.b.displayName},88482:(e,t,a)=>{a.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>u});var s=a(95155),r=a(12115),l=a(53999);let n=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});n.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...r})});i.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",a),...r})});d.displayName="CardContent";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",a),...r})});u.displayName="CardFooter"},88524:(e,t,a)=>{a.d(t,{A0:()=>i,BF:()=>o,Hj:()=>c,XI:()=>n,nA:()=>u,nd:()=>d});var s=a(95155),r=a(12115),l=a(53999);let n=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",a),...r})})});n.displayName="Table";let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",a),...r})});i.displayName="TableHeader";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",a),...r})});o.displayName="TableBody",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...r})}).displayName="TableFooter";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...r})});c.displayName="TableRow";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...r})});d.displayName="TableHead";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...r})});u.displayName="TableCell",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",a),...r})}).displayName="TableCaption"},89852:(e,t,a)=>{a.d(t,{p:()=>n});var s=a(95155),r=a(12115),l=a(53999);let n=r.forwardRef((e,t)=>{let{className:a,type:r,...n}=e;return(0,s.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...n})});n.displayName="Input"},90088:(e,t,a)=>{a.d(t,{d:()=>i});var s=a(95155),r=a(12115),l=a(4884),n=a(53999);let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...r,ref:t,children:(0,s.jsx)(l.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=l.bL.displayName},93394:(e,t,a)=>{a.r(t),a.d(t,{default:()=>M});var s=a(95155),r=a(12115),l=a(97168),n=a(88482),i=a(88524),o=a(95784),c=a(89852),d=a(82714),u=a(90088),m=a(20547),h=a(53999);let f=m.bL,p=m.l9,x=r.forwardRef((e,t)=>{let{className:a,align:r="center",sideOffset:l=4,...n}=e;return(0,s.jsx)(m.ZL,{children:(0,s.jsx)(m.UC,{ref:t,align:r,sideOffset:l,className:(0,h.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})})});x.displayName=m.UC.displayName;var y=a(42355),g=a(13052),b=a(43900);function j(e){let{className:t,classNames:a,showOutsideDays:r=!0,...n}=e;return(0,s.jsx)(b.hv,{showOutsideDays:r,className:(0,h.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,h.cn)((0,l.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,h.cn)((0,l.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:e=>{let{...t}=e;return(0,s.jsx)(y.A,{className:"h-4 w-4"})},IconRight:e=>{let{...t}=e;return(0,s.jsx)(g.A,{className:"h-4 w-4"})}},...n})}j.displayName="Calendar";var v=a(14503),N=a(91788),w=a(32919),A=a(78749),T=a(92657),S=a(53904),O=a(54213),C=a(1482),E=a(93500),_=a(15448),R=a(69074),k=a(83013);function M(){let[e,t]=(0,r.useState)(null),[a,m]=(0,r.useState)(null),[h,y]=(0,r.useState)(!1),[g,b]=(0,r.useState)([]),[M,L]=(0,r.useState)(!1),[I,F]=(0,r.useState)(""),[q,U]=(0,r.useState)([]),[B,D]=(0,r.useState)(!1),[P,z]=(0,r.useState)([]),[J,$]=(0,r.useState)(1),[W,H]=(0,r.useState)(0),[Z]=(0,r.useState)(100),[V,Y]=(0,r.useState)(!1),[G,K]=(0,r.useState)(!1),[Q,X]=(0,r.useState)(0),[ee,et]=(0,r.useState)(0),[ea,es]=(0,r.useState)(!1),[er,el]=(0,r.useState)(""),[en,ei]=(0,r.useState)(!1),[eo,ec]=(0,r.useState)(!1),ed=async()=>{if(!er.trim()){(0,v.oR)({title:"错误",description:"请输入密码",variant:"destructive"});return}ec(!0);try{let e=await fetch("/api/auth-database",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:er.trim(),action:"verify"})}),t=await e.json();t.success?(es(!0),el(""),(0,v.oR)({title:"验证成功",description:"欢迎使用数据库查询功能"})):(0,v.oR)({title:"验证失败",description:t.message||"密码错误",variant:"destructive"})}catch(e){console.error("密码验证错误:",e),(0,v.oR)({title:"验证错误",description:"网络错误，请重试",variant:"destructive"})}finally{ec(!1)}},eu=(e,t)=>{if(0===t.length){(0,v.oR)({title:"无数据",description:"没有数据可导出。",variant:"destructive"});return}let a=Object.keys(t[0]),s=new Blob([[a.join(","),...t.map(e=>a.map(t=>{let a=String(e[t]||"");return a.includes(",")||a.includes('"')||a.includes("\n")?'"'.concat(a.replace(/"/g,'""'),'"'):a}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a"),l=URL.createObjectURL(s);r.setAttribute("href",l),r.setAttribute("download","".concat(e,"_").concat(new Date().toISOString().slice(0,19).replace(/:/g,"-"),".csv")),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r),(0,v.oR)({title:"导出成功",description:"".concat(e," 已成功导出到CSV文件。")})},em=async e=>{console.log("开始导出数据，表名:",e),K(!0),X(0);try{let t=ep({withCount:!0})||"SELECT COUNT(*) as total FROM ".concat(e,";");console.log("获取导出总行数:",t);let a=await fetch("/api/database-query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:t})}),s=await a.json();console.log("导出COUNT查询响应:",s);let r=0;if(a.ok&&s.data){if(console.log("导出COUNT查询数据结构:",s.data),"object"!=typeof s.data||Array.isArray(s.data))Array.isArray(s.data)&&s.data.length>0&&(r=parseInt(s.data[0].total)||0);else{let e=Object.keys(s.data);if(e.length>0){let t=s.data[e[0]];t&&t.length>0&&(r=parseInt(t[0].total)||0)}}console.log("导出解析的总行数:",r)}else console.error("导出COUNT查询失败:",s);if(0===r){(0,v.oR)({title:"无数据",description:"没有数据可导出。",variant:"destructive"});return}et(r);let l=Math.ceil(r/100),n=[];for(let t=0;t<l;t++){let a=100*t,s=ep({page:t+1})||"SELECT * FROM ".concat(e," LIMIT ").concat(100," OFFSET ").concat(a,";");console.log("导出批次 ".concat(t+1,"/").concat(l,":"),s);let r=await fetch("/api/database-query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:s})}),i=await r.json();if(r.ok&&i.data){let e=Object.values(i.data)[0];e&&Array.isArray(e)&&(0===t&&Object.keys(e[0]||{}),n=n.concat(e))}X(t+1),await new Promise(e=>setTimeout(e,10))}n.length>0?(eu(e,n),(0,v.oR)({title:"导出完成",description:"成功导出 ".concat(n.length," 条记录")})):(0,v.oR)({title:"导出失败",description:"未能获取到数据",variant:"destructive"})}catch(e){console.error("导出错误:",e),(0,v.oR)({title:"导出失败",description:e.message,variant:"destructive"})}finally{K(!1),X(0),et(0)}},eh=async()=>{L(!0);try{let e=["USE gina_db; SHOW TABLES;","SHOW TABLES FROM gina_db;","SELECT name FROM gina_db.sys.tables ORDER BY name;","SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;"];for(let t=0;t<e.length;t++)try{console.log("Trying query ".concat(t+1,": ").concat(e[t]));let a=await fetch("/api/database-query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e[t]})}),s=await a.json();if(a.ok){console.log("Query succeeded:",s);let e=s.data;if(e&&Object.keys(e).length>0){let t=Object.keys(e)[0],a=e[t];if(Array.isArray(a)&&a.length>0){let e=["TABLE_NAME","name","Tables_in_gina_db"],t=[];for(let s of e)if(a[0].hasOwnProperty(s)){t=a.map(e=>String(e[s])).filter(Boolean);break}if(0===t.length&&(t=a.map(e=>{let t=Object.values(e);return t.length>0?String(t[0]):null}).filter(e=>!!e)),t.length>0){b(t),console.log("Found tables:",t);return}}}}else console.log("Query ".concat(t+1," failed:"),s.error)}catch(e){console.log("Query ".concat(t+1," error:"),e);continue}throw Error("所有表列表查询都失败了。请检查数据库连接和权限。")}catch(e){console.error("Error fetching tables:",e),(0,v.oR)({title:"错误",description:"获取表列表失败: "+e.message,variant:"destructive"})}finally{L(!1)}},ef=async e=>{if(e){D(!0);try{let t=await fetch("/api/database-query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:"SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '".concat(e,"' ORDER BY ORDINAL_POSITION;")})}),a=await t.json();if(t.ok){let e=a.data;if(e&&Object.keys(e).length>0){let t=Object.keys(e)[0],a=e[t];if(Array.isArray(a)){let e=a.map(e=>{let t=e.COLUMN_NAME||e.column_name||"",a=(e.DATA_TYPE||e.data_type||"").toLowerCase(),s="string";return a.includes("int")||a.includes("decimal")||a.includes("float")||a.includes("numeric")||a.includes("double")||a.includes("real")||a.includes("money")||a.includes("smallmoney")?s="number":a.includes("date")||a.includes("time")||a.includes("timestamp")?s="date":(a.includes("bit")||a.includes("boolean"))&&(s="boolean"),{name:t,type:a,dataType:s}}).filter((e,t,a)=>t===a.findIndex(t=>t.name===e.name));U(e),console.log("Found columns:",e)}}}else throw Error(a.error||"获取列信息失败。")}catch(e){console.error("Error fetching columns:",e),(0,v.oR)({title:"错误",description:"获取列信息失败: "+e.message,variant:"destructive"})}finally{D(!1)}}};(0,r.useEffect)(()=>{eh()},[]);let ep=function(){let{withCount:e=!1,page:t=1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!I)return"";let a="",s=P.filter(e=>e.enabled&&""!==e.value&&null!==e.value);if(s.length>0&&(a=" WHERE "+s.map(e=>{let t=e.column,a=e.value;switch(e.operator){case"equals":default:return"".concat(t," = '").concat(a,"'");case"contains":case"regex":return"".concat(t," LIKE '%").concat(a,"%'");case"starts_with":return"".concat(t," LIKE '").concat(a,"%'");case"ends_with":return"".concat(t," LIKE '%").concat(a,"'");case"greater_than":return"".concat(t," > ").concat(a);case"less_than":return"".concat(t," < ").concat(a);case"between":if(Array.isArray(a)&&2===a.length)return"".concat(t," BETWEEN ").concat(a[0]," AND ").concat(a[1]);return"".concat(t," = ").concat(a);case"date_range":if(Array.isArray(a)&&2===a.length)return"".concat(t," BETWEEN '").concat(a[0],"' AND '").concat(a[1],"'");return"".concat(t," = '").concat(a,"'")}}).join(" AND ")),e)return"SELECT COUNT(*) as total FROM ".concat(I).concat(a,";");let r=(t-1)*Z;return"SELECT * FROM ".concat(I).concat(a," LIMIT ").concat(Z," OFFSET ").concat(r,";")},ex=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=ep({page:e});y(!0),m(null),t(null);try{let s=ep({withCount:!0});console.log("执行COUNT查询:",s);let r=await fetch("/api/database-query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:s})}),l=await r.json();console.log("COUNT查询响应:",l);let n=0;if(r.ok&&l.data){if(console.log("COUNT查询数据结构:",l.data),"object"!=typeof l.data||Array.isArray(l.data))Array.isArray(l.data)&&l.data.length>0&&(n=parseInt(l.data[0].total)||0);else{let e=Object.values(l.data)[0];e&&e.length>0&&(n=parseInt(e[0].total)||0)}console.log("解析的总行数:",n)}H(n),Y(!0);let i=await fetch("/api/database-query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:a})}),o=await i.json();if(i.ok)t(o.data),$(e);else throw Error(o.error||"查询失败。")}catch(e){m(e.message),(0,v.oR)({title:"错误",description:e.message,variant:"destructive"})}finally{y(!1)}},ey=(e,t)=>{let a=[...P];a[e]={...a[e],...t},z(a)},eg=e=>{z(P.filter((t,a)=>a!==e))},eb=(e,t)=>{if(0===t.length)return(0,s.jsxs)("p",{children:["表 '",e,"' 没有数据行。"]},e);let a=Object.keys(t[0]);return(0,s.jsxs)(n.Zp,{className:"mt-4",children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e,(0,s.jsxs)("span",{className:"text-sm text-gray-500 font-normal",children:["(",t.length," rows)"]})]}),(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>em(e),disabled:G,className:"flex items-center gap-2",children:[(0,s.jsx)(N.A,{className:"h-4 w-4"}),G?"导出中...":"导出全部数据"]})]})}),(0,s.jsxs)(n.Wu,{className:"p-0",children:[G&&(0,s.jsxs)("div",{className:"p-4 border-b bg-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"导出进度"}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[Q," / ",Math.ceil(ee/100)," 批次"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(ee>0?Q/Math.ceil(ee/100)*100:0,"%")}})}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["正在导出 ",ee," 条记录..."]})]}),(0,s.jsx)("div",{className:"overflow-auto max-h-[600px] border rounded-md",children:(0,s.jsxs)(i.XI,{children:[(0,s.jsx)(i.A0,{className:"sticky top-0 bg-white z-10 shadow-sm",children:(0,s.jsx)(i.Hj,{children:a.map(e=>(0,s.jsx)(i.nd,{className:"whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2",style:{minWidth:"120px"},children:e},e))})}),(0,s.jsx)(i.BF,{children:t.map((e,t)=>(0,s.jsx)(i.Hj,{className:"hover:bg-gray-50",children:a.map(a=>(0,s.jsx)(i.nA,{className:"whitespace-nowrap px-4 py-2 text-sm border-b",style:{minWidth:"120px"},title:String(e[a]),children:(0,s.jsx)("div",{className:"max-w-[200px] truncate",children:String(e[a])})},"".concat(t,"-").concat(a)))},t))})]})}),(0,s.jsx)("div",{className:"px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t",children:V?"显示第 ".concat((J-1)*Z+1,"-").concat(Math.min(J*Z,W)," 条，总计 ").concat(W," 条记录"):"总计: ".concat(t.length," 条记录")})]})]},e)};return ea?(0,s.jsxs)("div",{className:"container mx-auto p-4",children:[(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(O.A,{className:"h-5 w-5"}),"数据库查询"]}),(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{es(!1),el(""),t(null),m(null),F(""),z([]),$(1),H(0),Y(!1)},className:"flex items-center gap-2",children:[(0,s.jsx)(w.A,{className:"h-4 w-4"}),"退出"]})]})}),(0,s.jsxs)(n.Wu,{children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium mb-2",children:[(0,s.jsx)(O.A,{className:"inline h-4 w-4 mr-1"}),"快速表选择 (gina_db)"]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(o.l6,{onValueChange:e=>{F(e),z([]),$(1),H(0),Y(!1),t(null),m(null),ef(e)},children:[(0,s.jsx)(o.bq,{className:"w-full",children:(0,s.jsx)(o.yv,{placeholder:M?"加载表中...":"选择要查询的表"})}),(0,s.jsx)(o.gC,{children:g.map(e=>(0,s.jsx)(o.eb,{value:e,children:e},e))})]}),(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eh,disabled:M,className:"flex items-center gap-1",children:[(0,s.jsx)(S.A,{className:"h-4 w-4 ".concat(M?"animate-spin":"")}),"刷新"]})]}),g.length>0&&(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["在 gina_db 数据库中找到 ",g.length," 个表"]})]}),I&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)(d.J,{className:"text-sm font-medium flex items-center gap-2",children:[(0,s.jsx)(C.A,{className:"h-4 w-4"}),I," 的筛选条件"]}),(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{0!==q.length&&z([...P,{column:q[0].name,operator:"equals",value:"",enabled:!0}])},disabled:B||0===q.length,children:"添加筛选"})]}),B&&(0,s.jsx)("div",{className:"text-sm text-gray-500 mb-2",children:"加载列中..."}),P.length>0&&(0,s.jsx)("div",{className:"space-y-3 mb-4",children:P.map((e,t)=>{var a;return(0,s.jsxs)("div",{className:"flex items-center gap-2 p-3 border rounded-lg",children:[(0,s.jsx)(u.d,{checked:e.enabled,onCheckedChange:e=>ey(t,{enabled:e})}),(0,s.jsxs)(o.l6,{value:e.column,onValueChange:e=>ey(t,{column:e}),children:[(0,s.jsx)(o.bq,{className:"w-40",children:(0,s.jsx)(o.yv,{})}),(0,s.jsx)(o.gC,{children:q.map((e,t)=>(0,s.jsx)(o.eb,{value:e.name,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:["string"===e.dataType&&(0,s.jsx)(E.A,{className:"h-3 w-3"}),"number"===e.dataType&&(0,s.jsx)(_.A,{className:"h-3 w-3"}),"date"===e.dataType&&(0,s.jsx)(R.A,{className:"h-3 w-3"}),e.name," (",e.type,")"]})},"".concat(e.name,"-").concat(t)))})]}),(0,s.jsxs)(o.l6,{value:e.operator,onValueChange:e=>ey(t,{operator:e}),children:[(0,s.jsx)(o.bq,{className:"w-32",children:(0,s.jsx)(o.yv,{})}),(0,s.jsx)(o.gC,{children:(()=>{let t=q.find(t=>t.name===e.column);return(null==t?void 0:t.dataType)==="string"?[(0,s.jsx)(o.eb,{value:"equals",children:"等于"},"equals"),(0,s.jsx)(o.eb,{value:"contains",children:"包含"},"contains"),(0,s.jsx)(o.eb,{value:"starts_with",children:"开头是"},"starts_with"),(0,s.jsx)(o.eb,{value:"ends_with",children:"结尾是"},"ends_with"),(0,s.jsx)(o.eb,{value:"regex",children:"正则表达式"},"regex")]:(null==t?void 0:t.dataType)==="number"?[(0,s.jsx)(o.eb,{value:"equals",children:"等于"},"equals"),(0,s.jsx)(o.eb,{value:"greater_than",children:"大于"},"greater_than"),(0,s.jsx)(o.eb,{value:"less_than",children:"小于"},"less_than"),(0,s.jsx)(o.eb,{value:"between",children:"范围"},"between")]:(null==t?void 0:t.dataType)==="date"?[(0,s.jsx)(o.eb,{value:"equals",children:"等于"},"equals"),(0,s.jsx)(o.eb,{value:"date_range",children:"日期范围"},"date_range")]:[(0,s.jsx)(o.eb,{value:"equals",children:"等于"},"equals")]})()})]}),"between"===e.operator||"date_range"===e.operator?(0,s.jsx)("div",{className:"flex items-center gap-1",children:"date_range"===e.operator?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"outline",className:"w-32 justify-start text-left font-normal",children:[(0,s.jsx)(R.A,{className:"mr-2 h-4 w-4"}),Array.isArray(e.value)&&e.value[0]?(0,k.GP)(new Date(e.value[0]),"yyyy-MM-dd"):"开始日期"]})}),(0,s.jsx)(x,{className:"w-auto p-0",children:(0,s.jsx)(j,{mode:"single",selected:Array.isArray(e.value)&&e.value[0]?new Date(e.value[0]):void 0,onSelect:a=>{let s=Array.isArray(e.value)?e.value:["",""];ey(t,{value:[a?(0,k.GP)(a,"yyyy-MM-dd"):"",s[1]]})},initialFocus:!0})})]}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"to"}),(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"outline",className:"w-32 justify-start text-left font-normal",children:[(0,s.jsx)(R.A,{className:"mr-2 h-4 w-4"}),Array.isArray(e.value)&&e.value[1]?(0,k.GP)(new Date(e.value[1]),"yyyy-MM-dd"):"结束日期"]})}),(0,s.jsx)(x,{className:"w-auto p-0",children:(0,s.jsx)(j,{mode:"single",selected:Array.isArray(e.value)&&e.value[1]?new Date(e.value[1]):void 0,onSelect:a=>{let s=Array.isArray(e.value)?e.value:["",""],r=a?(0,k.GP)(a,"yyyy-MM-dd"):"";ey(t,{value:[s[0],r]})},initialFocus:!0})})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.p,{type:"text",placeholder:"最小值",className:"w-24",value:Array.isArray(e.value)?e.value[0]:"",onChange:a=>{let s=Array.isArray(e.value)?e.value:["",""];ey(t,{value:[a.target.value,s[1]]})}}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"到"}),(0,s.jsx)(c.p,{type:"text",placeholder:"最大值",className:"w-24",value:Array.isArray(e.value)?e.value[1]:"",onChange:a=>{ey(t,{value:[(Array.isArray(e.value)?e.value:["",""])[0],a.target.value]})}})]})}):(0,s.jsx)(c.p,{type:(null===(a=q.find(t=>t.name===e.column))||void 0===a?void 0:a.dataType)==="number"?"number":"text",placeholder:"值",className:"flex-1",value:Array.isArray(e.value)?"":e.value,onChange:e=>ey(t,{value:e.target.value})}),(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>eg(t),children:"删除"})]},t)})}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(l.$,{onClick:()=>{$(1),ex(1)},disabled:h,className:"flex-1",children:h?"查询中...":"使用筛选查询"}),(0,s.jsx)(l.$,{variant:"outline",onClick:async()=>{z([]),$(1),y(!0),m(null),t(null);try{let e="SELECT COUNT(*) as total FROM ".concat(I,";");console.log("获取总行数:",e);let a=await fetch("/api/database-query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e})}),s=await a.json();console.log("COUNT查询响应:",s),console.log("COUNT查询响应状态:",a.ok);let r=0;if(a.ok&&s.data){console.log("COUNT查询数据结构:",s.data),console.log("数据类型:",typeof s.data),console.log("是否为数组:",Array.isArray(s.data));try{if("object"!=typeof s.data||Array.isArray(s.data))Array.isArray(s.data)&&s.data.length>0&&(console.log("尝试数组格式解析..."),console.log("第一行数据:",s.data[0]),r=parseInt(s.data[0].total)||0);else{console.log("尝试对象格式解析...");let e=Object.keys(s.data);if(console.log("表名:",e),e.length>0){let t=s.data[e[0]];console.log("第一个表的数据:",t),t&&t.length>0&&(console.log("第一行数据:",t[0]),r=parseInt(t[0].total)||0)}}console.log("解析的总行数:",r)}catch(e){console.error("解析COUNT数据时出错:",e)}}else console.error("COUNT查询失败:",s);H(r),Y(!0);let l="SELECT * FROM ".concat(I," LIMIT 100 OFFSET 0;"),n=await fetch("/api/database-query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:l})}),i=await n.json();if(n.ok)t(i.data),(0,v.oR)({title:"显示全部数据",description:"显示前100行，总计 ".concat(r," 条记录")});else throw Error(i.error||"查询失败。")}catch(e){m(e.message),(0,v.oR)({title:"错误",description:e.message,variant:"destructive"})}finally{y(!1)}},disabled:h||!I,size:"sm",children:"显示全部 (100行)"})]})]}),I&&(0,s.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:[(0,s.jsx)(d.J,{className:"text-xs font-medium text-gray-600 mb-1 block",children:"生成的查询语句:"}),(0,s.jsx)("code",{className:"text-xs text-gray-800 font-mono",children:ep({page:J})||"SELECT * FROM ".concat(I," LIMIT ").concat(Z," OFFSET 0;")})]})]})]}),a&&(0,s.jsxs)(n.Zp,{className:"mt-4 bg-destructive/10",children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{className:"text-destructive",children:"错误"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("p",{className:"text-destructive",children:a})})]}),e&&(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h2",{className:"text-xl font-bold",children:"查询结果"}),V&&W>0&&(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["总计 ",W," 条记录，第 ",J," 页，共 ",Math.ceil(W/Z)," 页"]})]}),Object.keys(e).length>0?Object.entries(e).map(e=>{let[t,a]=e;return eb(t,a)}):(0,s.jsx)("p",{children:"查询执行成功，但没有返回数据。"}),V&&W>Z&&(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mt-4",children:[(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>ex(J-1),disabled:J<=1||h,children:"上一页"}),(0,s.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,Math.ceil(W/Z))},(e,t)=>{let a=Math.max(1,J-2)+t;return a>Math.ceil(W/Z)?null:(0,s.jsx)(l.$,{variant:a===J?"default":"outline",size:"sm",onClick:()=>ex(a),disabled:h,children:a},a)})}),(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>ex(J+1),disabled:J>=Math.ceil(W/Z)||h,children:"下一页"})]})]})]}):(0,s.jsx)("div",{className:"container mx-auto p-6 space-y-6",children:(0,s.jsxs)(n.Zp,{className:"max-w-md mx-auto",children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center gap-2 justify-center",children:[(0,s.jsx)(w.A,{className:"h-5 w-5"}),"数据库访问验证"]})}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-center text-sm text-gray-600 mb-4",children:"请输入密码以访问数据库查询功能"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"password",children:"密码"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(c.p,{id:"password",type:en?"text":"password",value:er,onChange:e=>el(e.target.value),placeholder:"请输入访问密码",onKeyDown:e=>{"Enter"===e.key&&ed()},disabled:eo}),(0,s.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>ei(!en),disabled:eo,children:en?(0,s.jsx)(A.A,{className:"h-4 w-4"}):(0,s.jsx)(T.A,{className:"h-4 w-4"})})]})]}),(0,s.jsx)(l.$,{onClick:ed,disabled:eo||!er.trim(),className:"w-full",children:eo?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S.A,{className:"mr-2 h-4 w-4 animate-spin"}),"验证中..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"验证密码"]})})]})]})})}},95784:(e,t,a)=>{a.d(t,{bq:()=>m,eb:()=>x,gC:()=>p,l6:()=>d,yv:()=>u});var s=a(95155),r=a(12115),l=a(31992),n=a(66474),i=a(47863),o=a(5196),c=a(53999);let d=l.bL;l.YJ;let u=l.WT,m=r.forwardRef((e,t)=>{let{className:a,children:r,...i}=e;return(0,s.jsxs)(l.l9,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...i,children:[r,(0,s.jsx)(l.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.l9.displayName;let h=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.PP,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=l.PP.displayName;let f=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.wn,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})});f.displayName=l.wn.displayName;let p=r.forwardRef((e,t)=>{let{className:a,children:r,position:n="popper",...i}=e;return(0,s.jsx)(l.ZL,{children:(0,s.jsxs)(l.UC,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...i,children:[(0,s.jsx)(h,{}),(0,s.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,s.jsx)(f,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.JU,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...r})}).displayName=l.JU.displayName;let x=r.forwardRef((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsxs)(l.q7,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),(0,s.jsx)(l.p4,{children:r})]})});x.displayName=l.q7.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...r})}).displayName=l.wv.displayName},97168:(e,t,a)=>{a.d(t,{$:()=>c,r:()=>o});var s=a(95155),r=a(12115),l=a(99708),n=a(74466),i=a(53999);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:a,variant:r,size:n,asChild:c=!1,...d}=e,u=c?l.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(o({variant:r,size:n,className:a})),ref:t,...d})});c.displayName="Button"}}]);