using System;
using System.Linq;
using System.Text;

namespace RealTimeInputConversion
{
    /// <summary>
    /// 演示如何在输入框中实时强制将全角字符转换为半角字符。
    /// </summary>
    public class RealTimeConverterDemo
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("--- 模拟实时输入框强制转换 ---");

            // 1. 创建一个模拟文本框实例
            var textBox = new TextBoxSimulator();

            // 2. 订阅 TextChanged 事件，当文本变化时调用我们的转换逻辑
            textBox.TextChanged += OnTextChanged_ForceHalfWidth;

            // 3. 模拟用户输入
            Console.WriteLine("\n场景1: 输入全角字母和数字");
            textBox.SimulateTyping("ＨＥＬＬＯ１２３");

            Console.WriteLine("\n场景2: 输入混合内容");
            textBox.SimulateTyping("你好ＷＯＲＬＤ");

            Console.WriteLine("\n场景3: 输入已是半角的内容 (无转换发生)");
            textBox.SimulateTyping("Hello123");
            
            Console.WriteLine("\n--- 模拟结束 ---");
        }

        /// <summary>
        /// 这是核心事件处理逻辑，可以应用在真实的UI框架中。
        /// 例如，在 WinForms 中，方法签名可以是:
        /// private void textBox1_TextChanged(object sender, EventArgs e)
        /// </summary>
        private static void OnTextChanged_ForceHalfWidth(object sender, EventArgs e)
        {
            // 获取文本框实例
            var textBox = sender as TextBoxSimulator;
            if (textBox == null) return;

            // 记录当前文本和光标位置，这对于保持用户体验至关重要
            string originalText = textBox.Text;
            int cursorPosition = textBox.SelectionStart;

            // 执行转换
            string convertedText = CharWidthUtils.ToHalfWidth(originalText);

            // **关键**: 仅当文本实际发生变化时才更新，以防止无限循环
            if (originalText != convertedText)
            {
                Console.WriteLine($"检测到全角字符。正在转换...");
                Console.WriteLine($"原文: '{originalText}'");
                
                // 更新文本框内容
                textBox.Text = convertedText;
                
                // 恢复光标位置
                textBox.SelectionStart = cursorPosition;

                Console.WriteLine($"转换后: '{textBox.Text}' (光标位置: {textBox.SelectionStart})");
            }
            else
            {
                 Console.WriteLine($"文本 '{originalText}' 无需转换。");
            }
        }
    }

    /// <summary>
    /// 一个模拟UI文本框的类，用于演示。
    /// </summary>
    public class TextBoxSimulator
    {
        private string _text = "";
        
        /// <summary>
        /// 模拟文本框的文本内容。
        /// </summary>
        public string Text
        {
            get => _text;
            set
            {
                // 只有在文本真正改变时才更新并触发事件
                if (_text != value)
                {
                    _text = value;
                    // 注意：在真实UI中，设置Text属性会再次触发TextChanged事件。
                    // 我们的事件处理器中的 if (originalText != convertedText) 检查会处理这个问题。
                }
            }
        }

        /// <summary>
        /// 模拟光标位置。
        /// </summary>
        public int SelectionStart { get; set; }

        /// <summary>
        /// 模拟文本变化事件。
        /// </summary>
        public event EventHandler TextChanged;

        /// <summary>
        /// 模拟用户输入文本。
        /// </summary>
        public void SimulateTyping(string input)
        {
            Console.WriteLine($"\n用户输入: '{input}'");
            this.Text = input;
            this.SelectionStart = input.Length; // 模拟光标在末尾
            
            // 触发事件
            OnTextChanged(EventArgs.Empty);
        }

        protected virtual void OnTextChanged(EventArgs e)
        {
            TextChanged?.Invoke(this, e);
        }
    }

    #region 字符转换工具类 (CharWidthUtils)
    
    /// <summary>
    /// 提供用于检查和转换半角/全角字符的辅助方法。
    /// </summary>
    public static class CharWidthUtils
    {
        /// <summary>
        /// 将字符串中的全角字符（字母、数字、符号）转换为对应的半角字符。
        /// </summary>
        /// <param name="fullWidthStr">包含全角字符的字符串。</param>
        /// <returns>转换后的字符串。</returns>
        public static string ToHalfWidth(string fullWidthStr)
        {
            if (string.IsNullOrEmpty(fullWidthStr))
            {
                return fullWidthStr;
            }

            var sb = new StringBuilder(fullWidthStr.Length);
            foreach (char c in fullWidthStr)
            {
                // 全角空格转半角空格
                if (c == '\u3000')
                {
                    sb.Append('\u0020');
                }
                // 全角字母、数字、符号转半角
                else if (c >= '\uFF01' && c <= '\uFF5E')
                {
                    sb.Append((char)(c - 65248));
                }
                else
                {
                    sb.Append(c); // 其他字符（如中文字符）不变
                }
            }
            return sb.ToString();
        }
    }

    #endregion
}