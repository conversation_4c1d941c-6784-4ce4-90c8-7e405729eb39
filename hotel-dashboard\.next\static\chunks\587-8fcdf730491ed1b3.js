"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[587],{41397:(e,t,o)=>{o.d(t,{k:()=>r});var l=o(95155),n=o(12115),a=o(15312),s=o(53999);let r=n.forwardRef((e,t)=>{let{className:o,value:n,...r}=e;return(0,l.jsx)(a.bL,{ref:t,className:(0,s.cn)("relative h-2 w-full overflow-hidden rounded-full bg-gray-200",o),...r,children:(0,l.jsx)(a.C1,{className:"h-full w-full flex-1 bg-blue-500 transition-all",style:{transform:"translateX(-".concat(100-(n||0),"%)")}})})});r.displayName=a.bL.displayName},49587:(e,t,o)=>{o.r(t),o.d(t,{default:()=>M});var l=o(95155),n=o(12115),a=o(88482),s=o(82714),r=o(97168),i=o(91788),c=o(98239),d=o.n(c),g=o(14503),u=o(91540),f=o(59311),h=o.n(f);async function p(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3,l=Date.now();return new Promise((n,a)=>{let s=()=>{let a=e.innerText.includes("图表加载中..."),r=e.querySelector(".recharts-surface");r&&!a?(console.log("Chart for block ".concat(t," is ready.")),n()):Date.now()-l>o?(console.warn("Timeout waiting for chart ".concat(t," to render. isLoadingTextPresent: ").concat(a,", hasRechartsSurface: ").concat(!!r)),n()):setTimeout(s,200)};s()})}async function m(e,t,o,l){try{console.log("[exportUtils] Starting export for ZIP. Selected block IDs:",o);let n=new(h()),a=o.length,s=0,r=0;for(let t of o){console.log("[exportUtils] Processing block ".concat(t));let o=e.querySelector('[data-block-id="'.concat(t,'"]'));if(!o){console.error("[exportUtils] Block element not found for ID: ".concat(t,". Skipping.")),n.file("error_block-".concat(t,"_not_found.txt"),"DOM element for block ID ".concat(t," was not found.")),s++,r++,l&&l(s/a*100);continue}try{console.log("[exportUtils] Found block element for ".concat(t,". Waiting for chart to be ready...")),await p(o,t),console.log("[exportUtils] Chart for ".concat(t," is considered ready. Generating image..."));let e=await d().toPng(o,{width:o.scrollWidth,height:o.scrollHeight,bgcolor:"#ffffff",style:{border:"none !important",outline:"none !important","box-shadow":"none !important","background-color":"#ffffff !important"},filter:e=>(e instanceof HTMLElement&&(e.style.border="none",e.style.outline="none",e.style.boxShadow="none"),!0),cacheBust:!0});console.log("[exportUtils] Image generated for block ".concat(t,"."));let l=e.split(",")[1];if(!l)throw Error("Failed to extract base64 data for block ".concat(t));let a=atob(l),s=new Uint8Array(a.length);for(let e=0;e<a.length;e++)s[e]=a.charCodeAt(e);n.file("chart_block-".concat(t,".png"),s),console.log("[exportUtils] Added image for block ".concat(t," to zip."))}catch(e){console.error("[exportUtils] Failed to generate or add image for block ".concat(t,":"),e),n.file("error_block-".concat(t,"_img_generation.txt"),"Failed to generate image for block ".concat(t,": ").concat(e instanceof Error?e.message:String(e))),r++}s++,l&&l(s/a*100),console.log("[exportUtils] Finished processing block ".concat(t,". Progress: ").concat(s/a*100,"%"))}if(0===s&&a>0)throw Error("No blocks were processed for export.");if(0===a){(0,g.oR)({title:"没有内容可导出",description:"没有选择任何数据块进行导出。",variant:"default"});return}console.log("[exportUtils] Generating zip file...");let i=await n.generateAsync({type:"blob"});if(console.log("[exportUtils] Zip file generated, size:",i.size),0===i.size)throw Error("Generated zip file is empty. This might happen if all image generations failed.");(0,u.saveAs)(i,"".concat(t,".zip")),console.log("[exportUtils] Zip file saved."),r>0?(0,g.oR)({title:"导出部分完成",description:"".concat(a-r," 个图表已导出，但有 ").concat(r," 个图表导出失败。详情请查看ZIP包内的错误文件。"),variant:"default"}):(0,g.oR)({title:"导出成功",description:"已将 ".concat(a," 个图表导出为压缩包。")})}catch(e){throw console.error("[exportUtils] Error exporting elements as images:",e),(0,g.oR)({title:"导出失败",description:e instanceof Error?e.message:"导出过程中发生错误",variant:"destructive"}),e}}var x=o(41397),k=o(54059),y=o(9428),b=o(53999);let w=n.forwardRef((e,t)=>{let{className:o,...n}=e;return(0,l.jsx)(k.bL,{className:(0,b.cn)("grid gap-2",o),...n,ref:t})});w.displayName=k.bL.displayName;let N=n.forwardRef((e,t)=>{let{className:o,...n}=e;return(0,l.jsx)(k.q7,{ref:t,className:(0,b.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o),...n,children:(0,l.jsx)(k.C1,{className:"flex items-center justify-center",children:(0,l.jsx)(y.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});N.displayName=k.q7.displayName;let j=e=>{let{dataChunks:t,onSelectionChange:o,onStartExport:c}=e;console.log("[LogDisplayArea] Rendering. ProcessedDataChunks count:",t.length);let[d,u]=(0,n.useState)(""),[f,h]=(0,n.useState)([]),p=(0,n.useRef)(null),[k,y]=(0,n.useState)(!1),[b,j]=(0,n.useState)(0),{toast:v}=(0,g.dj)(),L=n.useRef(o);(0,n.useEffect)(()=>{L.current=o},[o]),(0,n.useEffect)(()=>{if(console.log("[LogDisplayArea] useEffect for auto-selection triggered. processedDataChunks count:",t.length,"current selectedBlockId:",d),0===t.length)""!==d&&(console.log("[LogDisplayArea] No data chunks, clearing selectedBlockId."),u(""));else if(d&&""!==d.trim()&&t.some(e=>e.block_id===d))console.log("[LogDisplayArea] Current selection is still valid:",d);else{console.log("[LogDisplayArea] Current selection is invalid or not set. Attempting to find first valid block.");let e=t.find(e=>e.block_id&&"string"==typeof e.block_id&&""!==e.block_id.trim());e?(console.log("[LogDisplayArea] Found first valid block. Setting selectedBlockId to:",e.block_id),u(e.block_id)):""!==d&&(console.warn("[LogDisplayArea] No valid block_id found in any processed chunks. Clearing selectedBlockId."),u(""))}},[t,d]),(0,n.useEffect)(()=>{if(d&&t.length>0){let e=t.filter(e=>e.block_id===d);L.current(e)}else L.current([])},[d,t]);let A=(0,n.useCallback)(e=>{console.log("[LogDisplayArea] handleBlockSelectionChange - START. blockId:",e),u(e),console.log("[LogDisplayArea] handleBlockSelectionChange - END.")},[]),D=(0,n.useCallback)(e=>{console.log("[LogDisplayArea] handleExportSelectionChange - START. blockId:",e),h(t=>{let o=t.includes(e)?t.filter(t=>t!==e):[...t,e];return console.log("[LogDisplayArea] handleExportSelectionChange - New selection:",o),o})},[]),F=(0,n.useCallback)(()=>{console.log("[LogDisplayArea] selectAllForExport - START");let e=t.map(e=>e.block_id);h(e),console.log("[LogDisplayArea] selectAllForExport - END. Selected all IDs:",e)},[t]),S=(0,n.useCallback)(()=>{console.log("[LogDisplayArea] deselectAllForExport - START"),h([]),console.log("[LogDisplayArea] deselectAllForExport - END")},[]),T=async()=>{if(console.log("handleExportAllImages called"),console.log("displayAreaRef:",p.current),console.log("processedDataChunks:",t),console.log("exportBlockIds:",f),!p.current){v({variant:"destructive",title:"错误",description:"显示区域容器未找到。"});return}if(!t||0===t.length){v({variant:"destructive",title:"错误",description:"没有可导出的内容。"});return}if(0===f.length){v({variant:"destructive",title:"错误",description:"请至少选择一个数据块进行导出。"});return}y(!0),j(0);try{let e=document.querySelector(".log-chart-container");if(!e)throw Error("找不到图表容器");let t=e.querySelectorAll("[data-block-id]");t.forEach(e=>{let t=e.getAttribute("data-block-id");t&&f.includes(t)?e.style.display="block":e.style.display="none"}),await new Promise(e=>setTimeout(e,1e3)),await m(e,"log-analysis-summary-".concat(Date.now()),f,e=>{j(e)}),t.forEach(e=>{e.getAttribute("data-block-id")===d?e.style.display="block":e.style.display="none"})}catch(e){console.error("DisplayArea export failed in component:",e),v({variant:"destructive",title:"导出失败",description:e instanceof Error?e.message:"导出过程中发生错误。"})}finally{y(!1),j(0)}},E=t&&t.length>0;return(0,l.jsxs)(a.Zp,{className:"h-[450px] flex flex-col",children:[(0,l.jsxs)(a.aR,{className:"flex flex-row items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(a.ZB,{children:"选择数据块进行分析"}),(0,l.jsx)(a.BT,{children:"从解析的日志文件中选择一个数据块以在图表中显示。"})]}),(0,l.jsx)(r.$,{onClick:T,disabled:k||!E||0===f.length,size:"sm",children:k?"导出中...":(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"导出选中图片"]})})]}),(0,l.jsx)(a.Wu,{className:"flex-1 overflow-hidden p-4",children:(0,l.jsxs)("div",{ref:p,className:"h-full flex flex-col",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)(r.$,{onClick:F,size:"sm",variant:"outline",children:"全选"}),(0,l.jsx)(r.$,{onClick:S,size:"sm",variant:"outline",children:"取消全选"}),(0,l.jsxs)("span",{className:"text-sm text-muted-foreground ml-2",children:["已选择导出: ",f.length," 项"]}),k&&(0,l.jsx)("div",{className:"flex-1 ml-4",children:(0,l.jsx)(x.k,{value:b,className:"h-1"})})]}),(0,l.jsx)("div",{className:"flex-1 overflow-y-auto min-h-0",children:(0,l.jsx)("div",{className:"space-y-2 p-2 border rounded-md",children:t.map(e=>(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)(w,{value:d,onValueChange:A,className:"flex items-center",children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(N,{value:e.block_id,id:"display-".concat(e.block_id)}),(0,l.jsx)(s.J,{htmlFor:"display-".concat(e.block_id),className:"cursor-pointer",children:"显示"})]})}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",id:"export-".concat(e.block_id),checked:f.includes(e.block_id),onChange:()=>D(e.block_id),className:"h-4 w-4 rounded border-gray-300"}),(0,l.jsx)(s.J,{htmlFor:"export-".concat(e.block_id),className:"cursor-pointer",children:"数据块 ".concat(e.block_id," (胶厚: ").concat(e.glue_thickness_values.length,", 准直: ").concat(e.collimation_diff_values.length,")")})]})]},e.block_id))})}),(!t||0===t.length)&&(0,l.jsx)("p",{className:"text-muted-foreground p-2",children:"暂无数据块可供分析或导出。"})]})})]})};var v=o(83540),L=o(6650),A=o(94754),D=o(96025),F=o(16238),S=o(94517),T=o(24026),E=o(21374),U=o(70919);let C=e=>{if(!e)return NaN;try{let t=e.replace(",",".");if(!t.includes("T")&&t.includes(" ")){let e=t.split(" ");e.length>1&&e[0].includes("-")&&e[1].includes(":")&&(t=e[0]+"T"+e.slice(1).join(" "))}let o=new Date(t),l=o.getTime();if(isNaN(l)){let t=e.replace(",",".");l=(o=new Date(t)).getTime()}if(isNaN(l)&&(l=(o=new Date(e)).getTime()),isNaN(l))return console.warn('[LogChartViewHelper] Failed to parse timestamp: "'.concat(e,'"')),NaN;return l}catch(t){return console.error('[LogChartViewHelper] Error parsing timestamp: "'.concat(e,'"'),t),NaN}},R=e=>{let t=[];(e.glue_thickness_values||[]).forEach(e=>{let o=C(e.timestamp);isNaN(o)||t.push({time:o,glueThickness:e.value,collimationDiff:null})}),(e.collimation_diff_values||[]).forEach(e=>{let o=C(e.timestamp);isNaN(o)||t.push({time:o,glueThickness:null,collimationDiff:e.value})}),t.sort((e,t)=>e.time-t.time);let o=[];return t.forEach(e=>{let t=o.find(t=>t.time===e.time);t?(null!==e.glueThickness&&(t.glueThickness=e.glueThickness),null!==e.collimationDiff&&(t.collimationDiff=e.collimationDiff)):o.push({...e})}),o},_=e=>{let{chunk:t,isChartReady:o}=e,{chartDataForThisBlock:a,eventPointsForThisBlock:s,timeDomainForThisBlock:r,glueDomainForThisBlock:i,collimationDomainForThisBlock:c,hasDataForThisBlock:d}=(0,n.useMemo)(()=>{if(!t)return{chartDataForThisBlock:[],eventPointsForThisBlock:[],timeDomainForThisBlock:[Date.now()-36e5,Date.now()],glueDomainForThisBlock:[0,1e3],collimationDomainForThisBlock:[0,.1],hasDataForThisBlock:!1};let e=R(t),o=(t.valve_open_events||[]).map(e=>({time:C(e.timestamp),value:0,label:"打开放气阀 (".concat(e.timestamp.split(" ")[1]||e.timestamp,")")})).filter(e=>!isNaN(e.time));o.sort((e,t)=>e.time-t.time);let l=[Date.now()-36e5,Date.now()];if(e.length>0){let t=e.map(e=>e.time).filter(e=>!isNaN(e));t.length>0&&(l=[Math.min(...t),Math.max(...t)])}l[0]===l[1]&&(l[1]=l[0]+36e5);let n=e.map(e=>e.glueThickness).filter(e=>null!==e&&!isNaN(e)),a=[0,1e3];n.length>0&&(a=[Math.min(...n),Math.max(...n)]),a[0]===a[1]&&(a[1]=a[0]+10);let s=e.map(e=>e.collimationDiff).filter(e=>null!==e&&!isNaN(e)),r=[0,.1];return s.length>0&&(r=[Math.min(...s),Math.max(...s)]),r[0]===r[1]&&(r[1]=r[0]+.01),{chartDataForThisBlock:e,eventPointsForThisBlock:o,timeDomainForThisBlock:l,glueDomainForThisBlock:a,collimationDomainForThisBlock:r,hasDataForThisBlock:e.length>0}},[t]);return d&&o?(0,l.jsx)(v.u,{width:"100%",height:"100%",children:(0,l.jsxs)(L.X,{data:a,margin:{top:20,right:40,left:30,bottom:20},children:[(0,l.jsx)(A.d,{strokeDasharray:"3 3"}),(0,l.jsx)(D.W,{dataKey:"time",domain:r,type:"number",tickFormatter:e=>new Date(e).toLocaleTimeString(),allowDuplicatedCategory:!1}),(0,l.jsx)(F.h,{yAxisId:"glue",orientation:"left",domain:i,type:"number",stroke:"#8884d8",label:{value:"胶厚 (μm)",angle:-90,position:"insideLeft",offset:-5,style:{fill:"#8884d8",textAnchor:"middle"}},tickFormatter:e=>e.toFixed(2),width:70}),(0,l.jsx)(F.h,{yAxisId:"collimation",orientation:"right",domain:c,type:"number",stroke:"#82ca9d",label:{value:"准直差",angle:90,position:"insideRight",offset:-15,style:{fill:"#82ca9d",textAnchor:"middle"}},tickFormatter:e=>e.toFixed(3),width:80}),(0,l.jsx)(S.m,{labelFormatter:e=>new Date(e).toLocaleString(),formatter:(e,t)=>"number"!=typeof e?[e,t]:"glueThickness"===t?[e.toFixed(2)+" μm","胶厚"]:"collimationDiff"===t?[e.toFixed(3),"准直差"]:[e,t],contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",padding:"8px"}}),(0,l.jsx)(T.s,{verticalAlign:"top",height:36,wrapperStyle:{paddingBottom:"10px"}}),(0,l.jsx)(E.N,{yAxisId:"glue",type:"monotone",dataKey:"glueThickness",name:"胶厚",stroke:"#8884d8",strokeWidth:2,dot:{r:2},activeDot:{r:5},isAnimationActive:!1,connectNulls:!0}),(0,l.jsx)(E.N,{yAxisId:"collimation",type:"monotone",dataKey:"collimationDiff",name:"准直差",stroke:"#82ca9d",strokeWidth:2,dot:{r:2},activeDot:{r:5},isAnimationActive:!1,connectNulls:!0}),s.map((e,o)=>(0,l.jsx)(U.e,{x:e.time,stroke:"rgba(255,0,0,0.7)",yAxisId:"glue",strokeDasharray:"4 4",label:{value:e.label,position:"insideTopRight",fill:"rgba(255,0,0,0.7)",fontSize:10}},"event-".concat(t.block_id,"-").concat(o)))]})}):(0,l.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:o?"此数据块无有效图表数据。":"图表加载中..."})},B=function(e){let{dataChunks:t,selectedBlockIds:o,onBlockSelect:s}=e,r=(0,n.useRef)(null),[i,c]=(0,n.useState)(!1),[d,u]=(0,n.useState)(!1),{toast:f}=(0,g.dj)();return(0,n.useEffect)(()=>{u(!1);let e=setTimeout(()=>u(!0),100);return()=>clearTimeout(e)},[o,t]),(0,n.useMemo)(()=>(console.log("LogChartView - hasChartData check:",{dataChunks:t}),t&&Array.isArray(t))?t.length>0:(console.log("LogChartView - dataChunks is invalid:",t),!1),[t]),(0,n.useMemo)(()=>(console.log("LogChartView - chartData calculation:",{dataChunks:t,selectedBlockIds:o}),t&&Array.isArray(t))?t.filter(e=>(console.log("Filtering chunk:",{chunk:e,selectedBlockIds:o}),o.includes(e.block_id))).flatMap(e=>(console.log("Processing chunk for chart data:",e),e.data&&Array.isArray(e.data))?e.data.map(t=>({name:t.name,value:t.value,type:t.type,block_id:e.block_id})):(console.log("Invalid chunk data:",e.data),[])):(console.log("LogChartView - dataChunks is invalid in chartData:",t),[]),[t,o]),(0,l.jsx)("div",{ref:r,className:"space-y-6 log-chart-container",children:t.map(e=>(0,l.jsxs)(a.Zp,{"data-block-id":e.block_id,className:o.includes(e.block_id)?"block":"hidden",children:[(0,l.jsx)(a.aR,{className:"flex flex-row justify-between items-center",children:(0,l.jsxs)(a.ZB,{children:["数据块 ",e.block_id]})}),(0,l.jsx)(a.Wu,{className:"p-0",children:(0,l.jsx)("div",{className:"w-full h-[400px] min-h-[400px]",style:{width:"100%",height:"400px"},children:(0,l.jsx)(_,{chunk:e,isChartReady:d})})})]},e.block_id))})};var P=o(49362),I=o(44134).hp;let W=e=>{let{onProcessingStart:t,onDataProcessed:i,onError:c,disabled:d=!1}=e,[u,f]=(0,n.useState)(null),[h,p]=(0,n.useState)(!1),[m,x]=(0,n.useState)(null),[k,y]=(0,n.useState)("未选择文件"),b=(0,n.useRef)(null),w=(0,n.useRef)(i),N=(0,n.useRef)(c),j=(0,n.useRef)(t);(0,n.useEffect)(()=>{w.current=i},[i]),(0,n.useEffect)(()=>{N.current=c},[c]),(0,n.useEffect)(()=>{j.current=t},[t]);let v=e=>{if(!e||0===e.length){x(null);return}let t=0,o=null,l=null;for(let n of e){if(t+=n.lines_count,n.start_time)try{let e=new Date(n.start_time.replace(",","."));(!o||e<o)&&(o=e)}catch(e){console.warn("无法解析起始时间:",n.start_time,e)}if(n.end_time)try{let e=new Date(n.end_time.replace(",","."));(!l||e>l)&&(l=e)}catch(e){console.warn("无法解析结束时间:",n.end_time,e)}}let n=e=>{if(!e||isNaN(e.getTime()))return null;let t=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0"),l=e.getDate().toString().padStart(2,"0"),n=e.getHours().toString().padStart(2,"0"),a=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(l," ").concat(n,":").concat(a,":").concat(s)};x({totalLines:t,firstLogTime:n(o),lastLogTime:n(l)})};(0,n.useEffect)(()=>{console.log("[LogFileUpload] useEffect[] - START: Component did mount / Worker setup effect running.");{console.log("[LogFileUpload] useEffect[] - Creating new Worker instance.");let e=new Worker(o.tu(new URL(o.p+o.u(269),o.b)),{type:void 0});console.log("[LogFileUpload] useEffect[] - New Worker instance CREATED."),e.onmessage=e=>{console.log("[LogFileUpload] Worker onmessage - RECEIVED:",e.data),p(!1),e.data.error?(console.log("[LogFileUpload] Worker onmessage - Calling onErrorRef.current with error."),N.current(e.data.error),x(null),(0,g.oR)({title:"处理错误",description:e.data.error,variant:"destructive"})):e.data.success&&e.data.allBlocks&&(console.log("[LogFileUpload] Worker onmessage - Calling onDataProcessedRef.current with data."),w.current(e.data.allBlocks),v(e.data.allBlocks),(0,g.oR)({title:"处理完成",description:e.data.message||"日志文件已成功解析。"})),console.log("[LogFileUpload] Worker onmessage - END.")},e.onerror=e=>{console.log("[LogFileUpload] Worker onerror - RECEIVED error:",e),p(!1),N.current(e.message),x(null),(0,g.oR)({title:"Worker 错误",description:e.message,variant:"destructive"}),console.log("[LogFileUpload] Worker onerror - END.")},b.current=e,console.log("[LogFileUpload] useEffect[] - Worker instance assigned to workerRef.current.")}return()=>{console.log("[LogFileUpload] useEffect[] - CLEANUP FUNCTION START: Component will unmount / Worker teardown.");let e=b.current;if(e){console.log("[LogFileUpload] Cleanup: Attempting to terminate worker...",e);try{e.onmessage=null,e.onerror=null,e.terminate(),console.log("[LogFileUpload] Cleanup: Worker terminated successfully.")}catch(e){console.error("[LogFileUpload] Cleanup: Error terminating worker:",e)}b.current=null,console.log("[LogFileUpload] Cleanup: workerRef.current set to null.")}else console.log("[LogFileUpload] Cleanup: No worker instance found in ref to terminate.");console.log("[LogFileUpload] useEffect[] - CLEANUP FUNCTION END.")}},[]);let L=(0,n.useCallback)(async()=>{if(console.log("[LogFileUpload] handleUpload - START."),!u){(0,g.oR)({title:"没有选择文件",description:"请选择一个日志文件进行上传。",variant:"destructive"}),console.log("[LogFileUpload] handleUpload - No file selected, returning.");return}if(console.log("[LogFileUpload] handleUpload - Setting isProcessing to true, calling onProcessingStartRef.current()."),p(!0),j.current(),x(null),(0,g.oR)({title:"开始处理",description:"正在处理文件: ".concat(u.name)}),!b.current){(0,g.oR)({title:"Worker 未初始化",description:"Web Worker 尚未准备好，请稍后再试。",variant:"destructive"}),N.current("Worker not initialized"),p(!1),x(null),console.log("[LogFileUpload] handleUpload - Worker not initialized, returning.");return}console.log("[LogFileUpload] handleUpload - Worker instance exists, proceeding with file processing.");try{let e=await u.arrayBuffer();if(!e||0===e.byteLength){p(!1),N.current("文件为空或读取失败。"),x(null),(0,g.oR)({title:"错误",description:"文件为空或读取失败。",variant:"destructive"});return}let t="",o="UTF-8",l=!1,n=new Uint8Array(e);if(console.log("[LogFileUpload] uInt8ArrayForDetection type:",typeof n,"instanceof Uint8Array:",n instanceof Uint8Array,"length:",n.length),n.length>0&&console.log("[LogFileUpload] uInt8ArrayForDetection sample (first 20 bytes):",n.slice(0,20)),n.length>10)try{console.log("[LogFileUpload] Attempting jschardet.detect...");let e=I.from(n),t=P.detect(e);console.log("[LogFileUpload] jschardet.detect result:",t),t&&t.encoding&&t.confidence>.2?(o=t.encoding.toUpperCase(),console.log("[LogFileUpload] jschardet confidently detected encoding: ".concat(o," with confidence: ").concat(t.confidence)),["GBK","GB2312","GB18030","BIG5"].includes(o)?o="gbk":["UTF-8","ASCII"].includes(o)||(console.warn("[LogFileUpload] Detected encoding ".concat(o," is not directly handled by TextDecoder as is, defaulting to GBK for now.")),o="gbk")):(console.warn("[LogFileUpload] jschardet could not detect encoding with sufficient confidence. Will try fallbacks. Result:",t),l=!0)}catch(e){console.error("[LogFileUpload] Error during jschardet.detect:",e.message,e),l=!0,e.message&&e.message.includes("aBuf.slice(...).split is not a function")}else console.warn("[LogFileUpload] File too small for reliable encoding detection. Will try fallbacks."),l=!0;try{if(l)throw Error("jschardet failed or was not confident, proceeding to fallbacks.");console.log("[LogFileUpload] Attempting to decode with detected/chosen encoding: ".concat(o)),t=new TextDecoder(o.toLowerCase()).decode(e)}catch(l){console.warn("[LogFileUpload] Decoding with ".concat(o," failed (or skipped). Error: ").concat(l.message));try{o="gbk",console.log("[LogFileUpload] Attempting to decode with fallback: ".concat(o)),t=new TextDecoder(o.toLowerCase()).decode(e)}catch(l){console.warn("[LogFileUpload] Decoding with ".concat(o," failed. Error: ").concat(l.message)),o="utf-8",console.log("[LogFileUpload] Attempting to decode with last resort: ".concat(o)),t=new TextDecoder(o.toLowerCase()).decode(e)}}console.log("[LogFileUpload] Successfully decoded file content using encoding:",o),console.log("[LogFileUpload] handleUpload - Posting message to worker with fileContent."),b.current.postMessage(t)}catch(t){console.error("[LogFileUpload] Final catch after all processing attempts in handleUpload:",t),p(!1),x(null);let e=t.message.includes("aBuf.slice(...).split is not a function")?t.message:"文件解码失败: ".concat(t.message,". 请确保文件是文本格式且编码受支持。");N.current(e),(0,g.oR)({title:"解码失败",description:e,variant:"destructive"});return}console.log("[LogFileUpload] handleUpload - END (processing will continue in worker).")},[u]);return(0,l.jsxs)(a.Zp,{className:"w-full max-w-md h-[450px] flex flex-col",children:[(0,l.jsxs)(a.aR,{children:[(0,l.jsx)(a.ZB,{children:"胶合日志文件上传"}),(0,l.jsx)(a.BT,{children:"选择一个日志文件进行分析。"})]}),(0,l.jsxs)(a.Wu,{className:"space-y-6 p-6 flex-grow flex flex-col overflow-y-auto",children:[(0,l.jsxs)("div",{className:"flex flex-col items-start gap-1.5",children:[" ",(0,l.jsx)(s.J,{htmlFor:"hidden-log-file",className:"text-sm font-medium",children:"选择文件"})," ",(0,l.jsxs)("label",{htmlFor:"hidden-log-file",className:"w-full h-10 rounded-md border border-input flex items-center text-sm ring-offset-background ".concat(d?"cursor-not-allowed bg-muted":"cursor-pointer focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"),children:[(0,l.jsx)("span",{className:"bg-primary text-primary-foreground h-full flex items-center justify-center px-4 rounded-l-md whitespace-nowrap ".concat(d?"opacity-50":""),children:"上传文件"}),(0,l.jsx)("span",{className:"flex-grow h-full px-3 py-2 flex items-center text-muted-foreground overflow-hidden",children:(0,l.jsx)("span",{className:"truncate",children:k})})]}),(0,l.jsx)("input",{id:"hidden-log-file",type:"file",className:"sr-only",onChange:e=>{if(e.target.files&&e.target.files[0]){let t=e.target.files[0];f(t),y(t.name),x(null)}else f(null),y("未选择文件"),x(null)},disabled:h||d,accept:".log,.txt,application/octet-stream"})]}),u&&(0,l.jsxs)("div",{className:"space-y-2 text-sm pt-3",children:[(0,l.jsxs)("p",{className:"text-muted-foreground",children:["已选择: ",u.name," (",(u.size/1024).toFixed(2)," KB)"]}),m&&!h&&(0,l.jsxs)("div",{className:"border-t pt-3 mt-3 space-y-1 text-xs text-muted-foreground",children:[(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-semibold text-foreground",children:"总行数:"})," ",null!==m.totalLines?m.totalLines.toLocaleString():"N/A"]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-semibold text-foreground",children:"起始时间:"})," ",m.firstLogTime||"N/A"]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-semibold text-foreground",children:"结束时间:"})," ",m.lastLogTime||"N/A"]})]})]})]}),(0,l.jsx)(a.wL,{children:(0,l.jsx)(r.$,{onClick:L,disabled:!u||h||d,className:"w-full",children:h?(0,l.jsx)(l.Fragment,{children:"处理中..."}):"上传并分析"})})]})};function M(){let[e,t]=(0,n.useState)([]),[o,s]=(0,n.useState)([]),[r,i]=(0,n.useState)(!1),[c,d]=(0,n.useState)(null),[u,f]=(0,n.useState)(!1),[h,p]=(0,n.useState)([]),x=(0,n.useRef)(null),{toast:k}=(0,g.dj)(),y=t=>{console.log("[LogAnalysisPage] handleBlockSelect - START. Received blockId:",t);let o=e.find(e=>e.block_id===t);o&&s([o]),console.log("[LogAnalysisPage] handleBlockSelect - END")},b=(0,n.useMemo)(()=>(console.log("[LogAnalysisPage] Recalculating chartDataForView. Selected blocks count:",o.length),o),[o]);return(0,n.useEffect)(()=>{if(u&&h.length>0&&x.current){let e=h.map(e=>e.block_id);console.log("[LogAnalysisPage] Starting ZIP export for block IDs:",e),m(x.current,"exported_log_charts",e,e=>{console.log("[LogAnalysisPage] Export progress: ".concat(e.toFixed(2),"%"))}).then(()=>{k({title:"导出成功",description:"所有选中的图表已成功导出。"})}).catch(e=>{console.error("[LogAnalysisPage] Export failed:",e),k({title:"导出失败",description:e instanceof Error?e.message:"导出过程中发生错误。",variant:"destructive"})}).finally(()=>{f(!1),p([])})}},[u,h,e]),console.log("[LogAnalysisPage] Rendering. isLoading:",r,"error:",c,"dataChunks count:",e.length,"selectedBlocksForChart count:",o.length,"chartDataForView count:",b.length),(0,l.jsxs)("div",{className:"flex flex-col h-full p-4 gap-4",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"日志分析"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsx)("div",{className:"md:col-span-1",children:(0,l.jsx)(W,{onProcessingStart:()=>{console.log("[LogAnalysisPage] handleProcessingStart - START"),i(!0),d(null),s([]),console.log("[LogAnalysisPage] handleProcessingStart - END")},onDataProcessed:e=>{console.log("[LogAnalysisPage] handleDataProcessed - START. Received data count:",e.length),t(e.map(e=>({...e,data:[]}))),i(!1),k({title:"数据已加载",description:"成功处理了 ".concat(e.length," 个数据块。")}),console.log("[LogAnalysisPage] handleDataProcessed - END")},onError:e=>{console.log("[LogAnalysisPage] handleError - START. Received errorMessage:",e),d(e),i(!1),console.log("[LogAnalysisPage] handleError - END")},disabled:r})}),(0,l.jsx)("div",{className:"md:col-span-2",children:(0,l.jsx)(j,{dataChunks:e,onSelectionChange:e=>{console.log("[LogAnalysisPage] handleBlockSelectionChanged - START. Received selectedBlocks count:",e.length),s(e),console.log("[LogAnalysisPage] handleBlockSelectionChanged - END")},onStartExport:t=>{console.log("[LogAnalysisPage] initiateExportProcess - START. exportIds:",t);let o=e.filter(e=>t.includes(e.block_id));if(0===o.length){k({title:"导出错误",description:"没有找到要导出的数据块。",variant:"destructive"});return}p(o),f(!0)}})})]}),(0,l.jsxs)("div",{className:"flex-grow mt-4",children:[r&&(0,l.jsx)(a.Zp,{className:"h-full flex items-center justify-center",children:(0,l.jsx)(a.Wu,{className:"text-center",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"正在处理文件..."})})}),c&&(0,l.jsx)(a.Zp,{className:"h-full flex items-center justify-center bg-destructive/10",children:(0,l.jsxs)(a.Wu,{className:"text-center",children:[(0,l.jsx)("p",{className:"text-destructive font-semibold",children:"发生错误"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:c})]})}),!r&&!c&&(0,l.jsx)("div",{ref:x,className:"h-full",children:u?(0,l.jsx)(B,{dataChunks:h,selectedBlockIds:h.map(e=>e.block_id),onBlockSelect:y}):b.length>0?(0,l.jsx)(B,{dataChunks:b,selectedBlockIds:b.map(e=>e.block_id),onBlockSelect:y}):(0,l.jsx)(a.Zp,{className:"h-full flex items-center justify-center",children:(0,l.jsx)(a.Wu,{className:"text-center",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:e.length>0?"请从左侧选择数据块以显示图表":"请先上传日志文件"})})})})]})]})}}}]);