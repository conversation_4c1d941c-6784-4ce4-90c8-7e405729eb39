import { NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';

export async function POST(request: Request) {
  let filePath: string | null = null;
  try {
    const body = await request.json();
    const sqlQuery = body.query;

    if (!sqlQuery || typeof sqlQuery !== 'string') {
      return NextResponse.json({ error: 'Query is required and must be a string.' }, { status: 400 });
    }

    // IMPORTANT: Sanitize or validate sqlQuery here if necessary to prevent SQL injection,
    // even though it's passed as an argument. For now, we assume the query is safe.

    const wrapperAppDir = path.resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');
    const wrapperAppPath = path.join(wrapperAppDir, 'WrapperApp.exe');
    
    // Escape quotes and other special characters for the command line
    const escapedQuery = sqlQuery.replace(/"/g, '\\"');
    const command = `"${wrapperAppPath}" "${escapedQuery}" --silent`;

    console.log(`Executing command: ${command} in ${wrapperAppDir}`);

    // Use PowerShell to redirect stderr to null and capture stdout
    const powershellCommand = `& "${wrapperAppPath}" "${escapedQuery}" --silent 2>$null`;

    const { stdout, stderr } = await new Promise<{stdout: string, stderr: string}>((resolve, reject) => {
      const child = spawn('powershell', ['-Command', powershellCommand], {
        cwd: wrapperAppDir,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdoutData = '';
      let stderrData = '';

      child.stdout.on('data', (data) => {
        stdoutData += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderrData += data.toString();
      });

      child.on('close', (code) => {
        if (code !== 0) {
          reject(new Error(`Process exited with code ${code}`));
        } else {
          resolve({ stdout: stdoutData, stderr: stderrData });
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });

    if (stderr) {
      console.error(`C# Wrapper Error: ${stderr}`);
      // Try to parse stdout for a JSON error message first
      try {
        const errorJson = JSON.parse(stdout);
        if (errorJson.error) {
          return NextResponse.json({ error: `Wrapper Error: ${errorJson.error}`, details: stderr }, { status: 500 });
        }
      } catch (e) {
        // If stdout is not a JSON error, return the raw stderr
        return NextResponse.json({ error: 'An error occurred in the C# wrapper.', details: stderr }, { status: 500 });
      }
    }

    // Clean the output by removing WebSocket binary data and keeping only text lines
    const cleanOutput = stdout
      .split('\n')
      .filter(line => {
        // Remove lines that contain binary data (hex dumps)
        if (line.match(/^[0-9a-fA-F]{4,8}\s+[0-9a-fA-F\s]+/)) return false;
        // Remove lines with WebSocket protocol data
        if (line.includes('GET /ws HTTP') || line.includes('WebSocket') || line.includes('Upgrade:')) return false;
        // Remove lines with only binary characters or control characters
        if (line.match(/^[\x00-\x1F\x7F-\xFF\s]*$/)) return false;
        // Keep lines that look like file paths
        if (line.match(/[A-Za-z]:\\.*\.json/)) return true;
        // Keep other text lines that might be useful
        return line.trim().length > 0 && line.match(/^[A-Za-z0-9\\:._\-\s]+$/);
      })
      .join('\n');

    console.log(`Raw stdout length: ${stdout.length}, Cleaned length: ${cleanOutput.length}`);
    console.log(`Cleaned output: ${cleanOutput}`);

    // Try multiple regex patterns to find the file path
    const patterns = [
      /[A-Z]:[\\\/][^<>:"|?*\r\n\x00-\x1F]*\.json/gi,  // Standard Windows path
      /[A-Z]:\\\\[^<>:"|?*\r\n\x00-\x1F]*\.json/gi,    // Double backslash
      /([A-Z]:[\\\/](?:(?![<>:"|?*\r\n\x00-\x1F]).)*\.json)/gi, // More restrictive
      /[A-Z]:[\\\/]Users[\\\/][^<>:"|?*\r\n\x00-\x1F]*\.json/gi, // Specific to Users directory
    ];

    let foundPath = null;
    for (const pattern of patterns) {
      const matches = cleanOutput.match(pattern);
      if (matches && matches.length > 0) {
        console.log(`Pattern ${pattern} found matches: ${matches.join(', ')}`);
        // Take the first valid-looking path
        for (const match of matches) {
          if (match.includes('Temp') && match.endsWith('.json')) {
            foundPath = match;
            break;
          }
        }
        if (foundPath) break;
      }
    }

    if (!foundPath) {
      console.error(`Could not extract file path from stdout. Raw output: ${stdout}`);
      return NextResponse.json({ error: '无法从C#包装器的输出中提取文件路径。' }, { status: 500 });
    }

    filePath = foundPath;
    console.log(`Extracted file path: ${filePath}`);

    if (!filePath) {
      return NextResponse.json({ error: '无法从C#包装器的输出中提取文件路径。' }, { status: 500 });
    }

    if (!fs.existsSync(filePath) || !fs.statSync(filePath).isFile()) {
        console.error(`File not found or is not a file: ${filePath}`);
        return NextResponse.json({ error: 'Temporary data file not found.', path: filePath }, { status: 404 });
    }

    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const result = JSON.parse(fileContent);
    
    return NextResponse.json(result);

  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json({ error: 'An internal server error occurred.', details: error.message }, { status: 500 });
  } finally {
    if (filePath && fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`Successfully deleted temporary file: ${filePath}`);
      } catch (unlinkError) {
        console.error(`Failed to delete temporary file: ${filePath}`, unlinkError);
      }
    }
  }
}