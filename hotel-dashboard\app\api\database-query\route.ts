import { NextResponse } from 'next/server';
import { exec } from 'child_process';
import path from 'path';
import util from 'util';
import fs from 'fs';

// Promisify exec for async/await usage
const execPromise = util.promisify(exec);

export async function POST(request: Request) {
  let filePath: string | null = null;
  try {
    const body = await request.json();
    const sqlQuery = body.query;

    if (!sqlQuery || typeof sqlQuery !== 'string') {
      return NextResponse.json({ error: 'Query is required and must be a string.' }, { status: 400 });
    }

    // IMPORTANT: Sanitize or validate sqlQuery here if necessary to prevent SQL injection,
    // even though it's passed as an argument. For now, we assume the query is safe.

    const wrapperAppDir = path.resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');
    const wrapperAppPath = path.join(wrapperAppDir, 'WrapperApp.exe');
    
    // Escape quotes and other special characters for the command line
    const escapedQuery = sqlQuery.replace(/"/g, '\\"');
    const command = `"${wrapperAppPath}" "${escapedQuery}"`;

    console.log(`Executing command: ${command} in ${wrapperAppDir}`);

    const { stdout, stderr } = await execPromise(command, { cwd: wrapperAppDir });

    if (stderr) {
      console.error(`C# Wrapper Error: ${stderr}`);
      // Try to parse stdout for a JSON error message first
      try {
        const errorJson = JSON.parse(stdout);
        if (errorJson.error) {
          return NextResponse.json({ error: `Wrapper Error: ${errorJson.error}`, details: stderr }, { status: 500 });
        }
      } catch (e) {
        // If stdout is not a JSON error, return the raw stderr
        return NextResponse.json({ error: 'An error occurred in the C# wrapper.', details: stderr }, { status: 500 });
      }
    }

    // New logic to extract file path from stdout using regex
    const match = stdout.match(/[A-Z]:\\\\[^:]+\.json/i);

    if (!match || !match[0]) {
      console.error(`Could not extract file path from stdout. Raw output: ${stdout}`);
      return NextResponse.json({ error: '无法从C#包装器的输出中提取文件路径。' }, { status: 500 });
    }

    const extractedPath = match[0];
    filePath = extractedPath; // Assign to the outer scope variable for the finally block

    if (!fs.existsSync(extractedPath) || !fs.statSync(extractedPath).isFile()) {
        console.error(`File not found or is not a file: ${extractedPath}`);
        return NextResponse.json({ error: 'Temporary data file not found.', path: extractedPath }, { status: 404 });
    }

    const fileContent = fs.readFileSync(extractedPath, 'utf-8');
    const result = JSON.parse(fileContent);
    
    return NextResponse.json(result);

  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json({ error: 'An internal server error occurred.', details: error.message }, { status: 500 });
  } finally {
    if (filePath && fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`Successfully deleted temporary file: ${filePath}`);
      } catch (unlinkError) {
        console.error(`Failed to delete temporary file: ${filePath}`, unlinkError);
      }
    }
  }
}