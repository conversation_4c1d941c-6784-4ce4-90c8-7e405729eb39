import { NextResponse } from 'next/server';
import { exec } from 'child_process';
import path from 'path';
import util from 'util';
import fs from 'fs';

// Promisify exec for async/await usage
const execPromise = util.promisify(exec);

export async function POST(request: Request) {
  let filePath: string | null = null;
  try {
    const body = await request.json();
    const sqlQuery = body.query;

    if (!sqlQuery || typeof sqlQuery !== 'string') {
      return NextResponse.json({ error: 'Query is required and must be a string.' }, { status: 400 });
    }

    // IMPORTANT: Sanitize or validate sqlQuery here if necessary to prevent SQL injection,
    // even though it's passed as an argument. For now, we assume the query is safe.

    const wrapperAppDir = path.resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');
    const wrapperAppPath = path.join(wrapperAppDir, 'WrapperApp.exe');
    
    // Escape quotes and other special characters for the command line
    const escapedQuery = sqlQuery.replace(/"/g, '\\"');
    const command = `"${wrapperAppPath}" "${escapedQuery}"`;

    console.log(`Executing command: ${command} in ${wrapperAppDir}`);

    const { stdout, stderr } = await execPromise(command, { cwd: wrapperAppDir });

    if (stderr) {
      console.error(`C# Wrapper Error: ${stderr}`);
      // Try to parse stdout for a JSON error message first
      try {
        const errorJson = JSON.parse(stdout);
        if (errorJson.error) {
          return NextResponse.json({ error: `Wrapper Error: ${errorJson.error}`, details: stderr }, { status: 500 });
        }
      } catch (e) {
        // If stdout is not a JSON error, return the raw stderr
        return NextResponse.json({ error: 'An error occurred in the C# wrapper.', details: stderr }, { status: 500 });
      }
    }

    // New logic to extract file path from stdout using regex
    // Look for Windows file paths (C:\...\filename.json) in the output
    // The path might be mixed with other output, so we need a more robust regex
    console.log(`Raw stdout length: ${stdout.length}`);
    console.log(`First 500 chars of stdout: ${stdout.substring(0, 500)}`);
    console.log(`Last 500 chars of stdout: ${stdout.substring(Math.max(0, stdout.length - 500))}`);

    // Try multiple regex patterns to find the file path
    const patterns = [
      /[A-Z]:[\\\/][^<>:"|?*\r\n\x00-\x1F]*\.json/gi,  // Standard Windows path
      /[A-Z]:\\\\[^<>:"|?*\r\n\x00-\x1F]*\.json/gi,    // Double backslash
      /([A-Z]:[\\\/](?:(?![<>:"|?*\r\n\x00-\x1F]).)*\.json)/gi, // More restrictive
      /[A-Z]:[\\\/]Users[\\\/][^<>:"|?*\r\n\x00-\x1F]*\.json/gi, // Specific to Users directory
    ];

    let foundPath = null;
    for (const pattern of patterns) {
      const matches = stdout.match(pattern);
      if (matches && matches.length > 0) {
        console.log(`Pattern ${pattern} found matches: ${matches.join(', ')}`);
        // Take the first valid-looking path
        for (const match of matches) {
          if (match.includes('Temp') && match.endsWith('.json')) {
            foundPath = match;
            break;
          }
        }
        if (foundPath) break;
      }
    }

    if (!foundPath) {
      console.error(`Could not extract file path from stdout. Raw output: ${stdout}`);
      return NextResponse.json({ error: '无法从C#包装器的输出中提取文件路径。' }, { status: 500 });
    }

    filePath = foundPath;
    console.log(`Extracted file path: ${filePath}`);

    if (!filePath) {
      return NextResponse.json({ error: '无法从C#包装器的输出中提取文件路径。' }, { status: 500 });
    }

    if (!fs.existsSync(filePath) || !fs.statSync(filePath).isFile()) {
        console.error(`File not found or is not a file: ${filePath}`);
        return NextResponse.json({ error: 'Temporary data file not found.', path: filePath }, { status: 404 });
    }

    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const result = JSON.parse(fileContent);
    
    return NextResponse.json(result);

  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json({ error: 'An internal server error occurred.', details: error.message }, { status: 500 });
  } finally {
    if (filePath && fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`Successfully deleted temporary file: ${filePath}`);
      } catch (unlinkError) {
        console.error(`Failed to delete temporary file: ${filePath}`, unlinkError);
      }
    }
  }
}